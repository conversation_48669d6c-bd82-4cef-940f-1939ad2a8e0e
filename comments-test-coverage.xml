<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="1" tests="325" time="11.427" timestamp="2025-05-20T20:13:54.581889+02:00" hostname="Egors-Laptop.local"><testcase classname="tests.unit.auth.cognito_test.TestResult" name="test_result_success" time="0.057" /><testcase classname="tests.unit.auth.cognito_test.TestResult" name="test_result_failure" time="0.020" /><testcase classname="tests.unit.auth.cognito_test.TestAsyncCacheDecorator" name="test_call_with_cache_hit" time="0.023" /><testcase classname="tests.unit.auth.cognito_test.TestAsyncCacheDecorator" name="test_call_with_cache_miss" time="0.024" /><testcase classname="tests.unit.auth.cognito_test.TestAsyncCacheDecorator" name="test_call_with_redis_exception" time="0.026" /><testcase classname="tests.unit.auth.cognito_test.TestAsyncRedisCache" name="test_cache_method" time="0.022" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_issuer" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_cognito_jwks_success" time="0.025" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_cognito_jwks_http_error" time="0.226" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_cognito_jwks_general_exception" time="0.056" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_use_keys_with_cache_hit" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_use_keys_with_cache_miss_then_hit" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_use_keys_with_invalidate_exception" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_validate_jwt_success" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_validate_jwt_invalid_keys" time="0.022" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_validate_jwt_jwt_error" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_validate_jwt_jws_error" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_validate_jwt_invalid_token_use" time="0.023" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_validate_jwt_invalid_audience" time="0.026" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_email_from_token_id_token" time="0.023" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_email_from_token_access_token" time="0.029" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_get_email_from_token_fallback_to_sub" time="0.041" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_authenticate_success" time="0.028" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_authenticate_email_not_found" time="0.022" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_authenticate_email_not_verified" time="0.022" /><testcase classname="tests.unit.auth.cognito_test.TestCognitoAuthenticator" name="test_authenticate_invalid_token" time="0.022" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_get_issuer" time="0.020" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_verify_user_success" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_verify_user_not_found" time="0.022" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_authenticate_success" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_authenticate_multiple_tokens" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_authenticate_email_not_found" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestSoleraAuthenticator" name="test_authenticate_rejected_status" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestParticipantAndProviderAuthenticators" name="test_participant_authenticator_init" time="0.021" /><testcase classname="tests.unit.auth.cognito_test.TestParticipantAndProviderAuthenticators" name="test_provider_authenticator_init" time="0.021" /><testcase classname="tests.unit.graphql_api.admin.inputs_test" name="test_admin_input" time="0.021" /><testcase classname="tests.unit.graphql_api.admin.inputs_test" name="test_admins_input" time="0.020" /><testcase classname="tests.unit.graphql_api.admin.inputs_test" name="test_cohort_input" time="0.021" /><testcase classname="tests.unit.graphql_api.admin.queries.get_classes_creators_test" name="test_get_classes_creators_with_errors" time="0.053" /><testcase classname="tests.unit.graphql_api.admin.queries.get_classes_creators_test" name="test_get_classes_creators_success" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_check_join_availability[test_date0]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_check_join_availability[test_date1]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_get_class_status_1" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_get_class_status_2" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_get_class_status_3" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_get_class_status_4[1]" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_get_class_status_4[0]" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.helpers_test" name="test_get_class_status_5" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.mocks_test" name="test_presenter" time="0.020" /><testcase classname="tests.unit.graphql_api.classes.mocks_test" name="test_upcoming_classes" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_create_booking" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_handle_booked_status_when_already_booked" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_handle_booked_status_when_canceled" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_session_not_found" time="0.036" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_not_found_session_full" time="0.029" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_not_found_create_booking" time="0.027" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_not_found_not_booked" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_found_to_booked" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_found_already_attended" time="0.024" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_found_update_status" time="0.025" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_status_by_participant_id_booking_found_same_status" time="0.024" /><testcase classname="tests.unit.graphql_api.classes.mutations.update_booking_status_test" name="test_update_booking_status" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_get_requested_page" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_map_response[booked-False-True-availableToJoin]" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_map_response[attended-False-True-availableToJoin]" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_map_response[attended-True-False-past]" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_map_response[watched_recording-True-False-past]" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_map_response[booked-False-False-None]" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_get_one_hour_ago" time="0.020" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_handle_booking_status[booking_status0-1]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_handle_booking_status[booking_status1-1]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_handle_booking_status[booking_status2-1]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_handle_booking_status[booking_status3-2]" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_handle_booking_status[booking_status4-2]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_no_filters" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_search_filter" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_topic_filter" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_health_coach_filter" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_recording_filter" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_booking_status_filter" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_invalid_booking_status" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_date_filters" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_build_base_query_with_time_of_day_filter" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.bookings_by_participant_test" name="test_get_bookings" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.intro_sessions_test" name="test_get_intro_sessions_cohort_not_found" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.intro_sessions_test" name="test_get_intro_sessions_no_webinars" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.intro_sessions_test" name="test_get_intro_sessions_with_sessions_in_range" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.intro_sessions_test" name="test_get_intro_sessions_multiple_webinars" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_dates_query_filter" time="0.020" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_start_date[None-cohort_start0-expected0]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_start_date[filter_start_date1-cohort_start1-expected1]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_start_date[filter_start_date2-cohort_start2-expected2]" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_build_base_query_no_cohort_member" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_build_base_query_with_cohort_no_intro_booked" time="0.024" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_build_base_query_with_cohort_and_intro_booked" time="0.070" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_build_base_query_with_filters" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_requested_page" time="0.022" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_map_response" time="0.021" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_classes_with_intro_booking" time="0.023" /><testcase classname="tests.unit.graphql_api.classes.queries.upcoming_classes_test" name="test_get_classes_no_intro_booking_no_results_retry" time="0.023" /><testcase classname="tests.unit.graphql_api.content_material.queries.get_favorite_content_test" name="test_get_favorite_content_with_no_program_error" time="0.025" /><testcase classname="tests.unit.graphql_api.content_material.queries.get_favorite_content_test" name="test_get_favorite_content_default_response" time="0.025" /><testcase classname="tests.unit.graphql_api.enums_test" name="test_order_direction_enum" time="0.020" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_basic" time="0.021" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_with_defaults" time="0.023" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_with_aliases" time="0.023" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_with_custom_getter" time="0.022" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_with_async_getter" time="0.021" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_many" time="0.023" /><testcase classname="tests.unit.graphql_api.mixins_test" name="test_marshal_many_with_defaults" time="0.021" /><testcase classname="tests.unit.graphql_api.participant.utils_test" name="test_parse_withings_connection_status_false_scenarios[test_status0]" time="0.021" /><testcase classname="tests.unit.graphql_api.participant.utils_test" name="test_parse_withings_connection_status_false_scenarios[test_status1]" time="0.021" /><testcase classname="tests.unit.graphql_api.participant.utils_test" name="test_parse_withings_connection_status_true_scenario" time="0.021" /><testcase classname="tests.unit.graphql_api.program.filter_activities_test" name="test_filter_activities_empty_list" time="0.021" /><testcase classname="tests.unit.graphql_api.program.filter_activities_test" name="test_filter_activities_matching_activities" time="0.021" /><testcase classname="tests.unit.graphql_api.program.filter_activities_test" name="test_filter_activities_non_matching_type" time="0.021" /><testcase classname="tests.unit.graphql_api.program.filter_activities_test" name="test_filter_activities_non_matching_device" time="0.021" /><testcase classname="tests.unit.graphql_api.program.filter_activities_test" name="test_filter_activities_non_matching_date" time="0.022" /><testcase classname="tests.unit.graphql_api.program.filter_activities_test" name="test_filter_activities_string_activity_type" time="0.021" /><testcase classname="tests.unit.graphql_api.program.find_closest_section_test" name="test_find_closest_section_empty_list" time="0.021" /><testcase classname="tests.unit.graphql_api.program.find_closest_section_test" name="test_find_closest_section_single_section" time="0.021" /><testcase classname="tests.unit.graphql_api.program.find_closest_section_test" name="test_find_closest_section_multiple_sections" time="0.022" /><testcase classname="tests.unit.graphql_api.program.find_closest_section_test" name="test_find_closest_section_future_dates" time="0.024" /><testcase classname="tests.unit.graphql_api.program.find_closest_section_test" name="test_find_closest_section_past_dates" time="0.022" /><testcase classname="tests.unit.graphql_api.program.find_closest_section_test" name="test_find_closest_section_same_time" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_personal_success_input" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_update_input" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_section_personal_success_input" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_section_curriculum_input" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_section_coaching_call_input" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_section_video_type_input" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_section_recipe_type_input" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_weight_type_input" time="0.040" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_food_type_input" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_activity_type_input" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_quiz_type_input" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_personal_success" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_curriculum" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_coaching_call" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_video_type" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_recipe_type" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_weight_type" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_food_type" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_activity_type" time="0.021" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_quiz_type" time="0.020" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_section_input_to_dict_no_type" time="0.022" /><testcase classname="tests.unit.graphql_api.program.inputs_test" name="test_program_module_input" time="0.020" /><testcase classname="tests.unit.graphql_api.program.mutations.add_chat_activity_test" name="test_add_chat_activity_success" time="0.023" /><testcase classname="tests.unit.graphql_api.program.mutations.add_chat_activity_test" name="test_add_chat_activity_failure" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_duplicate_error" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_not_found" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_registration_failed" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_already_enrolled" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_deleted_same_key" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_deleted_different_key_success" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_deleted_different_key_no_cohort" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.land_participant_test" name="test_land_participant_success" time="0.021" /><testcase classname="tests.unit.graphql_api.program.mutations.make_progress_test" name="test_make_progress_no_cohort" time="0.004" /><testcase classname="tests.unit.graphql_api.program.mutations.make_progress_test" name="test_make_progress_live_session" time="0.005" /><testcase classname="tests.unit.graphql_api.program.mutations.make_progress_test" name="test_make_progress_new_activity" time="0.005" /><testcase classname="tests.unit.graphql_api.program.mutations.make_progress_test" name="test_make_progress_existing_weight_activity" time="0.005" /><testcase classname="tests.unit.graphql_api.program.queries.get_module_progress_additional_test" name="test_get_module_progress_participant_not_found" time="0.035" /><testcase classname="tests.unit.graphql_api.program.queries.get_module_progress_additional_test" name="test_get_module_progress_no_cohort" time="0.034" /><testcase classname="tests.unit.graphql_api.program.queries.get_module_progress_additional_test" name="test_get_module_progress_no_sections" time="0.028" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_courses_test" name="test_get_participant_program_courses_success" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_courses_test" name="test_get_participant_program_courses_empty_list" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_module_test" name="test_get_participant_program_module_with_cohort_member_not_found" time="0.024" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_module_test" name="test_get_participant_program_module_with_program_module_not_found" time="0.024" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_module_test" name="test_get_participant_program_module_success[test_today0-True]" time="0.023" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_module_test" name="test_get_participant_program_module_success[test_today1-False]" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_modules_test" name="test_get_participant_program_modules_with_cohort_member_not_found" time="0.023" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_modules_test" name="test_get_participant_program_modules_success" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_modules_test" name="test_get_participant_program_modules_no_current_module" time="0.023" /><testcase classname="tests.unit.graphql_api.program.queries.get_participant_program_modules_test" name="test_get_participant_program_modules_empty_list" time="0.021" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_render_module_with_sections_filtering_logic" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_render_module_with_sections_different_activity_types" time="0.023" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_process_program_module" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_get_activity_type_value_enum" time="0.020" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_get_activity_type_value_string" time="0.020" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_get_activity_type_value_invalid" time="0.021" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_filter_activities" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_filter_activities_empty" time="0.026" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_map_to_progress_data_type_completed" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_map_to_progress_data_type_not_completed" time="0.021" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_map_to_progress_data_type_group_no_url" time="0.021" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_calculate_weight_trend_no_activities" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_calculate_weight_trend_single_activity" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_calculate_weight_trend_multiple_activities" time="0.022" /><testcase classname="tests.unit.graphql_api.program.queries_functions_test" name="test_calculate_weight_trend_with_previous_activity" time="0.023" /><testcase classname="tests.unit.graphql_api.program.services_test" name="test_make_progress_weight_type" time="0.022" /><testcase classname="tests.unit.graphql_api.program.services_test" name="test_make_progress_group_type" time="0.021" /><testcase classname="tests.unit.graphql_api.program.services_test" name="test_make_progress_other_type" time="0.022" /><testcase classname="tests.unit.graphql_api.program.services_test" name="test_make_progress_with_custom_date" time="0.021" /><testcase classname="tests.unit.helper.timeconvert_test" name="test_from_utc_to_pst_with_naive_datetime" time="0.021" /><testcase classname="tests.unit.helper.timeconvert_test" name="test_from_utc_to_pst_with_aware_datetime" time="0.021" /><testcase classname="tests.unit.helper.timeconvert_test" name="test_from_utc_to_pst_with_dst" time="0.021" /><testcase classname="tests.unit.helper.timeconvert_test" name="test_from_utc_to_pst_with_none" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_get_cognito_client" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_get_secret_hash" time="0.023" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_create_user_success" time="0.022" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_create_user_user_exists" time="0.022" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_create_user_other_error" time="0.022" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_add_user_to_group_success" time="0.022" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_add_user_to_group_resource_not_found" time="0.023" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_add_user_to_group_other_error" time="0.022" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_set_user_password" time="0.023" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_get_user_success" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_get_user_not_found" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_get_user_other_error" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_forgot_password" time="0.022" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_confirm_sign_up" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_disable_user" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_delete_user" time="0.021" /><testcase classname="tests.unit.integrations.cognito_test" name="test_admin_enable_user" time="0.021" /><testcase classname="tests.unit.routers.devices_test" name="test_get_connection_status_with_errors" time="0.035" /><testcase classname="tests.unit.routers.devices_test" name="test_get_connection_status_with_success[test_status0]" time="0.025" /><testcase classname="tests.unit.routers.devices_test" name="test_get_connection_status_with_success[test_status1]" time="0.024" /><testcase classname="tests.unit.routers.devices_test" name="test_get_connection_status_with_success[test_status2]" time="0.024" /><testcase classname="tests.unit.routers.devices_test" name="test_set_user_weight_status_success" time="0.021"><skipped type="pytest.skip" message="Requires more complex mocking">/Users/<USER>/PycharmProjects/participant-api/tests/unit/routers/devices_test.py:93: Requires more complex mocking</skipped></testcase><testcase classname="tests.unit.routers.devices_test" name="test_set_user_weight_status_exception" time="0.043" /><testcase classname="tests.unit.routers.devices_test" name="test_get_code_healthy_device" time="0.026" /><testcase classname="tests.unit.routers.devices_test" name="test_get_code_not_connected_device" time="0.025" /><testcase classname="tests.unit.routers.devices_test" name="test_get_code_unhealthy_device" time="0.034" /><testcase classname="tests.unit.routers.devices_test" name="test_get_codes" time="0.024" /><testcase classname="tests.unit.routers.devices_test" name="test_get_codes_with_site" time="0.025" /><testcase classname="tests.unit.routers.devices_test" name="test_add_user_success" time="0.029" /><testcase classname="tests.unit.routers.devices_test" name="test_add_user_not_found" time="0.025" /><testcase classname="tests.unit.routers.devices_test" name="test_sync_success" time="0.026" /><testcase classname="tests.unit.routers.devices_test" name="test_sync_user_not_found" time="0.025" /><testcase classname="tests.unit.routers.devices_test" name="test_disconnect_success" time="0.026" /><testcase classname="tests.unit.routers.devices_test" name="test_disconnect_not_disconnected" time="0.026" /><testcase classname="tests.unit.routers.devices_test" name="test_disconnect_user_not_found" time="0.040" /><testcase classname="tests.unit.routers.devices_test" name="test_disconnect_exception" time="0.035" /><testcase classname="tests.unit.routers.devices_test" name="test_get_code_exception" time="0.031" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_postgres_success[asyncio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_postgres_failure[asyncio]" time="0.039" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_redis_connection_success[asyncio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_redis_connection_failure[asyncio]" time="0.037" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_success[asyncio]" time="0.021" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_no_credentials[asyncio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_client_error[asyncio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_other_exception[asyncio]" time="0.038" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_success[asyncio]" time="0.023" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_non_200_response[asyncio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_failure[asyncio]" time="0.037" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_empty_host[asyncio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_success[asyncio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_empty_queues[asyncio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_no_credentials[asyncio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_client_error[asyncio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_success[asyncio]" time="0.021" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_no_credentials[asyncio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_partial_credentials[asyncio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_resource_not_found[asyncio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_other_client_error[asyncio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_general_exception[asyncio]" time="0.038" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_other_exception[asyncio]" time="0.037" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_postgres_success[trio]" time="0.028" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_postgres_failure[trio]" time="0.039" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_redis_connection_success[trio]" time="0.024" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_redis_connection_failure[trio]" time="0.039" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_success[trio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_no_credentials[trio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_client_error[trio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_s3_connection_other_exception[trio]" time="0.038" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_success[trio]" time="0.023" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_non_200_response[trio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_failure[trio]" time="0.037" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_http_service_empty_host[trio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_success[trio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_empty_queues[trio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_no_credentials[trio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_client_error[trio]" time="0.084" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_success[trio]" time="0.022" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_no_credentials[trio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_partial_credentials[trio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_resource_not_found[trio]" time="0.034" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_other_client_error[trio]" time="0.033" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_check_cognito_connection_general_exception[trio]" time="0.038" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_verify_sqs_connection_other_exception[trio]" time="0.038" /><testcase classname="tests.unit.services.checks.service_connection_test" name="test_get_sqs_client" time="0.021" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_create_cte" time="0.020" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_query_with_type[asyncio]" time="0.022" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_get_full_data[asyncio]" time="0.021" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_prepare_data[asyncio]" time="0.021" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_query_with_type[trio]" time="0.023" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_get_full_data[trio]" time="0.022" /><testcase classname="tests.unit.services.devices.data_preparer_test" name="test_prepare_data[trio]" time="0.022" /><testcase classname="tests.unit.services.devices.result_service_test" name="test_loop_handler[asyncio]" time="0.022" /><testcase classname="tests.unit.services.devices.result_service_test" name="test_merge_list_dicts[asyncio]" time="0.021" /><testcase classname="tests.unit.services.devices.result_service_test" name="test_get_result[asyncio]" time="0.022" /><testcase classname="tests.unit.services.devices.result_service_test" name="test_loop_handler[trio]" time="0.022" /><testcase classname="tests.unit.services.devices.result_service_test" name="test_merge_list_dicts[trio]" time="0.021" /><testcase classname="tests.unit.services.devices.result_service_test" name="test_get_result[trio]" time="0.023" /><testcase classname="tests.unit.utils_test.TestAESCipher" name="test_init" time="0.021" /><testcase classname="tests.unit.utils_test.TestAESCipher" name="test_encrypt_decrypt" time="0.023" /><testcase classname="tests.unit.utils_test.TestAESCipher" name="test_decrypt_with_invalid_data" time="0.020" /><testcase classname="tests.unit.utils_test" name="test_decrypt" time="0.022" /><testcase classname="tests.unit.utils_test" name="test_generate_random_password" time="0.021" /><testcase classname="tests.unit.utils_test" name="test_generate_random_password_exception" time="0.030" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_get_upcoming_has_seen_intro" time="0.047" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_get_upcoming_has_not_seen_intro" time="0.007" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_has_booked" time="0.046" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_multiple_booking_statuses" time="0.034" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_has_not_booked" time="0.032" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_legacy_session" time="0.032" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_after_30_day_limit" time="0.031" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_7_day_limit" time="0.031" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_after_7_day_limit" time="0.033" /><testcase classname="tests.integration.graphql_api.classes.queries_test" name="test_no_intros_before_cohort_start" time="0.034" /><testcase classname="tests.integration.graphql_api.classes.toggle_favorite_status_test" name="test_toggle_favorite_status" time="0.032" /><testcase classname="tests.integration.graphql_api.classes.toggle_favorite_status_test" name="test_toggle_favorite_status_content_not_found" time="0.028" /><testcase classname="tests.integration.graphql_api.classes.update_booking_status_test" name="test_update_booking_status_booked" time="0.013" /><testcase classname="tests.integration.graphql_api.classes.update_booking_status_test" name="test_update_booking_status_attended" time="0.013" /><testcase classname="tests.integration.graphql_api.classes.update_booking_status_test" name="test_update_booking_status_canceled" time="0.013" /><testcase classname="tests.integration.graphql_api.classes.update_booking_status_test" name="test_update_booking_status_not_found" time="0.012" /><testcase classname="tests.integration.graphql_api.classes.update_booking_status_test" name="test_update_booking_status_already_made" time="0.013" /><testcase classname="tests.integration.graphql_api.cohort.get_module_progress_test" name="test_get_module_progress" time="0.031" /><testcase classname="tests.integration.graphql_api.cohort.get_module_progress_test" name="test_get_module_progress_no_progress" time="0.028" /><testcase classname="tests.integration.graphql_api.cohort.get_participant_program_modules_test" name="test_no_activities" time="0.039" /><testcase classname="tests.integration.graphql_api.cohort.get_participant_program_modules_test" name="test_has_activities" time="0.040" /><testcase classname="tests.integration.graphql_api.cohort.make_progress_test" name="test_make_progress" time="0.060" /><testcase classname="tests.integration.graphql_api.content_library.record_content_interaction_test" name="test_record_content_interaction" time="0.047" /><testcase classname="tests.integration.graphql_api.participant.me_query_test" name="test_me" time="0.061" /><testcase classname="tests.integration.graphql_api.participant.participant_mutations_test" name="test_add_weight_data" time="0.050" /></testsuite></testsuites>