# IoT ETL Package

This Python package provides ETL (Extract, Transform, Load) instrumentation for various IoT (Internet of Things) devices.
The initial integration is with the Withings public API.

## Project Structure

```
ciba_iot_etl
│
├── ciba_iot_etl
│   ├── extract
│   │   ├── withings_api
│   │   │   ├── common.py
│   │   │   ├── const.py
│   │   │   ├── core.py
│   ├── config.py
│   ├── experiments.py
│
├── tests
│
├── pyproject.toml
├── Makefile
└── README.md
```

### ciba_iot_etl/extract/withings_api

- `common.py`: Contains utility functions and common components for the Withings API integration.
- `const.py`: Contains constants and enums related to the Withings API.
- `core.py`: Contains the core functionality for interacting with the Withings API, including classes for loading data and handling API requests.

### ciba_iot_etl/main.py

The main entry point for the package.

## Installation

Ensure you have [Poetry](https://python-poetry.org/) installed. If not, you can install it using the following command:

```sh
curl -sSL https://install.python-poetry.org | python3 -
```

Install the dependencies using Poetry:

```sh
poetry install
```

## Usage

### Running the Package

To run the package, you can execute:

```sh
poetry run python ciba_iot_etl/experiments.py
```

### Building the Package

To build the package, use the Makefile:

```sh
make build
```

### Running Tests

To run tests using `pytest`, use the Makefile:

```sh
make test
```

### Cleaning Build Artifacts

To clean up build artifacts, use the Makefile:

```sh
make clean
```

Sure, here is the section on installing the package in another virtual environment locally:

### Installing the Package in Another Virtual Environment Locally

1. **Build the Package**: First, build the package using Poetry.

   ```sh
   make build
   ```

1. **Create a New Virtual Environment**: Create and activate a new virtual environment.

   ```sh
   python -m venv /path/to/new/venv
   source /path/to/new/venv/bin/activate  # On Windows, use `\path\to\new\venv\Scripts\activate`
   ```

   or

   ```shell
   poetry init
   ```

1. **Install the Package Locally**: Navigate to the `dist/` directory and install the package using `pip`.

   ```sh
   pip install /path/to/your/package/dist/ciba_iot_etl-0.1.0-py3-none-any.whl  # Adjust the version and file name as necessary
   ```

   or

   ```sh
   poetry run pip install /path/to/your/package/dist/ciba_iot_etl-0.1.0-py3-none-any.whl  # Adjust the version and file name as necessary
   ```

1. **Verify Installation**: Verify that the package has been installed correctly by running a script or importing the package in Python.

   ```sh
   python -c "import ciba_iot_etl; print('Package installed successfully')"
   ```

   or

   ```sh
   poetry run python -c "import ciba_iot_etl; print('Package installed successfully')"
   ```

## Withings API Integration

### WithingsLoader Class

The `WithingsLoader` class, derived from `BaseLoader` and `WithingsAPI`, provides methods to interact with the Withings API, including fetching user measurements, activities, intraday activities, and workouts. It also handles token refresh and manages API rate limits.

### Example Usage

Here is an example of how to use the `WithingsLoader` class:

```python
from src.extract.withings_api.core import WithingsLoader

client_id = "your_client_id"
client_secret = "your_client_secret"
state = "your_state"

loader = WithingsLoader(client_id, client_secret, state)

access_token = "your_access_token"
response = loader.get_user_measure(
    access_token,
    {
        "startdate": 1609459200,  # Example timestamp for 2021-01-01
        "enddate": **********,  # Example timestamp for 2021-02-01
    },
)
print(response)
```

## Makefile Commands

The project includes a Makefile for common tasks:

- **help**: Display available commands.
- **install**: Install dependencies using Poetry.
- **build**: Build the package.
- **clean**: Clean build artifacts.
- **test**: Run tests using `pytest`.

## Contributing

Contributions are welcome! Please submit issues and pull requests for any improvements or bug fixes.

## License

This project is licensed under the Proprietary License. See the `LICENSE` file for details.

### Additional Notes

- Ensure your `pyproject.toml` is properly configured with all necessary dependencies.
- Ensure you have tests written in the `tests` directory to validate your code.
- Update the example usage in the README with accurate details as per your project setup.
