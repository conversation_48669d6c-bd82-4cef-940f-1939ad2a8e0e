# scheduled_email_notifications

```mermaid
graph TD
A[AWS Scheduler] -->|Triggers| B[AWS Lambda]
F[SQS Message] -->|Triggers| B[AWS Lambda]
B -->|Check Trigger Type| G{Trigger Type}
G -->|Scheduler| H{Check Hour}
G -->|SQS| I{Check Notification Type}
H -->|Select Task Based on Hour| J[Task Execution]
I -->|Select Task Based on Notification Type| J[Task Execution]
J -->|Prepare Result| K[Prepare SendGrid Message]
K -->|Send Message| L[SendGrid API]
```
