# RPM registration service

### Env setup

### MacOS

#### Python

follow [this](https://medium.com/marvelous-mlops/the-rightway-to-install-python-on-a-mac-f3146d9d9a32) guide

#### UV

follow [this](https://docs.astral.sh/uv/)

#### Docker

follow [this](https://docs.docker.com/desktop/install/mac-install/)

#### Setup your github ssh-key

follow [this](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent)

#### Debug app in built image

##### VSCode Web

http://localhost:3005

Access password is available in [entrypoint.sh](entrypoint.sh)

##### JetBrains Gateway

https://www.jetbrains.com/remote-development/gateway/

##### Unit Tests

Run unit tests with command:

```shell
uv run pytest
```
