# Participant Push Notifications Lambda

## Overview

The participant push notification lambda determines when and for which participants to send push notifications.

## Table of Contents

- [Features](#features)
- [Architecture](#architecture)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Configuration](#configuration)
- [Usage](#usage)
- [Mermaid Graph](#mermaid-graph)
- [Testing](#testing)

## Features

- **Daily Push Notifications**: Send push notifications to participants based on daily activities.
- **Chat Push Notifications**: Send push notifications to participants based on chat activities.

## Architecture

The architecture of Participant Push Notifications lambda consists of the following components:

- **AWS Lambda**: The core of the application, handling notifications logic and data preparation.
- **API Gateway**: Exposes endpoint for Twilio webhook integration.
- **SQS**: Receives notification messages from lambda and sends them to Push Notification Service.
- **SSM Parameter Store**: Stores configuration parameters for the application.
- **Docker**: Containerizes the application for consistent deployment.

## Mermaid Graph

Below is a visual representation of the lambda process and its interactions with other services:

```mermaid
graph TD
    CRON[CRON everyday at 01:00 am UTC] -- Trigger for Daily push notifications --> Lambda[Lambda API Gateway]
    Twilio[Twilio Webhook trigger onNewMessage]--> LambdaAPI
    LambdaAPI -- Trigger for Chat push notifications  --> Lambda[Participant Push Notifications]
    Lambda -- Pulls environment parameters --> SSM[SSM Parameter Store]
    Lambda -- Looks for message sender via Chat identity and Chat group --> ChatDB[Chat DB]
    Lambda -- Get Participant Data --> ParticipantDB[Participant DB]
    ParticipantDB -- If there are messages to send --> Queue[Push Notifications Queue]
    ChatDB -- Message is from Health Coach or Admin, pull participants --> ParticipantDB
```

## Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.12** or higher
- **Docker** (for local development and testing)
- **AWS CLI** configured with appropriate permissions
- **Git** (for cloning the repository)

### Installation

1. **Clone the repository**:

   ```bash
   <NAME_EMAIL>:Cibahealth/push-notifications-lambda.git
   cd push_notifications

   ```

1. **Install dependencies**:
   You can use either pip or poetry to install the required dependencies.
   Using uv:

   ```bash
   uv install
   ```

1. **Set up environment variables**:
   Create a `.env` file in the root directory and add the necessary environment variables. Refer to the `template.yaml` for required variables.

### Configuration

- **Docker Configuration**: The application is containerized using Docker. Ensure that the `Dockerfile` is correctly set up for your environment.
- **AWS Configuration**: Update the `template.yaml` with your AWS account details, including security groups, subnet IDs, and other parameters.

## Usage

To run the application locally, you can use Docker:

```bash
docker build -t push_notifications .
docker run -p 9000:8080 push_notifications
```
