AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  python3.12

  Sample SAM Template for participant_push_notifications

Parameters:
  LambdaImageUri:
    Type: String
    Description: ECR Image URI for the ParticipantPushNotifications function
    Default: "572827854243.dkr.ecr.us-east-2.amazonaws.com/participant_push_notifications:latest"
  Environment:
    Type: String
    Description: Environment
    Default: "dev"
  DatadogApiKey:
    Type: String
    Description: Datadog API Key
    Default: ""
  Version:
    Type: String
    Description: Version
    Default: "0.0.0"
  Debug:
    Type: Number
    Description: Debug
    Default: "0"
  IsNewEnv:
    Type: Number
    Default: 0
  IsLambda:
    Type: Number
    Default: 1
  SecurityGroupIds:
    Type: List<String>
    Description: Security Group IDs
    Default: "sg-0c6d9bdb426027ffa"
  SubnetIds:
    Type: List<String>
    Description: Subnet IDs

Globals:
  Function:
    Timeout: 900
    LoggingConfig:
      LogFormat: JSON

Resources:
  ParticipantPushNotifications:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      PackageType: Image
      ImageUri: !Ref LambdaImageUri
      Role: !GetAtt ParticipantPushNotificationsFunctionRole.Arn
      MemorySize: 512
      Environment:
        Variables:
          ENV: !Ref Environment
          VERSION: !Ref Version
          DEBUG: !Ref Debug
          IS_NEW_ENV: !Ref IsNewEnv
          IS_LAMBDA: !Ref IsLambda
#          DD_API_KEY: !Ref DatadogApiKey
#          DD_ENV: !Ref Environment
#          DD_SERVICE: "participant_push_notifications"
      Events:
        DailyParticipantPushNotifications:
          Type: Schedule
          Properties:
            Name: DailyParticipantPushNotifications
            Schedule: cron(0 15 * * ? *)
        DailyClassReminder:
          Type: Schedule
          Properties:
            Name: DailyClassReminder
            Schedule: cron(0 1 * * ? *)
        HourlyClassReminder:
          Type: Schedule
          Properties:
            Name: HourlyClassReminder
            Schedule: cron(0/5 * * * ? *)
        TwilioWebhook:
          Type: Api
          Properties:
            Path: /twilio-webhook
            Method: any
      VpcConfig:
        SecurityGroupIds: !Ref SecurityGroupIds
        SubnetIds: !Ref SubnetIds
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./participant_push_notifications
  #  ParticipantPushNotificationsSQS:
  #    Type: AWS::SQS::Queue
  #    Properties:
  #      QueueName: daily_push_notifications
  #      VisibilityTimeout: 900
  ParticipantPushNotificationsFunctionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service: "lambda.amazonaws.com"
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: "ParticipantPushNotificationsPolicy"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - "ssm:GetParameter"
                  - "ssm:GetParameters"
                Resource: "*"
              - Effect: "Allow"
                Action:
                  - "sqs:ReceiveMessage"
                  - "sqs:DeleteMessage"
                  - "sqs:GetQueueAttributes"
                  - "sqs:SendMessage"
                  - "sqs:SendMessageBatch"
                Resource: "*"
              - Effect: "Allow"
                Action:
                  - "sns:Publish"
                Resource: "*"
              - Effect: "Allow"
                Action:
                  - "ec2:CreateNetworkInterface"
                  - "ec2:DescribeNetworkInterfaces"
                  - "ec2:DeleteNetworkInterface"
                Resource: "*"
        - PolicyName: "LambdaCloudWatchLogsPolicy"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - "logs:CreateLogGroup"
                  - "logs:CreateLogStream"
                  - "logs:PutLogEvents"
                Resource: "arn:aws:logs:*:*:*"

Outputs:
  LambdaFunctionName:
    Description: "ParticipantPushNotifications Lambda Function ARN"
    Value: !GetAtt ParticipantPushNotifications.Arn
  ParticipantPushNotificationsFunctionRole:
    Description: "Implicit IAM Role created for ParticipantPushNotificationsFunctionRole function"
    Value: !GetAtt ParticipantPushNotificationsFunctionRole.Arn
