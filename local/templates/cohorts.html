<!DOCTYPE html>
<html>
<head>
    <title>Cohorts List</title>
    <style>
        table, th, td {
            border: 1px solid #333;
            border-collapse: collapse;
            padding: 8px;
        }
        tr.clickable {
            cursor: pointer;
        }
        tr.clickable:hover {
            background-color: #f0f0f0;
        }
    </style>
    <script>
        function rowClicked(cohortId) {
            window.location.href = '/cohort/' + cohortId;
        }
    </script>
</head>
<body>
    <h1>Cohorts List</h1>
    <h1><a href="/ui/participants">Participants List</a></h1>
    <form method="get" action="/cohorts">
        <label for="name">Search by Name:</label>
        <input type="text" id="name" name="name" value="{{ name or '' }}">
        <label for="started_at">Filter by Started At:</label>
        <input type="date" id="started_at" name="started_at" value="{{ started_at or '' }}">
        <button type="submit">Search</button>
    </form>
    <br>
    <table>
        <thead>
            <tr>
                <th>Cohort Name</th>
                <th>Started At</th>
            </tr>
        </thead>
        <tbody>
            {% for cohort in cohorts %}
            <tr class="clickable" onclick="rowClicked('{{ cohort.id }}')">
                <td>{{ cohort.name }}</td>
                <td>{{ cohort.started_at }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="2">No cohorts found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
