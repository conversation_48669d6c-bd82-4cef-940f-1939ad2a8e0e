from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Query, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from tortoise.contrib.fastapi import register_tortoise
from ciba_participant.participant.models import Participant
from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.activity.maps import PARTICIPANT_TO_SOLERA_MAP
from ciba_participant import get_settings
from ciba_participant.db import get_tortoise_orm_config
from mint_vault.solera import get_participant_status, SoleraParticipant
from ciba_participant.solera.api import (
    SoleraAPIAsyncClient,
    SoleraActivitiesList,
    ValidationError,
)
from mint_vault.data_models import CibaActivityRequest
from typing import Optional
import pendulum
from aws_lambda_powertools import Logger
from ciba_participant.cohort.models import Cohort, CohortMembers
import json
from ciba_participant.participant.service import ParticipantService
import re
import os
import random


settings = get_settings()
logger = Logger(service="MintVault UI helper")

app = FastAPI(
    title="MintVault",
    debug=True,
    version="0.0.0",
)

# Mount static files (for CSS/JS, if needed)
# app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup Jinja2 templates (ensure your templates are in the "templates" folder)
templates = Jinja2Templates(directory="templates")

# Register Tortoise ORM with the app
register_tortoise(
    app,
    config=get_tortoise_orm_config(settings.default_db_url),
    add_exception_handlers=True,
)
client = SoleraAPIAsyncClient(
    client_id=settings.SOLERA_CLIENT_ID,
    client_secret=settings.SOLERA_CLIENT_SECRET,
    environment=settings.ENV,
)


# Endpoint for paginated and searchable participants list (10 per page)
@app.get("/ui/participants", response_class=HTMLResponse)
async def list_participants(
    request: Request,
    page: int = Query(1, ge=1),
    search_id: str = Query(None),
    search_email: str = Query(None),
):
    per_page = 10

    # Start a base queryset
    query = Participant.all()

    # If search filters are provided, apply them
    if search_id:
        query = query.filter(id=search_id)
    if search_email:
        query = query.filter(email__icontains=search_email)

    total_participants = await query.count()
    participants = await query.offset((page - 1) * per_page).limit(per_page)
    total_pages = (total_participants + per_page - 1) // per_page

    return templates.TemplateResponse(
        "participants_list.html",
        {
            "request": request,
            "participants": participants,
            "page": page,
            "total_pages": total_pages,
            "search_id": search_id or "",
            "search_email": search_email or "",
        },
    )


# Endpoint for participant details page
@app.get("/ui/participants/{participant_id}", response_class=HTMLResponse)
async def participant_details(request: Request, participant_id: str):
    participant = await get_participant_status(
        client=client, participant_id=participant_id
    )
    if not participant:
        raise HTTPException(status_code=404, detail="Participant not found")
    return templates.TemplateResponse(
        "participant_details.html",
        {"request": request, "participant": participant},
    )


@app.post("/participant/activity")
async def create_participant_activity(
    participant_id: str = Form(...),
    activity_type: ParticipantActivityEnum = Form(...),
    activity_date: Optional[str] = Form(None),
    activity_value: Optional[str] = Form(None),
    is_device: Optional[bool] = Form(False),
):
    """Create participant activity endpoint using form data."""
    # Parse activity_date from string to datetime if provided
    response = None
    dt_activity_date = None
    if activity_date:
        try:
            dt_activity_date = pendulum.parse(activity_date)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid datetime format")

    try:
        participant_activity = CibaActivityRequest(
            participant_id=participant_id,
            activity_type=activity_type,
            activity_date=dt_activity_date,
            activity_value=activity_value,
            is_device=is_device,
        )
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    # Instantiate and authenticate the client.
    client = SoleraAPIAsyncClient(
        client_id=settings.SOLERA_CLIENT_ID,
        client_secret=settings.SOLERA_CLIENT_SECRET,
        environment=settings.ENV,
    )
    await client.authenticate()

    # Process the activity.
    sp = SoleraParticipant(participant_id=participant_activity.participant_id)
    await sp.get_ciba_participant()
    sp.data_preparer = await sp.init_solera_data_handler()
    await sp.get_ciba_participant()
    saved_activity = await sp.save_activity(ciba_activity=participant_activity)
    solera_activity = await sp.convert_ciba_to_solera_activity(activity=saved_activity)
    if solera_activity:
        solera_list = SoleraActivitiesList()
        solera_list.activities.append(solera_activity)
        response = await SoleraParticipant.submit_activities(
            solera_client=client,
            activities=solera_list,
        )

    return {"status": "success", "message": "Activity processed", "body": response}


@app.post("/participant/activity/sync")
async def sync_activities(
    participant_id: str = Form(...),
    activity_type: ParticipantActivityEnum = Form(...),
    start_date: str = Form(...),
    end_date: str = Form(...),
    preview: bool = Form(...),
):
    from mint_vault.solera import get_activities_for_date_range

    start_date = pendulum.parse(start_date)
    end_date = pendulum.parse(end_date)

    activities = await get_activities_for_date_range(
        client=client,
        participant_id=participant_id,
        start_date=start_date,
        end_date=end_date,
        activity_type=activity_type,
    )

    if preview:
        return activities
    solera_list = SoleraActivitiesList()
    solera_list.activities.append(activities)
    response = await SoleraParticipant.submit_activities(
        solera_client=client,
        activities=solera_list,
    )
    return response


def sort_key(milestone_name: str) -> int:
    """
    Extracts a numeric value from a milestone name.
    For example:
      "M1" -> 1
      "Milestone 1" -> 1
      "Milestone 4a" -> 4
    If no number is found, returns 0.
    """
    match = re.search(r"(\d+)", milestone_name)
    return int(match.group(1)) if match else 0


@app.get("/cohort/{cohort_id}", response_class=HTMLResponse)
async def get_cohort_details(request: Request, cohort_id: str):
    # Query the cohort; if not found, raise an error
    cohort = await Cohort.filter(id=cohort_id).first()
    if not cohort:
        raise HTTPException(status_code=404, detail="Cohort not found")

    # Get cohort members and their participant details
    cohort_members = await cohort.participants
    total_participants = len(cohort_members)

    participants_details = []

    # Dictionaries to count milestone achievements per milestone name/key (for overall progress)
    solera_counts = {}
    solera_milestone_names = set()
    ciba_counts = {}
    ciba_milestone_names = set()

    # Retrieve each participant's status and aggregate milestone counts
    for participant in cohort_members:
        # Skip test participants
        if participant.is_test:
            continue
        status = await get_participant_status(
            participant_id=participant.id, client=client
        )
        participants_details.append(status)

        # Process Solera Milestone Status (assumed to be a list)
        solera_status_list = status.solera_milestone_status
        if solera_status_list:
            for solera in solera_status_list:
                for milestone in solera.get("milestones", []):
                    milestone_name = milestone.get("name")
                    if milestone_name:
                        solera_milestone_names.add(milestone_name)
                        if milestone.get("fulfilled"):
                            solera_counts[milestone_name] = (
                                solera_counts.get(milestone_name, 0) + 1
                            )

        # Process Ciba Milestone Status (assumed to be a dict)
        ciba_status = status.ciba_milestone_status
        if ciba_status:
            for milestone_key, milestone_data in ciba_status.items():
                ciba_milestone_names.add(milestone_key)
                if milestone_data.get("achieved"):
                    ciba_counts[milestone_key] = ciba_counts.get(milestone_key, 0) + 1

    # Calculate percentages per milestone for overall progress
    solera_milestones_percentage = {}
    for name in solera_milestone_names:
        solera_milestones_percentage[name] = (
            (solera_counts.get(name, 0) / total_participants * 100)
            if total_participants > 0
            else 0
        )

    ciba_milestones_percentage = {}
    for name in ciba_milestone_names:
        ciba_milestones_percentage[name] = (
            (ciba_counts.get(name, 0) / total_participants * 100)
            if total_participants > 0
            else 0
        )

    # Sort the milestone percentages (for display)
    sorted_solera = sorted(
        solera_milestones_percentage.items(), key=lambda kv: sort_key(kv[0])
    )
    sorted_ciba = sorted(
        ciba_milestones_percentage.items(), key=lambda kv: sort_key(kv[0])
    )

    # Build an ordered list of solera milestone names
    sorted_solera_names = [milestone for milestone, _ in sorted_solera]

    # Group participants by the first Solera milestone they have NOT fulfilled.
    solera_pending_by_milestone = {milestone: [] for milestone in sorted_solera_names}
    for status in participants_details:
        # For each participant, iterate over milestones in order.
        for milestone in sorted_solera_names:
            has_achieved = False
            for solera in status.solera_milestone_status:
                for m in solera.get("milestones", []):
                    if m.get("name") == milestone and m.get("fulfilled"):
                        has_achieved = True
                        break
                if has_achieved:
                    break
            if not has_achieved:
                solera_pending_by_milestone[milestone].append(status)
                break
        # If the participant has reached all milestones, they won't be assigned to any pending group.

    # (Optional) Save the output_data as JSON.
    json_participants_details = [el.model_dump_json() for el in participants_details]
    json_solera_pending_by_milestone = {}
    for mile, p_list in solera_pending_by_milestone.items():
        if p_list:
            if mile not in json_solera_pending_by_milestone:
                json_solera_pending_by_milestone[mile] = []
            for p_stat in p_list:
                json_solera_pending_by_milestone[mile].append(p_stat.model_dump_json())

    output_data = {
        "cohort": {
            "id": str(cohort.id),
            "name": cohort.name,
            "started_at": str(cohort.started_at),
        },
        "total_participants": total_participants,
        "participants_details": json_participants_details,
        "sorted_solera": sorted_solera,
        "sorted_ciba": sorted_ciba,
        "solera_pending_by_milestone": json_solera_pending_by_milestone,
    }
    file_path = os.path.join(
        "output", f"{output_data['cohort']['name']}_cohort_details.json"
    )
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w") as json_file:
        json.dump(output_data, json_file, default=str, indent=4)

    return templates.TemplateResponse(
        "cohort_details.html",
        {
            "request": request,
            "cohort": cohort,
            "participants_details": participants_details,
            "sorted_solera": sorted_solera,
            "sorted_ciba": sorted_ciba,
            "solera_pending_by_milestone": solera_pending_by_milestone,
        },
    )


@app.get("/cohorts", response_class=HTMLResponse)
async def cohort_list(request: Request, name: str = None, started_at: str = None):
    filter_kwargs = {}
    if name:
        filter_kwargs["name__icontains"] = name
    if started_at:
        filter_kwargs["started_at__gte"] = pendulum.parse(started_at)
    cohorts = await Cohort.filter(**filter_kwargs).all()
    return templates.TemplateResponse(
        "cohorts.html",
        {
            "request": request,
            "cohorts": cohorts,
            "name": name,
            "started_at": started_at,
        },
    )


@app.get(
    "/participant/{participant_id}/activity/{activity_type}/correct/{reference_id}"
)
async def correct_activity(
    participant_id: str, activity_type: ParticipantActivityEnum, reference_id: str
):
    solera_type = PARTICIPANT_TO_SOLERA_MAP[activity_type]

    sp = SoleraParticipant(participant_id=participant_id, solera_client=client)
    await sp.get_ciba_participant()
    sp.data_preparer = await sp.init_solera_data_handler()
    resp = await sp.correct(reference_id=reference_id, activity_type=solera_type)
    return resp


# @app.get("/current_milestone")
# async def get_current_milestone():
#     from ciba_participant.cohort.models import Cohort, CohortMembers
#     from collections import defaultdict


#     # Fetch cohorts that started on or after 2025-01-01
#     cohorts = await Cohort.filter(
#         started_at__gte=pendulum.parse('2024-12-01').date(),
#         started_at__lte=pendulum.parse('2025-03-01').date()
#         ).all()
#     if not cohorts:
#         return {}  # Return an empty dictionary if no cohorts are found

#     # Extract cohort IDs
#     cohort_ids = [c.id for c in cohorts]
#     cohort_start_id = [(c.id, c.started_at, c.program.title) for c in cohorts]


#     # Fetch cohort members and group participants by cohort_id
#     cohort_members = await CohortMembers.filter(cohort_id__in=cohort_ids).values_list("cohort_id", "participant_id")

#     cohort_dict = defaultdict(list)
#     member_ids = set()  # Using set to avoid duplicate participant queries

#     for cohort_id, participant_id in cohort_members:
#         cohort_dict[cohort_id].append(participant_id)
#         member_ids.add(participant_id)

#     if not member_ids:
#         return {}  # Return empty if there are no participants

#     # Fetch only active, non-test participants
#     active_participants = await Participant.filter(id__in=member_ids, status='active', is_test=False).all()

#     # Create participant activity mapping
#     participant_activities = {}

#     for participant in active_participants:
#         activities = await participant.activities  # Assuming `activities` is a related field

#         participant_activities[participant.id] = activities

#     # Replace participant IDs with activity data in cohort_dict
#     for cohort_id, participant_ids in cohort_dict.items():
#         cohort_dict[cohort_id] = {pid: {'participant_activities': participant_activities.get(pid, [])} for pid in participant_ids}

#     return cohort_dict


@app.get("/sync_deleted")
async def sync_deleted_participants():
    from csv import DictReader

    # If needed, you can call sync_solera_enrollment_status here.
    # deleted_users = await sync_solera_enrollment_status(client=client)

    with open("deleted_1-2025-03-12_71927.csv", "r") as deleted_file:
        deleted_users = list(DictReader(deleted_file))

    for deleted_user in deleted_users:
        # Process each deleted user. Replace the following print with your logic.
        try:
            user_id = deleted_user.get("id")
            user_email = deleted_user.get("email")
            await ParticipantService(participant_id=user_id, email=user_email).delete(
                email=user_email,
            )
        except Exception as e:
            logger.warning(f"Got error: {e}")

    return {"status": "success"}


@app.get("/catchup_chat")
async def catchup_chat():
    client = SoleraAPIAsyncClient(
        client_id=settings.SOLERA_CLIENT_ID,
        client_secret=settings.SOLERA_CLIENT_SECRET,
        environment=settings.ENV,
    )
    await client.authenticate()

    cohorts = await Cohort.filter(
        # started_at__gte=pendulum.parse('2024-12-01').date(),
        # started_at__lte=pendulum.parse('2025-03-01').date()
        id__in=[]
    ).all()
    if not cohorts:
        return {}  # Return an empty dictionary if no cohorts are found

    # Extract cohort IDs

    participant_activities = []
    now = pendulum.now()

    for cohort in cohorts:
        cohort_members = await CohortMembers.filter(cohort_id=cohort.id).values_list(
            "participant_id", flat=True
        )
        cohort_start = pendulum.instance(cohort.started_at)

        # Calculate the number of full weeks that have passed since the cohort started.
        weeks_passed = (now - cohort_start).in_weeks()

        # For each full week, generate an activity.
        for week in range(1, weeks_passed + 1):
            base_date = cohort_start.add(weeks=week)

            # Calculate the activity date for the current week.
            activity_date = base_date.subtract(
                days=random.randint(1, 4),
                hours=random.randint(1, 8),
                minutes=random.randint(1, 59),
                seconds=random.randint(1, 59),
            )
            if activity_date > now:
                break

            for participant_id in cohort_members:
                # Create an activity request for the participant.
                activity = CibaActivityRequest(
                    participant_id=str(participant_id),
                    activity_type="chat_type",
                    activity_date=activity_date,
                    activity_value="1",  # Replace with your actual value
                    is_device=False,  # Set based on your actual logic
                )
                participant_activities.append(activity)

    response_data = {}
    for participant_activity in participant_activities:
        try:
            sp = SoleraParticipant(participant_id=participant_activity.participant_id)
            await sp.get_ciba_participant()
            sp.data_preparer = await sp.init_solera_data_handler()
            await sp.get_ciba_participant()
            saved_activity = await sp.save_activity(ciba_activity=participant_activity)
            solera_activity = await sp.convert_ciba_to_solera_activity(
                activity=saved_activity
            )
            if solera_activity:
                solera_list = SoleraActivitiesList()
                solera_list.activities.append(solera_activity)
                response = await SoleraParticipant.submit_activities(
                    solera_client=client,
                    activities=solera_list,
                )
                if not response_data[participant_activity.participant_id]:
                    response_data[participant_activity.participant_id] = []
                response_data[participant_activity.participant_id].append(
                    {
                        "requestId": response.requestId,
                        "timestamp": solera_activity.timestamp,
                    }
                )
        except Exception as e:
            logger.warning(f"Got error: {e}")

    # a = 1
    # with open(f'{pendulum.now().isoformat()}_chat_catchup.json', 'w+') as f:
    #     f.write(json.dumps(response_data))
    # a = 1
