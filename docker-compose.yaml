version: '3'
services:
  db:
    image: postgres:14
    container_name: participant_api_db
    restart: unless-stopped
    tty: true
    ports:
      - "5434:5432"
    environment:
      POSTGRES_DB: participant_v2
      POSTGRES_USER: participant_api
      POSTGRES_PASSWORD: secret
    volumes:
      - participant_admin_db:/var/lib/postgresql
      - ./init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    networks:
        - participant-network
  redis:
    image: redis:6
    container_name: participant_api_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - participant-network

  app:
    image: participant-admin:dev-latest
    container_name: participant-admin
    restart: unless-stopped
    tty: true
    environment:
      ENV: local
      DOCKER_BUILDKIT: 1

    ports:
      - "8000:8000"
      - "2222:22"
      - "5678:5678"
      - "3005:3005"
    entrypoint: /app/entrypoint.sh
    depends_on:
      - db
    volumes:
      - .:/app/
    networks:
        - participant-network


volumes:
  participant_admin_db:
    driver: local
networks:
  participant-network:
    external: true
