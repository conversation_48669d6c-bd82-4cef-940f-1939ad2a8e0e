from typing import List
from uuid import UUID

from strawberry.dataloader import DataLoader
from ciba_participant.activity.crud import ParticipantActivityRepository

from src.activity.types import ParticipantActivityGraphType


async def batch_load_activities(
    keys: List[UUID],
) -> List[List[ParticipantActivityGraphType]]:
    activities = (
        await ParticipantActivityRepository.get_activities_by_participant_ids(
            keys
        )
    )
    activities_map = {key: [] for key in keys}
    for activity in activities:
        activities_map[activity.participant_id].append(activity)
    return [activities_map[key] for key in keys]


activity_dataloader = DataLoader(load_fn=batch_load_activities)
