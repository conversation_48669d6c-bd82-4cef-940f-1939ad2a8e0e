import strawberry
from ciba_participant.activity.crud import ParticipantActivityRepository

from src.activity.inputs import (
    ParticipantActivityInput,
    ParticipantActivityUpdateInput,
    ParticipantActivityIDInput,
)
from src.activity.types import ParticipantActivityGraphType
from src.common.types import SimpleRespType
from src.log.logging import logger


async def create_participant_activity(
    data: ParticipantActivityInput,
) -> ParticipantActivityGraphType:
    """Create participant activity resolver"""
    try:
        activity = (
            await ParticipantActivityRepository.create_participant_activity(
                data
            )
        )
        return activity
    except Exception as e:
        logger.exception(e)
        raise e


async def update_participant_activity(
    data: ParticipantActivityUpdateInput,
) -> ParticipantActivityGraphType:
    """Update participant activity resolver"""
    try:
        activity = (
            await ParticipantActivityRepository.update_participant_activity(
                data.id, **data.__dict__
            )
        )
        return activity
    except Exception as e:
        logger.exception(e)
        raise e


async def delete_participant_activity(
    data: ParticipantActivityIDInput,
) -> SimpleRespType:
    """Delete participant activity resolver"""
    try:
        await ParticipantActivityRepository.delete_participant_activity(
            data.id
        )
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


@strawberry.type
class ActivityMutation:
    """Mutation for activities"""

    create_participant_activity: ParticipantActivityGraphType = (
        strawberry.field(resolver=create_participant_activity)
    )
    update_participant_activity: ParticipantActivityGraphType = (
        strawberry.field(resolver=update_participant_activity)
    )
    delete_participant_activity: SimpleRespType = strawberry.field(
        resolver=delete_participant_activity
    )
