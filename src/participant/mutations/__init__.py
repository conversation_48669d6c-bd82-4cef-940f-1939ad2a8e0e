import json

import httpx
import strawberry
from httpx import Response
from tortoise.transactions import in_transaction

from ciba_participant.chat_api.chat_api import (
    FailedToAssignParticipantToChat,
)
from ciba_participant.cohort.models import Cohort
from ciba_participant.participant.models import (
    Participant,
    Authorized,
    ParticipantStatus,
)
from ciba_participant.participant.crud import (
    ParticipantRepository,
    SoleraParticipantRepository,
    HeadsUpParticipantRepository,
    ParticipantMetaRepository,
    AuthorizedRepository,
)

from src.auth.decorators import is_authorized
from src.auth.types import AuthorizedType
from src.common.types import SimpleRespType, ShortResponse
from src.log.logging import logger
from src.participant.constants import ADD_TO_GROUP_CHAT_MUTATION
from src.participant.inputs import (
    ParticipantCreateInput,
    ParticipantUpdateInput,
    ParticipantIDInput,
    SoleraParticipantCreateInput,
    SoleraParticipantUpdateInput,
    SoleraParticipantIDInput,
    HeadsUpParticipantCreateInput,
    HeadsUpParticipantUpdateInput,
    HeadsUpParticipantIDInput,
    ParticipantMetaCreateInput,
    ParticipantMetaUpdateInput,
    ParticipantMetaIDInput,
    AuthorizedCreateInput,
    AuthorizedUpdateInput,
    AssignParticipantChatInput,
)
from src.participant.mutations.merge_latest_data import merge_latest_data
from src.participant.types import (
    ParticipantType,
    SoleraParticipantType,
    HeadsUpParticipantType,
    ParticipantMetaType,
    ConversationType,
    ConversationDneError,
)
from ciba_participant.participant.pydantic_models import (
    ParticipantCreate,
    SoleraParticipantCreate,
    HeadsUpParticipantCreate,
    ParticipantMetaCreate,
    ParticipantMetaUpdate,
    AuthorizedCreate,
    AuthorizedUpdate,
)
from src.settings import get_settings

settings = get_settings()


async def create_participant(data: ParticipantCreateInput) -> ParticipantType:
    try:
        logger.info(f"Creating participant with data: {data.email}")
        participant = await ParticipantRepository.create_participant(
            ParticipantCreate(**data.__dict__)
        )
        logger.info(f"Participant created: {participant.email}")
        return participant
    except Exception as e:
        logger.exception(e)
        raise e


async def update_participant(data: ParticipantUpdateInput) -> ParticipantType:
    try:
        logger.info(f"Updating participant with data: {data.id}")
        participant_update = {
            k: v
            for k, v in data.__dict__.items()
            if v is not None and k != "id"
        }
        updated_participant = await ParticipantRepository.update_participant(
            data.id, participant_update
        )
        logger.info(f"Participant updated: {updated_participant.id}")
        return updated_participant
    except Exception as e:
        logger.exception(e)
        raise e


async def delete_participant(data: ParticipantIDInput) -> SimpleRespType:
    try:
        async with in_transaction():
            # Get participant with lock
            participant = (
                await Participant.filter(id=data.id)
                .select_for_update()
                .get_or_none()
            )
            if not participant:
                logger.info(f"Participant with id: {data.id}, not found")
                return SimpleRespType(status=False)

            # Verify current status
            if participant.status == ParticipantStatus.DELETED:
                logger.info(f"Participant {data.id} already deleted")
                return SimpleRespType(status=True)

            # Get cohort with lock
            cohort = (
                await Cohort.filter(participants__id=participant.id)
                .select_for_update()
                .first()
            )
            if not cohort:
                logger.info("Participant is not part of any cohort")
                return SimpleRespType(status=False)

            # Disable participant and handle all cleanup
            result = await ParticipantRepository.disable_participant(
                email=participant.email,
                cohort=cohort,
            )

            if not result:
                logger.error(f"Failed to disable participant {data.id}")
                return SimpleRespType(status=False)
            return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(f"Error deleting participant {data.id}: {str(e)}")
        return SimpleRespType(status=False)


async def create_solera_participant(
    data: SoleraParticipantCreateInput,
) -> SoleraParticipantType:
    try:
        logger.info(
            f"Creating solera participant with data: {data.participant_id}"
        )
        solera_participant = (
            await SoleraParticipantRepository.create_solera_participant(
                SoleraParticipantCreate(**data.__dict__)
            )
        )
        logger.info(
            f"Solera participant created: {solera_participant.participant_id}"
        )
        return solera_participant
    except Exception as e:
        logger.exception(e)
        raise e


async def update_solera_participant(
    data: SoleraParticipantUpdateInput,
) -> SoleraParticipantType:
    try:
        logger.info(f"Updating solera participant with data: {data.id}")
        solera_participant_update = {
            k: v
            for k, v in data.__dict__.items()
            if v is not None and k != "id"
        }
        updated_solera_participant = (
            await SoleraParticipantRepository.update_solera_participant(
                data.id, solera_participant_update
            )
        )
        logger.info(
            f"Solera participant updated: {updated_solera_participant.id}"
        )
        return updated_solera_participant
    except Exception as e:
        logger.exception(e)
        raise e


async def delete_solera_participant(
    data: SoleraParticipantIDInput,
) -> SimpleRespType:
    try:
        logger.info(
            f"Deleting solera participant with data: {data.participant_id}"
        )
        await SoleraParticipantRepository.delete_solera_participant(
            data.participant_id
        )
        logger.info(f"Solera participant deleted: {data.participant_id}")
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


async def create_heads_up_participant(
    data: HeadsUpParticipantCreateInput,
) -> HeadsUpParticipantType:
    try:
        logger.info(
            f"Creating heads up participant with data: {data.participant_id}"
        )
        heads_up_participant = (
            await HeadsUpParticipantRepository.create_heads_up_participant(
                HeadsUpParticipantCreate(**data.__dict__)
            )
        )
        logger.info(
            f"Heads up participant created: {heads_up_participant.participant_id}"
        )
        return heads_up_participant
    except Exception as e:
        logger.exception(e)
        raise e


async def update_heads_up_participant(
    data: HeadsUpParticipantUpdateInput,
) -> HeadsUpParticipantType:
    try:
        logger.info(f"Updating heads up participant with data: {data.id}")
        heads_up_participant_update = {
            k: v
            for k, v in data.__dict__.items()
            if v is not None and k != "id"
        }
        updated_heads_up_participant = (
            await HeadsUpParticipantRepository.update_heads_up_participant(
                data.id, heads_up_participant_update
            )
        )
        logger.info(
            f"Heads up participant updated: {updated_heads_up_participant.id}"
        )
        return updated_heads_up_participant
    except Exception as e:
        logger.exception(e)
        raise e


async def delete_heads_up_participant(
    data: HeadsUpParticipantIDInput,
) -> SimpleRespType:
    try:
        logger.info(f"Deleting heads up participant with data: {data.id}")
        await HeadsUpParticipantRepository.delete_heads_up_participant(data.id)
        logger.info(f"Heads up participant deleted: {data.id}")
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


async def create_participant_meta(
    data: ParticipantMetaCreateInput,
) -> ParticipantMetaType:
    try:
        logger.info(
            f"Creating participant meta with data: {data.participant_id}"
        )
        participant_meta = (
            await ParticipantMetaRepository.create_participant_meta(
                ParticipantMetaCreate(**data.__dict__)
            )
        )
        formatted_meta = json.dumps(participant_meta.metadata)
        participant_meta.metadata = formatted_meta
        logger.info(
            f"Participant meta created: {participant_meta.participant_id}"
        )
        return participant_meta
    except Exception as e:
        logger.exception(e)
        raise e


async def update_participant_meta(
    data: ParticipantMetaUpdateInput,
) -> ParticipantMetaType:
    try:
        logger.info(f"Updating participant meta with data: {data.id}")
        updated_participant_meta = (
            await ParticipantMetaRepository.update_participant_meta(
                pm=ParticipantMetaUpdate(**data.__dict__)
            )
        )
        formatted_meta = json.dumps(updated_participant_meta.metadata)
        updated_participant_meta.metadata = formatted_meta
        logger.info(
            f"Participant meta updated: {updated_participant_meta.participant_id}"
        )
        return updated_participant_meta
    except Exception as e:
        logger.exception(e)
        raise e


async def delete_participant_meta(
    data: ParticipantMetaIDInput,
) -> SimpleRespType:
    try:
        logger.info(f"Deleting participant meta with data: {data.id}")
        await ParticipantMetaRepository.delete_participant_meta(data.id)
        logger.info(f"Participant meta deleted: {data.id}")
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


@is_authorized(
    error_msg="Authorized creation requires Health Coach permissions."
)
async def create_authorized(
    data: AuthorizedCreateInput, password: str
) -> AuthorizedType:
    try:
        logger.info(f"Creating authorized with email: {data.email}")
        authorized = await AuthorizedRepository.create_authorized(
            AuthorizedCreate(**data.__dict__),
            password=password,
        )
        logger.info(f"Authorized with email: {authorized.email}, created")
        return AuthorizedType(**authorized.__dict__)
    except Exception as e:
        logger.exception(e)
        raise e


async def update_authorized(data: AuthorizedUpdateInput) -> AuthorizedType:
    try:
        logger.info(f"Updating authorized with data: {data.email}")
        authorized = await AuthorizedRepository.update_authorized(
            authorized_id=data.id, authorized=AuthorizedUpdate(**data.__dict__)
        )
        logger.info(f"Authorized updated: {authorized.email}")
        return AuthorizedType(**authorized.__dict__)
    except Exception as e:
        logger.exception(e)
        raise e


async def add_to_group_chat_request(
    url: str,
    api_key: str,
    cohort_unique_name: str,
    participant_type: str,
    participant_id: str,
    chat_identity: str,
) -> Response:
    headers = {"X-AUTH-KEY": api_key}
    payload = {
        "query": ADD_TO_GROUP_CHAT_MUTATION,
        "variables": {
            "uniqueName": cohort_unique_name,
            "participant": {
                "type": participant_type,
                "id": participant_id,
                "chatIdentity": chat_identity,
            },
        },
    }

    try:
        async with httpx.AsyncClient() as request_client:
            response = await request_client.post(
                url, json=payload, headers=headers, timeout=60
            )
        response.raise_for_status()
        return response

    except httpx.RequestError as exc:
        raise RuntimeError(f"Error occurred while requesting chat API: {exc}")

    except httpx.HTTPStatusError as exc:
        raise ValueError(
            f"Error response {exc.response.status_code} from chat API: {exc.response.text}"
        )


async def assign_participant_to_chat(
    assign_participant_to_chat: AssignParticipantChatInput,
) -> ConversationType | ConversationDneError:
    """Add participant to chat."""
    url = f"{settings.CHAT_API_HOST}/graphql"
    api_key = settings.CHAT_API_KEY

    try:
        cohort = await Cohort.filter(
            id=assign_participant_to_chat.cohort_id
        ).first()
        admin = await Authorized.filter(
            id=assign_participant_to_chat.participant_id
        ).first()

        logger.info(f"Cohort unique_name: {cohort.unique_name}")
        logger.info(f"Admin chat_identity: {admin.chat_identity}")

        admin_ciba_api_id = str(admin.api_id)
        admin_id = str(admin.id)

        # First attempt with ciba_api_id if available
        result = await add_to_group_chat_request(
            url,
            api_key,
            cohort.unique_name,
            assign_participant_to_chat.type,
            admin_ciba_api_id,
            admin.chat_identity,
        )
        response_json = result.json()

        if result.status_code != 200 or "errors" in response_json:
            logger.warning(
                f"Participant does not exist with ciba_api_id. Trying again with admin.id: {admin_id}"
            )

            # Second attempt with admin.id
            result = await add_to_group_chat_request(
                url,
                api_key,
                cohort.unique_name,
                assign_participant_to_chat.type,
                admin_id,
                admin.chat_identity,
            )
            response_json = result.json()

            # Check the result of the second attempt
            if result.status_code != 200 or "errors" in response_json:
                logger.error(
                    f"Could not assign participant to chat group using admin.id: {admin_id}. "
                    f"Status code: {result.status_code}. Content: {result.content}"
                )
                raise FailedToAssignParticipantToChat()

        # Process the successful response
        if response_json["data"]:
            response_data = response_json["data"].get(
                "addParticipantToConversation"
            )
            if response_data:
                return (
                    ConversationDneError(
                        message=response_data.get(
                            "message", "Conversation does not exist."
                        )
                    )
                    if "message" in response_data
                    else ConversationType(
                        id=response_data.get("id"),
                        type=response_data.get("type"),
                        sid=response_data.get("sid"),
                        unique_name=response_data.get("friendlyName"),
                        friendly_name=response_data.get("friendlyName"),
                        created_at=response_data.get("createdAt"),
                        updated_at=response_data.get("updatedAt"),
                    )
                )
        else:
            error_message = response_json["errors"][0].get("message", "")
            logger.error(
                f"Could not add admin to chat group {admin.id}: {error_message}. "
                f"Status code: {result.status_code}. Content: {result.content}"
            )
            raise ValueError(
                f"Could not add admin to chat group: {admin.email}, please try again. Error: {error_message}"
            )

        logger.error(
            f"Unexpected error while adding admin to chat. "
            f"Status code: {result.status_code}. Content: {result.content}"
        )
        raise FailedToAssignParticipantToChat()

    except Exception as e:
        logger.exception(f"Error during participant assignment: {e}")
        raise ValueError(f"Error during participant assignment: {e}")


async def update_participant_email(
    self, participant_id: str, email: str
) -> SimpleRespType:
    try:
        logger.info(f"Updating participant email: {participant_id}->{email}")
        result = await ParticipantRepository.update_email(
            participant_id, email
        )
        return SimpleRespType(status=result)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


@strawberry.type
class ParticipantMutation:
    create_participant: ParticipantType = strawberry.field(
        resolver=create_participant
    )
    update_participant: ParticipantType = strawberry.field(
        resolver=update_participant
    )
    update_participant_email: SimpleRespType = strawberry.field(
        resolver=update_participant_email
    )
    delete_participant: SimpleRespType = strawberry.field(
        resolver=delete_participant
    )
    create_solera_participant: SoleraParticipantType = strawberry.field(
        resolver=create_solera_participant
    )
    update_solera_participant: SoleraParticipantType = strawberry.field(
        resolver=update_solera_participant
    )
    delete_solera_participant: SimpleRespType = strawberry.field(
        resolver=delete_solera_participant
    )
    create_heads_up_participant: HeadsUpParticipantType = strawberry.field(
        resolver=create_heads_up_participant
    )
    update_heads_up_participant: HeadsUpParticipantType = strawberry.field(
        resolver=update_heads_up_participant
    )
    delete_heads_up_participant: SimpleRespType = strawberry.field(
        resolver=delete_heads_up_participant
    )
    create_participant_meta: ParticipantMetaType = strawberry.field(
        resolver=create_participant_meta
    )
    update_participant_meta: ParticipantMetaType = strawberry.field(
        resolver=update_participant_meta
    )
    delete_participant_meta: SimpleRespType = strawberry.field(
        resolver=delete_participant_meta
    )
    create_authorized: AuthorizedType = strawberry.field(
        resolver=create_authorized
    )
    update_authorized: AuthorizedType = strawberry.field(
        resolver=update_authorized
    )
    assign_participant_to_chat: ConversationType | ConversationDneError = (
        strawberry.field(resolver=assign_participant_to_chat)
    )
    merge_latest_data: ShortResponse = strawberry.field(
        resolver=merge_latest_data
    )
