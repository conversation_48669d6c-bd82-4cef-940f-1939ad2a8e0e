from uuid import UUID

from ciba_participant.participant.models import Participant
from ciba_participant.rpm_api.api import sync_measures
from ciba_participant.rpm_api.exceptions import RPMCallError

from src.common import messages
from src.common.types import ShortResponse


async def merge_latest_data(participant_id: UUID) -> ShortResponse:
    """
    Resolver that initiates a sync of the latest participant data from the RPM API.
    """
    requested_participant = await Participant.filter(
        id=participant_id
    ).get_or_none()

    if not requested_participant:
        return ShortResponse(
            success=False, error=messages.PARTICIPANT_NOT_FOUND
        )

    try:
        processing_result = await sync_measures(participant_id)

        if processing_result.success:
            return ShortResponse(success=True)

        return ShortResponse(
            success=False,
            error=messages.SYNC_REQUEST_FAILED,
        )
    except RPMCallError as error:
        return ShortResponse(success=False, error=str(error))
