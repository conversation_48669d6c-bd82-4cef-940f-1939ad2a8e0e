import json
from dataclasses import dataclass
from datetime import datetime, timezone
from uuid import UUID

import pendulum
from asyncstdlib import cached_property as async_cached_property
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.participant.crud import (
    ParticipantMetaRepository,
)
from ciba_participant.participant.models import Participant
from ciba_participant.program.models import ProgramModule
from src.log.logging import logger

from src.program.types import ProgramModuleType
from src.participant.inputs import ParticipantIDInput
from src.participant.types import (
    AddressType,
    HeightType,
    ParticipantType,
    StatusInCohort,
)
from ciba_participant.rpm_api.api import get_devices_status
from ciba_participant.rpm_api.models import DeviceStatus

from dateutil import parser

SPECIAL_DATE_FORMAT_CASES = [
    "%Y-%d-%m",
    "%Y/%d/%m",
]


def parse_date(date: str) -> datetime | None:
    try:
        # dateutil.parser is able to parse dates in a variety of formats
        return parser.parse(date, fuzzy=True)
    except Exception:
        # If the date is not in a standard format, try some special cases
        for format in SPECIAL_DATE_FORMAT_CASES:
            try:
                return datetime.strptime(date, format)
            except ValueError:
                continue
            except Exception as e:
                logger.exception(e)
        return None


@dataclass
class ProgramModuleDates:
    id: UUID
    started_at: datetime
    ended_at: datetime


async def get_current_module(
    modules_dates: list[ProgramModuleDates],
) -> CohortProgramModules | None:
    today = datetime.now(timezone.utc)

    current_module_id = None

    for module in modules_dates:
        if module.started_at <= today < module.ended_at:
            current_module_id = module.id
            break

    if current_module_id:
        return await CohortProgramModules.get_or_none(
            id=current_module_id
        ).prefetch_related("program_module")

    return None


@dataclass
class ParticipantCohortInfo:
    participant_id: UUID

    @async_cached_property
    async def program_module_dates(self) -> list[ProgramModuleDates]:
        return [
            ProgramModuleDates(*m)
            for m in await CohortProgramModules.filter(
                cohort__participants__id=self.participant_id,
            )
            .order_by("started_at")
            .values_list("id", "started_at", "ended_at")
        ]

    @async_cached_property
    async def cohort_start_date(self) -> datetime | None:
        try:
            return min(
                await self.program_module_dates, key=lambda x: x.started_at
            ).started_at
        except ValueError:
            return None

    @async_cached_property
    async def cohort_end_date(self) -> datetime | None:
        try:
            return max(
                await self.program_module_dates, key=lambda x: x.ended_at
            ).ended_at
        except ValueError:
            return None

    @async_cached_property
    async def current_cohort_program_module(
        self,
    ) -> CohortProgramModules | None:
        now = datetime.now(timezone.utc)

        current_module_id = None

        for module in await self.program_module_dates:
            if module.started_at <= now < module.ended_at:
                current_module_id = module.id
                break

        if current_module_id:
            return await CohortProgramModules.get_or_none(
                id=current_module_id
            ).prefetch_related("program_module")

        return None

    @async_cached_property
    async def current_program_module(self) -> ProgramModule | None:
        if (
            current_cohort_program_module
            := await self.current_cohort_program_module
        ):
            return current_cohort_program_module.program_module

        return None

    @async_cached_property
    async def status(self) -> StatusInCohort:
        now = datetime.now(timezone.utc)

        if len(await self.program_module_dates) == 0:
            return StatusInCohort.INACTIVE

        cohort_start_date = await self.cohort_start_date
        if cohort_start_date and now < cohort_start_date:
            return StatusInCohort.AWAITING

        cohort_end_date = await self.cohort_end_date
        if cohort_end_date and now > cohort_end_date:
            return StatusInCohort.FINISHED

        return StatusInCohort.ACTIVE

    @async_cached_property
    async def current_week(self) -> int | None:
        status = await self.status
        if status != StatusInCohort.ACTIVE:
            return None

        start_date = await self.cohort_start_date
        if start_date is None:
            return None

        today = datetime.now(timezone.utc).date()
        current_week = (today - start_date.date()).days // 7
        return current_week


@dataclass
class ParticipantMeta:
    participant_id: UUID

    @async_cached_property
    async def metadata(self) -> dict:
        participant_meta = (
            await ParticipantMetaRepository.get_participant_meta(
                self.participant_id
            )
        )

        if not participant_meta:
            return {}

        metadata = participant_meta.metadata

        match metadata:
            case dict():
                return metadata
            case str():
                try:
                    return json.loads(metadata)
                except json.JSONDecodeError:
                    return {}
            case _:
                return {}

    @async_cached_property
    async def phone(self) -> str | None:
        metadata = await self.metadata
        phone = metadata.get("phone_number")
        return phone

    @async_cached_property
    async def address(self) -> AddressType | None:
        metadata = await self.metadata
        address = metadata.get("address")

        match address:
            case dict():
                return AddressType(
                    city=address.get("city", ""),
                    state=address.get("state", ""),
                    street_1=address.get("street1", ""),
                    street_2=address.get("street2", ""),
                    zip_code=address.get("zipCode", ""),
                )
            case _:
                return None

    @async_cached_property
    async def birthdate(self) -> datetime | None:
        metadata = await self.metadata
        birthdate = metadata.get("birthdate")

        if not birthdate:
            return None

        return parse_date(birthdate)

    @async_cached_property
    async def age(self) -> int | None:
        birthdate = await self.birthdate

        if not birthdate:
            return None

        return pendulum.instance(birthdate).age

    @async_cached_property
    async def height(self) -> HeightType | None:
        metadata = await self.metadata
        height_feet = metadata.get("heightFeet")
        height_inches = metadata.get("heightInches", 0)

        if height_feet is None:
            return None

        return HeightType(feet=height_feet, inches=height_inches)

    @async_cached_property
    async def initial_weight(self) -> float | None:
        metadata = await self.metadata
        return metadata.get("user_initial_weight", None)

    @async_cached_property
    async def user_reported_weight(self) -> float | None:
        metadata = await self.metadata
        return metadata.get("user_reported_weight", None)

    @async_cached_property
    async def user_target_weight(self) -> float | None:
        metadata = await self.metadata
        return metadata.get("user_target_weight", None)

    @async_cached_property
    async def gender(self) -> str | None:
        metadata = await self.metadata
        return metadata.get("gender")

    @async_cached_property
    async def starting_date(self) -> datetime | None:
        metadata = await self.metadata
        starting_date = metadata.get("commitmentDate")

        if not starting_date:
            return None

        return parse_date(starting_date)

    @async_cached_property
    async def disenrolled_reason(self) -> str | None:
        metadata = await self.metadata
        return metadata.get("disenrolledReason")

    @async_cached_property
    async def disenrollment_date(self) -> datetime | None:
        metadata = await self.metadata
        disenrollment_date = metadata.get("disenrollmentDate")

        if not disenrollment_date:
            return None

        return parse_date(disenrollment_date)

    @async_cached_property
    async def solera_key(self) -> str | None:
        metadata = await self.metadata
        return metadata.get("enrollmentId")


async def get_participant(data: ParticipantIDInput) -> ParticipantType | None:
    try:
        participant: (
            Participant | None
        ) = await Participant.all_participants.get(id=data.id)

        if not participant:
            return None

        cohort_info = ParticipantCohortInfo(participant_id=participant.id)

        metadata = ParticipantMeta(participant_id=participant.id)

        cohort_program_module = await cohort_info.current_cohort_program_module
        program_module = await cohort_info.current_program_module

        output_module = None

        devices_status: list[DeviceStatus] = await get_devices_status(
            participant_id=data.id
        )

        if cohort_program_module and program_module:
            output_module = ProgramModuleType(
                id=program_module.id,
                created_at=program_module.created_at,
                updated_at=program_module.updated_at,
                title=program_module.title,
                short_title=program_module.short_title,
                length=program_module.length,
                description=program_module.description,
                program_id=program_module.program_id,
                order=program_module.order,
                started_at=cohort_program_module.started_at,
                ended_at=cohort_program_module.ended_at,
            )

        participant_output = ParticipantType(
            id=participant.id,
            email=participant.email,
            first_name=participant.first_name,
            last_name=participant.last_name,
            chat_identity=participant.chat_identity,
            group_id=participant.group_id,
            member_id=participant.member_id,
            solera_careplan_id=await metadata.solera_key,
            status=participant.status,
            status_in_cohort=await cohort_info.status,
            cognito_sub=participant.cognito_sub,
            medical_record=participant.medical_record,
            is_test=participant.is_test,
            last_reset=participant.last_reset,
            created_at=participant.created_at,
            updated_at=participant.updated_at,
            activities=participant.activities.order_by("-created_at"),
            starting_date=await metadata.starting_date,
            age=await metadata.age,
            gender=await metadata.gender,
            current_module=output_module,
            address=await metadata.address,
            birthdate=await metadata.birthdate,
            initial_weight=await metadata.initial_weight,
            height=await metadata.height,
            phone=await metadata.phone,
            current_week=await cohort_info.current_week,
            disenrolled_reason=await metadata.disenrolled_reason,
            disenrollment_date=await metadata.disenrollment_date,
            user_reported_weight=await metadata.user_reported_weight,
            user_target_weight=await metadata.user_target_weight,
            devices_status=devices_status,
        )

        return participant_output
    except Exception as e:
        logger.exception(e)
        return None
