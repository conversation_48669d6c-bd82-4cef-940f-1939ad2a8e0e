from uuid import UUID

from ciba_participant.participant.models import Participant
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.measures import get_latest_data

from src.common import messages
from src.participant.types import DeviceInfo, LatestMeasures, Measure


async def get_latest_measures(participant_id: UUID) -> LatestMeasures:
    """
    Resolver to handle get last measures from external services.
    @param participant_id: Participant ID
    """
    requested_participant = await Participant.filter(
        id=participant_id
    ).get_or_none()

    if not requested_participant:
        return LatestMeasures(
            success=False, error=messages.PARTICIPANT_NOT_FOUND
        )

    try:
        latest_data = await get_latest_data(participant_id)

        return LatestMeasures(
            success=True,
            last_ciba_sync=latest_data.last_ciba_sync,
            last_device_sync=latest_data.last_device_sync,
            devices=[
                DeviceInfo(
                    id=device.id,
                    device_type=device.device_type,
                    last_synced_at=device.last_synced_at,
                )
                for device in latest_data.devices
            ],
            measures=[
                Measure(
                    value=measure.value,
                    unit=measure.unit,
                    created_at=measure.created_at,
                )
                for measure in latest_data.measures
            ],
        )
    except RPMCallError as error:
        return LatestMeasures(success=False, error=str(error))
