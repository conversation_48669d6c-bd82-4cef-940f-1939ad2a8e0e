from dataclasses import dataclass
from datetime import datetime
from enum import StrEnum
from uuid import UUID

import strawberry
from typing import Optional

from ciba_participant.rpm_api.models import DeviceStatus
from src.log.logging import logger
from ciba_participant.participant.crud import (
    ParticipantRepository,
    SoleraParticipantRepository,
    HeadsUpParticipantRepository,
    ParticipantMetaRepository,
    AuthorizedRepository,
)
from ciba_participant.chat_api.chat_api import get_token

from src.participant.types import (
    ParticipantType,
    SoleraParticipantType,
    HeadsUpParticipantType,
    ParticipantMetaType,
    ParticipantsListType,
    SoleraParticipantsListType,
    HeadsUpParticipantsListType,
    ParticipantMetasListType,
    TokenType,
    ParticipantDneError,
    ParticipantNotInConversationError,
    LatestMeasures,
)
from src.auth.types import AuthorizedType, AuthorizedListType
from src.participant.inputs import (
    ParticipantIDInput,
    SoleraParticipantIDInput,
    HeadsUpParticipantIDInput,
    ParticipantMetaIDInput,
)
from src.settings import get_settings
from src.participant.queries.get_latest_measures import get_latest_measures
from .get_paginated_participants import (
    ParticipantsInfoListType,
    get_paginated_participants,
)
from .get_participant import get_participant
from .get_participant_classes_progress import (
    ParticipantClassProgressType,
    get_participant_classes_progress,
)
from .get_participant_module_progress import (
    ParticipantModuleProgressType,
    get_participant_module_progress,
)
from .get_participant_program_progress import (
    get_participant_program_progress,
    ParticipantProgramProgressType,
)
from .get_participant_activity_stats import (
    get_participant_activity_stats,
    ParticipantActivityStats,
)
from .get_participant_devices import get_participant_devices

settings = get_settings()


async def get_participants() -> ParticipantsListType:
    try:
        participants = await ParticipantRepository.get_participants(1, 500)
        return ParticipantsListType(participants=participants.participants)
    except Exception as e:
        logger.exception(e)
        return ParticipantsListType(participants=[])


def parse_date(date: str, formats: list[str]) -> Optional[datetime]:
    for format in formats:
        try:
            return datetime.strptime(date, format)
        except ValueError:
            continue
        except Exception as e:
            logger.exception(e)
            return None


@strawberry.enum
class StatusInCohort(StrEnum):
    INACTIVE = "inactive"
    AWAITING = "awaiting"
    ACTIVE = "active"
    FINISHED = "finished"


@dataclass
class ProgramModuleDates:
    id: UUID
    started_at: datetime
    ended_at: datetime


async def get_solera_participants() -> SoleraParticipantsListType:
    try:
        solera_participants = (
            await SoleraParticipantRepository.get_solera_participants(1, 10)
        )
        return solera_participants
    except Exception as e:
        logger.exception(e)
        return SoleraParticipantsListType(participants=[])


async def get_solera_participant(
    data: SoleraParticipantIDInput,
) -> Optional[SoleraParticipantType]:
    try:
        solera_participant = (
            await SoleraParticipantRepository.get_solera_participant(
                data.participant_id
            )
        )
        return solera_participant
    except Exception as e:
        logger.exception(e)
        return None


async def get_heads_up_participants() -> HeadsUpParticipantsListType:
    try:
        heads_up_participants = (
            await HeadsUpParticipantRepository.get_heads_up_participants(1, 10)
        )
        return heads_up_participants
    except Exception as e:
        logger.exception(e)
        return HeadsUpParticipantsListType(participants=[])


async def get_heads_up_participant(
    data: HeadsUpParticipantIDInput,
) -> Optional[HeadsUpParticipantType]:
    try:
        heads_up_participant = (
            await HeadsUpParticipantRepository.get_heads_up_participant(
                data.id
            )
        )
        return heads_up_participant
    except Exception as e:
        logger.exception(e)
        return None


async def get_participant_metas() -> ParticipantMetasListType:
    try:
        participant_metas = (
            await ParticipantMetaRepository.get_participant_metas(1, 10)
        )
        return participant_metas
    except Exception as e:
        logger.exception(e)
        return []


async def get_participant_meta(
    data: ParticipantMetaIDInput,
) -> Optional[ParticipantMetaType]:
    try:
        participant_meta = (
            await ParticipantMetaRepository.get_participant_meta(data.id)
        )
        return participant_meta
    except Exception as e:
        logger.exception(e)
        return None


async def list_authorized() -> AuthorizedListType:
    try:
        authorized = await AuthorizedRepository.list_authorized()
        return AuthorizedListType(authorized=authorized)
    except Exception as e:
        logger.exception(e)
        return AuthorizedListType(authorized=[])


async def get_authorized(data: ParticipantIDInput) -> Optional[AuthorizedType]:
    try:
        authorized = await AuthorizedRepository.get_authorized(data.id)
        return authorized
    except Exception as e:
        logger.exception(e)
        return None


async def get_chat_token(
    chat_identity: str, admin_id: str = None
) -> TokenType | ParticipantDneError | ParticipantNotInConversationError:
    """Get chat token for a participant.

    Attributes:
      chat_identity (str): Chat identity of the participant.
      admin_id (str): Admin ID of the participant, which is also cognito sub

    """

    result, errors = await get_token(
        chat_identity=chat_identity, admin_id=admin_id
    )

    if errors:
        logger.error(f"Failed to get token. Error: {errors}")
        raise ValueError(f"Could not get token from chat API: Error: {errors}")

    result = result.json()
    data = result.get("data", None)
    if not data:
        return ParticipantNotInConversationError(
            message="No data found in response"
        )

    token = data.get("token", None)
    if not token:
        return ParticipantNotInConversationError(
            message="No token found in response"
        )

    message = token.get("message", None)
    if message:
        return ParticipantNotInConversationError(message=message)

    return TokenType(chat_identity=token["chatIdentity"], token=token["token"])


@strawberry.type
class ParticipantQuery:
    get_participants: ParticipantsListType = strawberry.field(
        resolver=get_participants
    )
    get_participant: ParticipantType | None = strawberry.field(
        resolver=get_participant
    )
    get_solera_participants: SoleraParticipantsListType = strawberry.field(
        resolver=get_solera_participants
    )
    get_solera_participant: SoleraParticipantType = strawberry.field(
        resolver=get_solera_participant
    )
    get_heads_up_participants: HeadsUpParticipantsListType = strawberry.field(
        resolver=get_heads_up_participants
    )
    get_heads_up_participant: HeadsUpParticipantType = strawberry.field(
        resolver=get_heads_up_participant
    )
    get_participant_metas: ParticipantMetasListType = strawberry.field(
        resolver=get_participant_metas
    )
    get_participant_meta: ParticipantMetaType = strawberry.field(
        resolver=get_participant_meta
    )
    list_authorized: AuthorizedListType = strawberry.field(
        resolver=list_authorized
    )
    get_authorized: AuthorizedType = strawberry.field(resolver=get_authorized)
    get_chat_token: (
        TokenType | ParticipantDneError | ParticipantNotInConversationError
    ) = strawberry.field(resolver=get_chat_token)
    get_paginated_participants: ParticipantsInfoListType = strawberry.field(
        resolver=get_paginated_participants
    )
    get_participant_module_progress: ParticipantModuleProgressType | None = (
        strawberry.field(resolver=get_participant_module_progress)
    )
    get_participant_program_progress: (
        list[ParticipantProgramProgressType] | None
    ) = strawberry.field(resolver=get_participant_program_progress)
    get_participant_activity_stats: ParticipantActivityStats | None = (
        strawberry.field(resolver=get_participant_activity_stats)
    )
    get_participant_classes_progress: (
        list[ParticipantClassProgressType] | None
    ) = strawberry.field(resolver=get_participant_classes_progress)
    get_latest_measures: LatestMeasures = strawberry.field(
        resolver=get_latest_measures
    )
    get_participant_devices: list[DeviceStatus] = strawberry.field(
        resolver=get_participant_devices
    )
