from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from src.content_library.enums import (
    MaterialActivityEnum,
    MaterialStatusEnum,
    MaterialTagEnum,
)


@strawberry.type
class MaterialCreationResponse:
    id: Optional[UUID] = strawberry.field(default=None)
    success: bool
    error: Optional[str] = strawberry.field(default=None)
    upload_url: Optional[str] = strawberry.field(default=None)
    fields: Optional[str] = strawberry.field(default=None)


@strawberry.type
class MaterialSimpleResponse:
    id: Optional[UUID] = strawberry.field(default=None)
    success: bool
    error: Optional[str] = strawberry.field(default=None)


@strawberry.type
class RelatedProgram:
    id: UUID
    title: str


@strawberry.type
class ContentMaterialElement:
    id: UUID
    added_at: datetime
    mime_type: str
    title: str
    description: str
    status: MaterialStatusEnum
    programs: list[RelatedProgram]
    tags: list[MaterialTagEnum]
    activity_types: list[MaterialActivityEnum]
    link: Optional[str]
    link_expiration: Optional[datetime] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    form_id: Optional[str] = None


@strawberry.type
class ContentMaterialList:
    success: bool
    error: Optional[str] = strawberry.field(default=None)
    items: list[ContentMaterialElement]
    total: int
    total_pages: int
