from typing import Optional

import strawberry

from src.content_library.exceptions import MaterialQueryException
from src.content_library.inputs import MaterialFilters
from src.content_library.queries.get_content import get_content
from src.content_library.types import ContentMaterialList
from src.log.logging import logger


async def get_content_materials(
    page: int = 1,
    per_page: int = 10,
    filters: Optional[MaterialFilters] = None,
) -> ContentMaterialList:
    """
    Resolver to retrieve content materials.
    """
    logger.info(
        "Retrieving content materials: page %d, items per page %d, and filters %s",
        page,
        per_page,
        filters,
    )

    try:
        return await get_content(page=page, per_page=per_page, filters=filters)
    except (ValueError, MaterialQueryException) as error:
        return ContentMaterialList(
            success=False,
            error=str(error),
            items=[],
            total=0,
            total_pages=1,
        )


@strawberry.type()
class ContentLibraryQuery:
    """Content Library queries."""

    get_content_material: ContentMaterialList = strawberry.field(
        resolver=get_content_materials
    )
