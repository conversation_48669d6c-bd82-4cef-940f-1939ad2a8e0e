from typing import Optional, List
from uuid import UUID

import strawberry

from src.content_library.enums import (
    MaterialActivityEnum,
    MaterialTagEnum,
    MaterialStatusEnum,
)


@strawberry.input
class MaterialData:
    title: str
    description: str
    mime_type: str
    content_url: Optional[str] = strawberry.field(default=None)
    file_name: Optional[str] = strawberry.field(default=None)
    file_size: Optional[int] = strawberry.field(default=0)
    form_id: Optional[str] = strawberry.field(default=None)
    activity_types: List[MaterialActivityEnum]
    tags: List[MaterialTagEnum]
    programs: List[UUID]


@strawberry.input
class MaterialFilters:
    tags: Optional[List[MaterialTagEnum]] = strawberry.field(default=None)
    activity_types: Optional[List[MaterialActivityEnum]] = strawberry.field(
        default=None
    )
    programs: Optional[List[UUID]] = strawberry.field(default=None)
    status: Optional[MaterialStatusEnum] = strawberry.field(default=None)
    search: Optional[str] = strawberry.field(default=None)
