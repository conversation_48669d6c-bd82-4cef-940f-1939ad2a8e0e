from uuid import UUID

import strawberry

from ciba_participant.classes.crud import LiveSessionRepository

from src.log.logging import logger
from src.webinar.queries.get_creators import get_classes_creators
from src.webinar.queries.get_sessions import get_live_sessions
from src.webinar.queries.get_webinars import get_webinars
from src.webinar.queries.get_webinar import get_webinar
from src.webinar.types import (
    ClassCreatorType,
    LiveSessionListType,
    ParticipantType,
    WebinarListType,
    WebinarType,
)


async def get_participants_per_session(
    live_session_id: UUID,
) -> list[ParticipantType]:
    """
    Retrieve a list of participants for a specified live session.

    This asynchronous function fetches and returns a list of participants
    associated with a given live session. The function takes a live session
    identifier as input and provides detailed participant information in an
    organized structure.
    """
    participants = (
        await LiveSessionRepository.get_participants_by_live_session_id(
            live_session_id
        )
    )
    logger.info(
        f"Fetched {len(participants)} participants for live session {live_session_id}:"
    )

    return [
        ParticipantType(**participant.model_dump(), activities=None)
        for participant in participants
    ]


@strawberry.type()
class WebinarQuery:
    """Webinars graphql queries."""

    get_classes_creators: ClassCreatorType = strawberry.field(
        resolver=get_classes_creators
    )
    get_webinars: WebinarListType = strawberry.field(resolver=get_webinars)
    get_webinar: WebinarType = strawberry.field(resolver=get_webinar)
    get_participants_per_session: list[ParticipantType] = strawberry.field(
        resolver=get_participants_per_session
    )
    get_live_sessions: LiveSessionListType = strawberry.field(
        resolver=get_live_sessions
    )
