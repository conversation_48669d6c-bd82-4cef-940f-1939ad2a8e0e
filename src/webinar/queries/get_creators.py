from ciba_participant.classes.crud import WebinarRepository
from tortoise.exceptions import BaseORMException

from ciba_participant.participant.models import AutorizedRole
from src.content_library.messages import DB_READ_ERROR
from src.log.logging import logger
from src.webinar.types import ProviderType, ClassCreatorType


async def get_classes_creators() -> ClassCreatorType:
    """
    Method to get all classes creators.
    """
    try:
        creators = await WebinarRepository.get_creators()

        return ClassCreatorType(
            success=True,
            creators=[
                ProviderType(
                    id=creator.id,
                    full_name=creator.full_name(),
                    first_name=creator.first_name,
                    last_name=creator.last_name,
                    email=creator.email,
                    chat_identity=creator.chat_identity,
                    is_admin=creator.role == AutorizedRole.ADMIN,
                )
                for creator in creators
            ],
        )
    except BaseORMException as error:
        logger.exception(error)
        return ClassCreatorType(
            success=False,
            error=DB_READ_ERROR,
            creators=[],
        )
