from ciba_participant.dates.service import (
    get_recurrent_dates,
    get_interval_and_frequency,
)
from zoneinfo import ZoneInfo

from src.webinar.inputs import RecurrentDatesInput
from src.webinar.types import (
    PreviewDateListType,
    PreviewDateType,
)


async def get_preview_dates(data: RecurrentDatesInput) -> PreviewDateListType:
    starting_from = data.starting_from

    interval, frequency = get_interval_and_frequency(data.recurrence)

    if starting_from.tzinfo is None:
        starting_from = starting_from.replace(tzinfo=ZoneInfo(data.timezone))

    dates_info = list(
        get_recurrent_dates(
            count=data.count,
            frequency=frequency,
            interval=interval,
            starting_from=starting_from,
        )
    )

    preview_dates = []

    for date_info in dates_info:
        preview_dates.append(
            PreviewDateType(
                date=date_info.date,
                has_conflict=date_info.has_conflict,
            )
        )

    return PreviewDateListType(dates=preview_dates)
