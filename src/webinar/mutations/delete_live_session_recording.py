from uuid import UUID

from ciba_participant.classes.crud import LiveSessionRepository
from src.log.logging import logger

from src.common.types import SimpleRespType


async def delete_live_session_recording(
    live_session_id: UUID,
) -> SimpleRespType:
    try:
        logger.info(
            f"Deleting recording url for live session {live_session_id}"
        )

        await LiveSessionRepository.delete_live_session_recording(
            live_session_id
        )

        return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)
