from uuid import UUID
from src.log.logging import logger

from ciba_participant.classes.crud import LiveSessionRepository
from src.common.types import SimpleRespType


async def upload_video_recording(
    live_session_id: UUID, video_url: str
) -> SimpleRespType:
    try:
        logger.info(f"Uploading recording for live session {live_session_id}")

        await LiveSessionRepository.upload_live_session_recording(
            live_session_id, video_url
        )
        logger.info(f"Uploaded recording for live session {live_session_id}")

        return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)
