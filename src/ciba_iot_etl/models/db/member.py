from datetime import datetime

from typing import Optional

from uuid import uuid4

from tortoise import Model
from tortoise.fields import <PERSON><PERSON><PERSON><PERSON>, ReverseRelation, UUI<PERSON>ield

from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.db.base import DeviceType


class Member(Model, TimestampMixin):
    """Table to store Member data."""

    id = UUIDField(pk=True, default=uuid4)
    email = CharField(max_length=255, unique=True)

    # Define a reverse relation
    platforms = ReverseRelation["MemberPlatform"]
    state = ReverseRelation["MemberState"]
    withings = ReverseRelation["Withings"]
    fitbit = ReverseRelation["Fitbit"]
    dexcom = ReverseRelation["Dexcom"]
    devices = ReverseRelation["MemberDevice"]
    weights = ReverseRelation["Weight"]
    heart_rates = ReverseRelation["HeartRate"]
    blood_pressures = ReverseRelation["BloodPressure"]
    sleep = ReverseRelation["Sleep"]
    activities = ReverseRelation["Activity"]
    glucose_levels = ReverseRelation["GlucoseLevel"]

    @staticmethod
    async def get_by_platform(platform_type: str, platform_id: str) -> Model | None:
        """Check if user exists by external platform type and id"""
        return await Member.filter(
            platforms__platform_type=platform_type,
            platforms__platform_id=platform_id,
        ).get_or_none()

    async def get_withings_access_token(self) -> str:
        """Return withings access token if exists"""
        withings_instance = await self.withings.first()
        if withings_instance:
            return withings_instance.access_token
        return None

    async def get_withings_refresh_token(self) -> str:
        """Return withings access token if exists"""
        withings_instance = await self.withings.first()
        if withings_instance:
            return withings_instance.refresh_token
        return None

    async def get_fitbit_access_token(self) -> str:
        """Return fitbit access token if exists"""
        fitbit_instance = await self.fitbit.first()
        if fitbit_instance:
            return fitbit_instance.access_token
        return None

    async def get_fitbit_refresh_token(self) -> str:
        """Return fitbit refresh token if exists"""
        fitbit_instance = await self.fitbit.first()
        if fitbit_instance:
            return fitbit_instance.refresh_token
        return None

    async def get_withings_id(self) -> str:
        """Return withings id if exists"""
        withings_instance = await self.withings.first()
        if withings_instance:
            return str(withings_instance.id)
        return None

    async def get_withings_user_id(self) -> str:
        "Return unique withings account user_id"
        withings_instance = await self.withings.first()
        if withings_instance:
            return str(withings_instance.user_id)
        return None

    async def get_fitbit_id(self) -> str:
        """Return fitbit id if exists"""
        fitbit_instance = await self.fitbit.first()
        if fitbit_instance:
            return str(fitbit_instance.id)
        return None

    async def get_fitbit_user_id(self) -> str:
        """Return unique fitbit account user_id"""
        fitbit_instance = await self.fitbit.first()
        if fitbit_instance:
            return str(fitbit_instance.user_id)
        return None

    async def delete_withings(self) -> int:
        """Delete related Withings models, return a count of deleted models"""
        return await self.withings.delete()

    async def delete_fitbit(self) -> int:
        """Delete related Fitbit models, return a count of deleted models"""
        return await self.fitbit.delete()

    async def get_platforms(self) -> list:
        """Retutn platforms id and type"""
        platforms = await self.platforms.all()
        return [
            {"type": platform.platform_type, "id": str(platform.platform_id)}
            for platform in platforms
        ]

    async def update_withings_credentional(self, refresh_token: str, access_token: str):
        """Save new access and refresh token"""
        withings_instance = await self.withings.first()
        if withings_instance is None:
            return

        withings_instance.refresh_token = refresh_token
        withings_instance.access_token = access_token
        await withings_instance.save()

    async def update_fitbit_credentials(self, refresh_token: str, access_token: str):
        """Save new access and refresh token"""
        fitbit_instance = await self.fitbit.first()
        if fitbit_instance is None:
            return

        fitbit_instance.refresh_token = refresh_token
        fitbit_instance.access_token = access_token
        await fitbit_instance.save()

    async def withings_user_valid(self, userid: str) -> bool:
        """validate user_id"""
        withings_instance = await self.withings.first()
        if withings_instance is None:
            return False
        return withings_instance.user_id == userid

    async def get_latest_fitbit_update(self) -> Optional[datetime]:
        """
        For each of the Activity, Sleep, HeartRate, and BloodPressure tables
        (all device=DeviceType.FITBIT), this method:

        1. Finds the last (most recent) record in each table by ordering
           descending on updated_at and taking the first row.
        2. Among those 'last records', returns the earliest updated_at
           (i.e. the 'oldest' among the last-updates).

        Returns None if there are no records across any of those tables.
        """

        # Fetch the reverse relations so we can easily query them
        await self.fetch_related(
            "activity",  # <-- Make sure these match your ReverseRelation names
            "sleep",
            "heart_rates",
            "blood_pressures",
        )

        # Collect updated_at timestamps from each table's "most recent" entry
        last_updates = []
        for related_manager in [
            self.activity,
            self.sleep,
            self.heart_rates,
            self.blood_pressures,
        ]:
            last_record = (
                await related_manager.filter(device=DeviceType.FITBIT)
                .order_by("-updated_at")
                .first()
            )
            if last_record:
                last_updates.append(last_record.updated_at)

        if not last_updates:
            # No data across any of the tables for this member with DeviceType.FITBIT
            return None

        # Return the earliest among the 'most recent' updated_at values
        return min(last_updates)

    class Meta:
        table = "members"  # Specify the table name explicitly
