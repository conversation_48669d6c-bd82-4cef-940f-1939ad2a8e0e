from uuid import uuid4

from loguru import logger
from tortoise.models import Model
from tortoise.fields import (
    <PERSON><PERSON><PERSON><PERSON>,
    CharEnumField,
    ForeignKeyField,
    JSONField,
    UUIDField,
)

from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekDeviceType,
    TranstekStatus,
)
from ciba_iot_etl.models.pydantic.common import Carrier, TRACKING_URLS


class Transtek(Model, TimestampMixin):
    id = UUIDField(pk=True, default=uuid4)
    device_id = CharField(max_length=12, unique=True)
    imei = CharField(max_length=15, unique=True)
    model = CharField(max_length=20)
    device_type = CharEnumField(TranstekDeviceType, max_length=20)
    tracking_number = CharField(max_length=40, null=True)
    carrier = CharEnumField(Carrier, max_length=20, null=True)
    timezone = CharField(max_length=40, null=True)
    last_status_report = JSONField(null=True)
    status = CharEnumField(TranstekStatus, max_length=20)

    member = ForeignKeyField(
        "models.Member", related_name="transtek", null=True, blank=True
    )

    @property
    def tracking_url(self) -> str:
        """
        Generate tracking URL for the device shipment.
        Returns empty string if tracking_number or carrier is not set.
        """
        if not self.tracking_number or not self.carrier:
            return ""

        url_template = TRACKING_URLS.get(self.carrier)
        if not url_template:
            logger.warning(
                f"Tracking URL template not found for carrier: {self.carrier}"
            )
            return ""

        return url_template.format(tracking_number=self.tracking_number)
