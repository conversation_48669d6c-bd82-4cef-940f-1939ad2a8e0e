import datetime
import json
import logging
import sys
from logging.config import dictConfig
from types import FrameType  # type: ignore
from typing import List, cast

import loguru
import stackprinter

from src.log.schemas import BaseJsonLogSchema
from src.settings import ENV, get_settings

settings = get_settings()

PASS_ROUTES = [
    "/openapi.json",
    "/docs",
    "/health",
    "/ready",
]

LEVEL_TO_NAME = {
    logging.CRITICAL: "CRITICAL",
    logging.ERROR: "ERROR",
    logging.WARNING: "WARNING",
    logging.INFO: "INFO",
    logging.DEBUG: "DEBUG",
    logging.NOTSET: "TRACE",
}

UVICORN_ACCESS = "uvicorn.access"


class ConsoleLogger(logging.Handler):
    """Custom console logger."""

    def emit(self, record: logging.LogRecord) -> None:
        """Publish logs using loguru."""
        # Get corresponding Loguru level if it exists
        try:
            level = loguru.logger.level(record.levelname).name
        except ValueError:
            level = str(record.levelno)

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:  # noqa: WPS609
            frame = cast(FrameType, frame.f_back)
            depth += 1

        # Retrieve correlation_id if available
        correlation_id = getattr(record, "correlation_id", "-")

        # Include correlation_id in the log message
        log_message = (
            f"[Correlation ID: {correlation_id}] {record.getMessage()}"
        )

        loguru.logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            log_message,
        )


class JSONLogFormatter(logging.Formatter):
    """Custom class-formatter for writing logs in json format."""

    def format(
        self,
        record: logging.LogRecord,
        *args: str,
        **kwargs: str,
    ) -> str:
        """Format LogRecord to json.

        :param record: logging.LogRecord
        :return: json string
        """
        log_object: dict = self._format_log_object(record)
        return json.dumps(log_object, ensure_ascii=False)

    @staticmethod
    def _format_log_object(record: logging.LogRecord) -> dict:
        now = (
            datetime.datetime.fromtimestamp(record.created)
            .replace(microsecond=0)
            .isoformat()
        )
        message = record.getMessage()
        duration = (
            record.duration  # type: ignore
            if hasattr(record, "duration")
            else record.msecs
        )

        # Add correlation_id if available
        correlation_id = getattr(record, "correlation_id", "-")

        json_log_fields = BaseJsonLogSchema(
            filename=record.filename,
            lineno=str(record.lineno),
            thread=record.process,
            timestamp=now,
            level=LEVEL_TO_NAME[record.levelno],
            message=message,
            source_log=record.name,
            duration=duration,
            app_name=settings.PROJECT_NAME,
            app_version=settings.VERSION,
            app_env=settings.ENV,
            correlation_id=correlation_id,
        )

        if hasattr(record, "props"):
            json_log_fields.props = record.props  # type: ignore
        if hasattr(record, "user_id"):
            json_log_fields.user_id = record.user_id  # type: ignore
        if record.exc_info:
            json_log_fields.exceptions = stackprinter.format(
                record.exc_info,
                suppressed_paths=[
                    r"lib/python.*/site-packages/starlette.*",
                ],
                suppressed_vars=[
                    r".*password.*",
                    r".*token.*",
                    r".*email.*",
                    r".*request.headers.*",
                    r".*first_name.*",
                    r".*last_name.*",
                    r".*firstName.*",
                    r".*lastName.*",
                    r".*phone.*",
                    r".*zip.*",
                    r".*address.*",
                ],
                add_summary=False,
            ).split("\n")

        elif record.exc_text:
            json_log_fields.exceptions = record.exc_text

        # Pydantic to dict
        json_log_object = json_log_fields.dict(
            exclude_unset=True,
            by_alias=True,
        )
        # getting additional fields
        if hasattr(record, "request_json_fields"):
            json_log_object.update(record.request_json_fields)  # type: ignore

        return json_log_object


def handlers(env: str) -> List[str]:
    """Define handler based on env."""
    if env.lower() in (ENV.PROD, ENV.DEV, ENV.STG):
        return ["json"]
    return ["intercept"]


LOG_HANDLER = handlers(settings.ENV)
LOGGING_LEVEL = logging.DEBUG if settings.DEBUG else logging.INFO

LOG_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "correlation_id": {
            "()": "asgi_correlation_id.CorrelationIdFilter",
            "uuid_length": 32,
            "default_value": "-",
        },
    },
    "formatters": {
        "json": {
            "()": JSONLogFormatter,
        },
    },
    "handlers": {
        "json": {
            "formatter": "json",
            "class": "logging.StreamHandler",
            "filters": ["correlation_id"],
            "stream": sys.stdout,
        },
        "intercept": {
            "()": ConsoleLogger,
            "filters": ["correlation_id"],
        },
    },
    "loggers": {
        "": {
            "handlers": LOG_HANDLER,
            "level": LOGGING_LEVEL,
        },
        "uvicorn": {
            "handlers": LOG_HANDLER,
            "level": LOGGING_LEVEL,
        },
        UVICORN_ACCESS: {
            "handlers": LOG_HANDLER,
            "level": LOGGING_LEVEL,
        },
    },
}


class EndpointFilter(logging.Filter):
    """Mute such logs as health, docs, ready to avoid spamming in datadog."""

    def filter(self, record: logging.LogRecord) -> bool:
        if record.name == UVICORN_ACCESS:
            #  mute logs for PASS_ROUTES
            return all(
                record.getMessage().find(path) == -1 for path in PASS_ROUTES
            )
        return True


# Filter out /health
logging.getLogger(UVICORN_ACCESS).addFilter(EndpointFilter())

dictConfig(LOG_CONFIG)
logger = logging.getLogger("main")
