from typing import Optional

from common.types import DetailedResponse


async def assign_transtek_tracking_number(
    tracking_number: str,
    carrier: str,
    imei: Optional[str] = None,
    serial_number: Optional[str] = None,
) -> DetailedResponse:
    """Add tracking number and tracking info to transtek scale."""
    # TODO: Implement update for Transtek device

    return DetailedResponse(success=True)
