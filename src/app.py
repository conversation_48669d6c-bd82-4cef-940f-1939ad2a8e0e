# app.py
import sentry_sdk
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from strawberry.fastapi import GraphQLRouter
from tortoise.contrib.fastapi import register_tortoise
from ciba_participant.common.db import get_tortoise_orm_config
from ciba_participant.helpers.timezone_middleware import (
    TimeZoneMiddleware,
    TIMEZONE_HEADER,
)

from src.cohort.queries.get_cohort_by_participant_id import (
    ParticipantNotFoundException,
    InactiveParticipantException,
)
from src.auth import InvalidTokenError
from src.auth.errors import MissingAuthorizationHeaderError
from src.context import get_context
from src.schema import schema
from src.settings import get_settings
from src.log.logging import logger

settings = get_settings()

IGNORE_ERRORS = [
    PermissionError,
    InvalidTokenError,
    ParticipantNotFoundException,
    InactiveParticipantException,
]

if settings.ENV != "prod":
    IGNORE_ERRORS = IGNORE_ERRORS + [
        MissingAuthorizationHeaderError,
    ]

graphql_app = GraphQLRouter(
    schema, context_getter=get_context, graphql_ide="apollo-sandbox"
)


app = FastAPI(
    title=settings.PROJECT_NAME,
    debug=bool(settings.DEBUG),
    version=settings.VERSION,
    redoc_url=None,
    openapi_url=None,
    swagger_ui_oauth2_redirect_url=None,
)

sentry_sdk.init(
    dsn=settings.ADMIN_SENTRY_DSN,
    ignore_errors=IGNORE_ERRORS,  # type: ignore
    environment=settings.ENV,
    traces_sample_rate=1.0 if settings.ENV == "dev" else 0.5,
    profiles_sample_rate=1.0 if settings.ENV == "dev" else 0.5,
    enable_tracing=True,
    release=settings.VERSION,
    auto_session_tracking=True,
)

logger.info(f"APP initialized. Env {settings.ENV}")


app.include_router(graphql_app, prefix="/graphql")
app.add_middleware(
    CORSMiddleware,
    allow_headers="*",
    allow_origins="*",
    allow_methods="*",
    allow_credentials=True,
    expose_headers=[TIMEZONE_HEADER],
)
app.add_middleware(TimeZoneMiddleware)

register_tortoise(
    app,
    get_tortoise_orm_config(settings.default_db_url),
)


@app.get("/")
def version_check():
    return {"status": "ok", "version": settings.VERSION}


@app.get("/health")
def health_check():
    # TODO: Add health checks for db, redis, cognito, s3, etc.
    return {"status": "ok", "version": settings.VERSION}
