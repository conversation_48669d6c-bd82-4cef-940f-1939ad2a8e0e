class MissingAuthorizationHeaderError(Exception):
    def __init__(self, reason: str = ""):
        super().__init__("Authorization header is missing")
        self.reason = reason


class MissingTokenError(Exception):
    def __init__(self, reason: str = ""):
        super().__init__("Token is missing")
        self.reason = reason


class UnauthorizedError(Exception):
    def __init__(self, reason: str = ""):
        super().__init__(reason or "Unauthorized")
        self.reason = reason
