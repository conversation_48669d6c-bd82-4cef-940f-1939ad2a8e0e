import strawberry

from src.activity.mutations import ActivityMutation
from src.activity.queries import ActivityQuery
from src.admin.queries import AdminQuery
from src.auth.query import AuthQuery
from src.content_library.mutations import ContentLibraryMutation
from src.content_library.queries import ContentLibraryQuery
from src.webinar.mutations import WebinarMutation
from src.webinar.queries import WebinarQuery
from src.cohort.mutations import CohortMutation
from src.cohort.queries import CohortQuery
from src.participant.mutations import ParticipantMutation
from src.participant.queries import ParticipantQuery
from src.program.mutations import ProgramMutation
from src.program.queries import ProgramQuery
from transtek.mutations import TranstekMutation
from transtek.queries import TranstekQuery


@strawberry.type
class Query(
    AuthQuery,
    AdminQuery,
    ActivityQuery,
    CohortQuery,
    ParticipantQuery,
    ProgramQuery,
    WebinarQuery,
    ContentLibraryQuery,
    TranstekQuery,
):
    """Main graphql query class."""


@strawberry.type
class Mutation(
    ActivityMutation,
    CohortMutation,
    ProgramMutation,
    ParticipantMutation,
    WebinarMutation,
    ContentLibraryMutation,
    TranstekMutation,
):
    """Main graphql mutation class."""


schema = strawberry.Schema(query=Query, mutation=Mutation)
