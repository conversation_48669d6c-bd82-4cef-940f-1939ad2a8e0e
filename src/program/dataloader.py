from strawberry.dataloader import <PERSON><PERSON>oa<PERSON>
from typing import List
from ciba_participant.program.crud import ProgramRepository
from src.program.types import (
    ProgramType,
    convert_program_output_to_program_type,
    ProgramModuleType,
    convert_module_output_to_module_type,
)
from uuid import UUID


async def batch_load_programs(keys: List[UUID]) -> List[ProgramType]:
    programs = await ProgramRepository.get_programs_by_ids(keys)
    program_map = {program.id: program for program in programs}
    return [
        convert_program_output_to_program_type(program_map.get(key))
        for key in keys
    ]


program_dataloader = DataLoader(load_fn=batch_load_programs)


async def load_program_modules(keys: List[UUID]) -> List[ProgramModuleType]:
    program_modules = [
        await ProgramRepository.get_program(program_module_id)
        for program_module_id in keys
    ]
    return convert_module_output_to_module_type(program_modules)


module_dataloader = DataLoader(load_fn=load_program_modules)
