from uuid import UUID

import strawberry
from ciba_participant.activity.models import ParticipantActivity
from ciba_participant.cohort.models import <PERSON><PERSON><PERSON>
from ciba_participant.program.crud import (
    ProgramModuleRepository,
    ProgramModuleSectionRepository,
    ProgramRepository,
)
from ciba_participant.program.models import ProgramModule
from ciba_participant.program.pydantic_models import (
    ProgramCreate,
    ProgramModuleCreate,
    ProgramModuleSectionCreate,
    ProgramModuleSectionMetadata,
    ProgramModuleSectionUpdate,
    ProgramModuleUpdate,
    ProgramUpdate,
)
from src.log.logging import logger

from src.common.types import SimpleRespType
from src.participant.types import HasActivityRecordError
from src.program.inputs import (
    ProgramCreateInput,
    ProgramModuleCreateInput,
    ProgramModuleIDInput,
    ProgramModuleSectionCreateInput,
    ProgramModuleSectionIDInput,
    ProgramModuleSectionUpdateInput,
    ProgramModuleUpdateInput,
    ProgramUpdateInput,
)
from src.program.types import (
    ProgramModuleSectionType,
    ProgramModuleType,
    ProgramType,
)


async def create_program(data: ProgramCreateInput) -> ProgramType:
    # TODO: all crud Admin only
    try:
        logger.info("Creating program")
        new_program = await ProgramRepository.create_program(
            ProgramCreate(title=data.title, description=data.description)
        )
        logger.info("Created program")

        program = await ProgramRepository.get_program(new_program.id)

        assert program is not None

        return ProgramType(
            id=program.id,
            created_at=program.created_at,
            description=program.description,
            title=program.title,
            updated_at=program.updated_at,
        )

    except Exception as e:
        logger.exception(e)
        raise e


async def update_program(info, data: ProgramUpdateInput) -> ProgramType:
    # TODO: all crud Admin only
    try:
        logger.info(f"Updating program with id {data.id}")
        update_data = data.__dict__.copy()
        await ProgramRepository.update_program(
            data.id, ProgramUpdate(**update_data)
        )
        logger.info(f"Updated program with id {data.id}")
        updated_program = await ProgramRepository.get_program(data.id)

        assert updated_program is not None

        return ProgramType(
            id=updated_program.id,
            created_at=updated_program.created_at,
            title=updated_program.title,
            updated_at=updated_program.updated_at,
            description=updated_program.description,
            _modules=updated_program.modules,
        )

    except Exception as e:
        logger.exception(e)
        raise e


async def delete_program(id: UUID) -> SimpleRespType:
    # TODO: all crud Admin only

    cohort_with_program = await Cohort.filter(program_id=id).exists()
    if cohort_with_program:
        logger.error("Cannot delete programs with active cohorts.")
        raise ValueError("Cannot delete programs with active cohorts.")

    try:
        logger.info(f"Deleting program with id {id}")
        await ProgramRepository.delete_program(id)
        logger.info(f"Deleted program with id {id}")
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        raise e


async def create_program_module(
    info, data: ProgramModuleCreateInput
) -> ProgramModuleType:
    # TODO: all crud Admin only
    try:
        module_create = ProgramModuleCreate(**data.__dict__)
        new_module = await ProgramModuleRepository.create_program_module(
            module_create
        )
        module = await ProgramModuleRepository.get_program_module(
            new_module.id
        )

        assert module is not None

        logger.info(f"Created program module: {data.short_title}")

        return ProgramModuleType(
            id=module.id,
            created_at=module.created_at,
            updated_at=module.updated_at,
            title=module.title,
            short_title=module.short_title,
            length=module.length,
            description=module.description,
            program_id=module.program_id,
            order=module.order,
        )

    except Exception as e:
        logger.exception(e)
        raise e


async def update_program_module(
    info, data: ProgramModuleUpdateInput
) -> ProgramModuleType:
    # TODO: all crud Admin only
    try:
        module_update = ProgramModuleUpdate.model_validate(
            data, from_attributes=True
        )

        await ProgramModuleRepository.update_program_module(
            data.id, module_update
        )

        updated_module = await ProgramModuleRepository.get_program_module(
            data.id
        )

        assert updated_module is not None

        return ProgramModuleType(
            id=updated_module.id,
            created_at=updated_module.created_at,
            updated_at=updated_module.updated_at,
            title=updated_module.title,
            short_title=updated_module.short_title,
            length=updated_module.length,
            description=updated_module.description,
            program_id=updated_module.program_id,
            order=updated_module.order,
        )

    except Exception as e:
        logger.exception(e)
        raise e


async def delete_program_module(data: ProgramModuleIDInput) -> SimpleRespType:
    # TODO: all crud Admin only
    program_module = await ProgramModule.filter(id=data.id).first()

    cohort_with_program = await Cohort.filter(
        program_id=program_module.program_id
    ).exists()
    if cohort_with_program:
        logger.error("Cannot delete modules in programs with active cohorts.")
        raise ValueError(
            "Cannot delete modules in programs with active cohorts."
        )

    try:
        await ProgramModuleRepository.delete_program_module(data.id)
        logger.info(f"Deleted program module {data.id}")
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        raise e


async def create_program_module_section(
    info, data: ProgramModuleSectionCreateInput
) -> ProgramModuleSectionType:
    # TODO: all crud Admin only
    try:
        meta = (
            ProgramModuleSectionMetadata.model_validate(
                data.metadata, from_attributes=True
            )
            if data.metadata
            else None
        )
        section_create = ProgramModuleSectionCreate(
            description=data.description,
            metadata=meta,
            program_module_id=data.program_module_id,
            title=data.title,
            activity_type=data.activity_type,
            activity_category=data.activity_category,
        )

        section = (
            await ProgramModuleSectionRepository.create_program_module_section(
                section_create
            )
        )

        return ProgramModuleSectionType(
            id=section.id,
            created_at=section.created_at,
            updated_at=section.updated_at,
            title=section.title,
            description=section.description,
            raw_metadata=section.metadata,
            program_module_id=section.program_module_id,
            activity_type=section.activity_type,
            activity_category=section.activity_category,
        )

    except Exception as e:
        logger.exception(e)
        raise e


async def update_program_module_section(
    info, data: ProgramModuleSectionUpdateInput
) -> ProgramModuleSectionType:
    try:
        meta = (
            ProgramModuleSectionMetadata.model_validate(
                data.metadata, from_attributes=True
            )
            if data.metadata
            else None
        )

        changes = ProgramModuleSectionUpdate(
            description=data.description,
            metadata=meta,
            program_module_id=data.program_module_id,
            title=data.title,
            activity_type=data.activity_type,
            activity_category=data.activity_category,
        )

        await ProgramModuleSectionRepository.update_program_module_section(
            data.id, changes
        )

        updated_section = (
            await ProgramModuleSectionRepository.get_program_module_section(
                data.id
            )
        )

        assert updated_section is not None

        return ProgramModuleSectionType(
            id=updated_section.id,
            created_at=updated_section.created_at,
            updated_at=updated_section.updated_at,
            title=updated_section.title,
            description=updated_section.description,
            raw_metadata=updated_section.metadata,
            program_module_id=updated_section.program_module_id,
            activity_type=updated_section.activity_type,
            activity_category=updated_section.activity_category,
        )

    except Exception as e:
        logger.exception(e)
        raise e


async def delete_program_module_section(
    data: ProgramModuleSectionIDInput,
) -> SimpleRespType | HasActivityRecordError:
    # TODO: all crud Admin only
    try:
        activity = await ParticipantActivity.filter(section_id=data.id).first()
        if activity:
            return HasActivityRecordError()
        else:
            await ProgramModuleSectionRepository.delete_program_module_section(
                data.id
            )
            return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


@strawberry.type
class ProgramMutation:
    create_program: ProgramType = strawberry.field(resolver=create_program)
    update_program: ProgramType = strawberry.field(resolver=update_program)
    delete_program: SimpleRespType = strawberry.field(resolver=delete_program)
    add_program_module: ProgramModuleType = strawberry.field(
        resolver=create_program_module
    )
    update_program_module: ProgramModuleType = strawberry.field(
        resolver=update_program_module
    )
    delete_program_module: SimpleRespType = strawberry.field(
        resolver=delete_program_module
    )
    add_program_module_section: ProgramModuleSectionType = strawberry.field(
        resolver=create_program_module_section
    )
    update_program_module_section: ProgramModuleSectionType = strawberry.field(
        resolver=update_program_module_section
    )
    delete_program_module_section: SimpleRespType | HasActivityRecordError = (
        strawberry.field(resolver=delete_program_module_section)
    )
