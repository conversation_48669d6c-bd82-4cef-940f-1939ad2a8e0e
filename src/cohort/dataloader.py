from strawberry.dataloader import <PERSON><PERSON>oader
from typing import List
from ciba_participant.cohort.crud import CohortRepository
from src.cohort.types import CohortType
from uuid import UUID


async def batch_load_cohorts(keys: List[UUID]) -> List[CohortType]:
    cohorts = await CohortRepository.get_cohorts_by_ids(keys)
    cohort_map = {cohort.id: cohort for cohort in cohorts}
    return [cohort_map.get(key) for key in keys]


cohort_dataloader = DataLoader(load_fn=batch_load_cohorts)
