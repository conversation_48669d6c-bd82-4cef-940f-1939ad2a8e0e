from enum import StrEnum
from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import CohortRepository
from ciba_participant.cohort.crud.get_paginated_cohorts import (
    FilterInput,
    Include,
)
from src.log.logging import logger
from strawberry import Info
from strawberry.types.nodes import SelectedField

from src.cohort.types import CohortsListType, CohortType
from src.common.input import PaginationInput


class TranslateField(StrEnum):
    program = "program"
    participants = "participants"
    createdBy = "created_by"
    programModules = "program_modules"


@strawberry.enum
class CohortStateFilter(StrEnum):
    ALL = "all"
    EMPTY = "empty"
    NOT_EMPTY = "not_empty"


@strawberry.input
class GetCohortsFilterInput:
    name_like: Optional[str] = None
    created_by: Optional[UUID] = None
    program_id: Optional[UUID] = None
    cohort_state: CohortStateFilter = CohortStateFilter.ALL


async def get_cohorts(
    info: Info,
    pagination: Optional[PaginationInput] = PaginationInput(),
    filters: Optional[GetCohortsFilterInput] = None,
) -> CohortsListType:
    include = {
        Include[TranslateField[selection.name]]
        for selection in info.selected_fields[0].selections[0].selections
        if isinstance(selection, SelectedField)
        and selection.name in TranslateField.__members__
    }

    if pagination is None:
        raise NotImplementedError("Pagination is required")

    filters_input = None

    if filters:
        filters_input = FilterInput.model_validate(
            filters, from_attributes=True
        )

    paginated_cohorts = await CohortRepository.get_paginated_cohorts(
        page=pagination.page,
        per_page=pagination.per_page,
        include=include,
        filters=filters_input,
    )

    cohorts = paginated_cohorts.cohorts

    cohorts_output: list[CohortType] = []

    for cohort in cohorts:
        cohorts_output.append(
            CohortType(
                id=cohort.id,
                created_at=cohort.created_at,
                updated_at=cohort.updated_at,
                limit=cohort.limit,
                status=cohort.status,
                name=cohort.name,
                started_at=cohort.started_at,
                program_id=cohort.program_id,
                _participants=cohort.participants,
                _created_by=cohort.created_by,
                _program=cohort.program,
            )
        )

    logger.info(f"Got {len(cohorts_output)} cohorts")

    return CohortsListType(
        cohorts=cohorts_output, total_pages=paginated_cohorts.total_pages
    )
