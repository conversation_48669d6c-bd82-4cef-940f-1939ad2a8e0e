from enum import Str<PERSON>num
from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import CohortRepository
from ciba_participant.cohort.crud.get_cohort import Include
from src.log.logging import logger
from strawberry.types.nodes import SelectedField

from src.cohort.types import CohortType


class TranslateField(StrEnum):
    program = "program"
    participants = "participants"
    createdBy = "created_by"
    programModules = "program_modules"


async def get_cohort(
    info: strawberry.Info, data: UUID
) -> Optional[CohortType]:
    try:
        include = {
            Include[TranslateField[selection.name]]
            for selection in info.selected_fields[0].selections
            if isinstance(selection, SelectedField)
            and selection.name in TranslateField.__members__
        }

        cohort = await CohortRepository.get_cohort(data, include=include)

        if cohort is None:
            raise Exception("Cohort not found")

        return CohortType(
            id=cohort.id,
            created_at=cohort.created_at,
            updated_at=cohort.updated_at,
            limit=cohort.limit,
            status=await ,
            end_date=cohort.end_date,
            name=cohort.name,
            started_at=cohort.started_at,
            program_id=cohort.program_id,
            _participants=cohort.participants,
            _created_by=cohort.created_by,
            _program=cohort.program,
        )
    except Exception as e:
        logger.exception(e)
        return None
