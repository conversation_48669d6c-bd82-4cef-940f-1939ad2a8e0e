from enum import StrEnum
from typing import Optional
from uuid import UUID
from src.participant.inputs import ParticipantIDInput

import strawberry
from ciba_participant.cohort.crud import (
    CohortRepository,
    CohortMembersRepository,
    CohortMemberOutput,
)
from ciba_participant.participant.crud import (
    ParticipantRepository,
    Participant,
)
from ciba_participant.participant.models import ParticipantStatus
from ciba_participant.cohort.crud.get_cohort import Include
from src.log.logging import logger
from strawberry.types.nodes import SelectedField

from src.cohort.types import CohortType


class TranslateField(StrEnum):
    program = "program"
    participants = "participants"
    createdBy = "created_by"
    programModules = "program_modules"


class ParticipantNotFoundException(Exception):
    def __init__(self, p_id: UUID = ""):
        reason = f"Participant not found with ID {p_id}"
        super().__init__(reason)
        self.reason = reason


class InactiveParticipantException(Exception):
    def __init__(self, status: str = ""):
        reason = f"Participant status is {status}"
        super().__init__(reason)
        self.reason = reason


class UnassignedParticipantException(Exception):
    def __init__(self, p_id: UUID = ""):
        reason = (
            f"Participant with ID {p_id} has not been assigned to a cohort"
        )
        super().__init__(reason)
        self.reason = reason


async def get_cohort_by_participant_id(
    info: strawberry.Info, data: ParticipantIDInput
) -> Optional[CohortType]:
    try:
        participant: (
            Participant | None
        ) = await ParticipantRepository.get_participant(participant_id=data.id)

        if participant is None:
            raise ParticipantNotFoundException(data.id)

        if participant.status == ParticipantStatus.DELETED:
            raise InactiveParticipantException(participant.status.value)

        member: (
            CohortMemberOutput | None
        ) = await CohortMembersRepository.get_cohort_member(
            participant_id=data.id
        )

        if member is None or member.cohort_id is None:
            raise UnassignedParticipantException(data.id)

        cohort_id = member.cohort_id

        include = {
            Include[TranslateField[selection.name]]
            for selection in info.selected_fields[0].selections
            if isinstance(selection, SelectedField)
            and selection.name in TranslateField.__members__
        }

        cohort = await CohortRepository.get_cohort(cohort_id, include=include)

        if cohort is None:
            raise Exception("Cohort not found")

        return CohortType(
            id=cohort.id,
            created_at=cohort.created_at,
            updated_at=cohort.updated_at,
            limit=cohort.limit,
            status=cohort.status,
            end_date=cohort.end_date,
            name=cohort.name,
            started_at=cohort.started_at,
            program_id=cohort.program_id,
            _participants=cohort.participants,
            _created_by=cohort.created_by,
            _program=cohort.program,
        )
    except Exception as e:
        logger.exception(e)
        return None
