import pendulum

from ciba_participant.cohort.models import <PERSON>hor<PERSON>, CohortStatusEnum
from cohort.types import CohortStatusGraphEnum


async def calculate_cohort_status(cohort: Cohort) -> CohortStatusGraphEnum:

    now = pendulum.now()

    if cohort.status == CohortStatusEnum.COMPLETED.value:
        return CohortStatusGraphEnum.COMPLETED.value

    if cohort.started_at > now:
        return CohortStatusGraphEnum.PENDING.value

    end_date = await cohort.end_date

    if end_date <= now.add(days=28):
        return CohortStatusGraphEnum.ENDING.value

    return CohortStatusGraphEnum.ACTIVE.value
