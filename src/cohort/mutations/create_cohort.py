from datetime import datetime
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import CohortRepository
from src.log.logging import logger

from src.auth.decorators import is_authorized
from src.settings import get_settings

settings = get_settings()


@strawberry.type
class CreateCohortOutput:
    id: UUID
    name: str
    limit: int
    started_at: datetime
    program_id: UUID
    created_by: UUID


@strawberry.input
class CreateCohortInput:
    name: str
    start_date: datetime
    limit: int
    program_id: UUID


@is_authorized(error_msg="Cohort creation requires Health Coach permissions.")
async def create_cohort(
    info, data: CreateCohortInput, created_by: UUID
) -> CreateCohortOutput:
    try:
        logger.info(f"Creating cohort {data.name}")

        cohort = await CohortRepository.create_cohort(
            name=data.name,
            started_at=data.start_date,
            limit=data.limit,
            program_id=data.program_id,
            created_by=created_by,
        )

        output = CreateCohortOutput(
            id=cohort.id,
            name=cohort.name,
            limit=cohort.limit,
            program_id=cohort.program_id,
            started_at=cohort.started_at,
            created_by=cohort.created_by,
        )

        return output

    except Exception as e:
        logger.exception(e)
        raise e
