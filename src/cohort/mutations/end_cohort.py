from uuid import UUID

from ciba_participant.cohort.crud import CohortRepository
from src.log.logging import logger

from src.common.types import SimpleRespType


async def end_cohort(
    cohort_id: UUID,
) -> SimpleRespType:
    try:
        logger.info(f"Ending cohort with id: {cohort_id}")

        await CohortRepository.end_cohort(cohort_id=cohort_id)

        return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)
