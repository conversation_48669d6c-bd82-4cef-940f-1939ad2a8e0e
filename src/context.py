from typing import Protocol

from src.auth import (
    CognitoAuth,
    WithAuth,
    With<PERSON><PERSON>orized,
    With<PERSON>uthorized<PERSON>ser,
    WithBearerToken,
    WithCognitoUser,
    WithToken,
    WithUnverifiedCognitoAuth,
    WithUser,
    WithVerifiedCognitoAuth,
)

from .settings import ENV, get_settings


class Context(
    WithToken, WithA<PERSON>[CognitoAuth], With<PERSON>ser, WithAuthorized, Protocol
):
    pass


class ProdContext(
    WithBearerToken,
    WithVerifiedCognitoAuth,
    WithAuthorizedUser,
    WithCognitoUser,
):
    pass


class DevContext(
    WithBearerToken,
    WithUnverifiedCognitoAuth,
    WithAuthorizedUser,
    WithCognitoUser,
):
    pass


async def get_context() -> Context:
    settings = get_settings()

    if settings.ENV in [ENV.PROD, ENV.DEV]:
        return ProdContext()
    else:
        return DevContext()
