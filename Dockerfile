FROM public.ecr.aws/lambda/python:3.12

#COPY --from=public.ecr.aws/datadog/lambda-extension:latest /opt/. /opt/

RUN dnf install -y openssh-clients git


COPY * ./

RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts

RUN --mount=type=ssh pip install -r requirements.txt

#ENV DD_LAMBDA_HANDLER="app.lambda_handler"
#ENV DD_TRACE_ENABLED=false
# Command can be overwritten by providing a different command in the template directly.
CMD ["app.lambda_handler"]
