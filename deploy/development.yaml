service_name: scheduled-email-notifications
metadata:
  team: participant
  source: python
type: sam
internal: true
environment:
  - name: "Environment"
    value: "dev"
  - name: "Version"
    value: "0.0.1"
  - name: "Debug"
    value: 1
  - name: "IsNewEnv"
    value: "1"
  - name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
    value: "1"
  - name: "KmsKeyId"
    value: "d662a077-8c98-4d4e-8c45-c25a65a9a05c"
  - name: "ExistingBucketName"
    value: "participant-ecosystem"
  # - name: "SendgridApiKey" # Security flaw, it exposes the secret as plain text in CloudFormation and in the Lambda environment variables
  #   value: "temporaryValue"
  # - name: "DatadogApiKey" # Security flaw, it exposes the secret as plain text in CloudFormation and in the Lambda environment variables
  #   value: "Secret"
sam_iam_capabilities:
  - CAPABILITY_IAM
  - CAPABILITY_AUTO_EXPAND
sam_failure_policy: "DO_NOTHING"
security_groups:
  - name: "scheduled-email-notifications-sg"
    description: "Security group for scheduled-email-notifications"
    protocol: "tcp"
    internal: false
    ports:
      - 443
