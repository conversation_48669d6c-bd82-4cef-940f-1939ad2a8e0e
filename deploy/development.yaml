service_name: mint-vault
metadata:
  team: participant
  source: python
type: sam
internal: true
environment:
  - name: "DEBUG"
    value: "1"
  - name: "ENV"
    value: "dev"
  - name: "VERSION"
    value: "stable"
  - name: "IsNewEnv"
    value: "1"
  - name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
    value: "1"
sam_iam_capabilities:
  - CAPABILITY_IAM
  - CAPABILITY_AUTO_EXPAND
sam_failure_policy: "DO_NOTHING"
security_groups:
  - name: "mint-vault-sg"
    description: "Security group for mint-vault"
    protocol: "tcp"
    internal: false
    ports:
      - 443
