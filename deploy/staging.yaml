service_name: participant-admin-api
metadata:
  team: participant
  source: python
internal: false
stage: stg
deploy:
  command:
    - /bin/bash
    - /app/entrypoint.sh
securityContext: null
image:
  pullPolicy: IfNotPresent
networking:
  backendPort: 8000
resources:
  requests:
    memory: "1280Mi"
    cpu: "768m"
  limits:
    memory: "2304Mi"
    cpu: "1256m"
environment:
  - name: "IS_NEW_ENV"
    value: "1"
ssm_params:
  - name: POSTGRES_USER
    from: /participant/POSTGRES_USER
  - name: POSTGRES_PORT
    from: /participant/POSTGRES_PORT
  - name: POSTGRES_PASSWORD
    from: /participant/POSTGRES_PASSWORD
  - name: POSTGRES_HOST
    from: /participant/POSTGRES_HOST
  - name: POSTGRES_DB
    from: /participant/POSTGRES_DB_v2
  - name: ALLOW_ORIGINS
    from: /participant/ALLOW_ORIGINS
  - name: ALLOW_METHODS
    from: /participant/ALLOW_METHODS
  - name: ALL<PERSON>_HEADERS
    from: /participant/ALLOW_HEADERS
  - name: CHAT_API_HOST
    from: /participant/CHAT_API_HOST
  - name: CHAT_API_KEY
    from: /participant/CHAT_API_KEY
  - name: SCHEDULE_MANAGER_API_ENDPOINT
    from: /participant/SCHEDULE_MANAGER_API_ENDPOINT
  - name: COGNITO_USER_POOL_ID
    from: /participant/COGNITO_USER_POOL_ID
  - name: COGNITO_AWS_REGION
    from: /participant/COGNITO_AWS_REGION
  - name: COGNITO_APP_CLIENT_ID
    from: /participant/COGNITO_APP_CLIENT_ID
  - name: COGNITO_KMS_KEY_ARN
    from: /participant/COGNITO_KMS_KEY_ARN
  - name: ENV
    from: /participant/ENV
  - name: COGNITO_SERVER_CLIENT_ID
    from: /participant/COGNITO_SERVER_CLIENT_ID
  - name: COGNITO_SERVER_CLIENT_SECRET
    from: /participant/COGNITO_SERVER_CLIENT_SECRET
  - name: AWS_BUCKET_NAME
    from: /participant/AWS_BUCKET_NAME
  - name: PROVIDERS_COGNITO_APP_CLIENT_ID
    from: /participant/PROVIDERS_COGNITO_APP_CLIENT_ID
  - name: PROVIDERS_COGNITO_USER_POOL_ID
    from: /participant/PROVIDERS_COGNITO_USER_POOL_ID
  - name: ADMIN_SENTRY_DSN
    from: /participant/ADMIN_SENTRY_DSN
  - name: CONTENT_LIBRARY_BUCKET_NAME
    from: /participant/CONTENT_LIBRARY_BUCKET_NAME
  - name: CONTENT_LIBRARY_KEY_ID
    from: /content-library/cloudfront/key-group/id
  - name: CONTENT_LIBRARY_SIGN_KEY
    from: /content-library/cloudfront/key-group/private-key
  - name: CONTENT_LIBRARY_DISTRIBUTION
    from: /content-library/cloudfront/distribution
  - name: RPM_API_URL
    from: /participant/RPM_API_URL
  - name: CIBA_API_HOST
    from: /participant/CIBA_API_HOST
  - name: PARTICIPANT_EMAIL_SQS_URL
    from: /participant/PARTICIPANT_EMAIL_SQS_URL
  - name: CIBA_API_KEY
    from: /participant/CIBA_API_KEY
  - name: SOLERA_CLIENT_ID
    from: /participant/SOLERA_CLIENT_ID
  - name: SOLERA_CLIENT_SECRET
    from: /participant/SOLERA_CLIENT_SECRET
  - name: PROVIDER_APP_CLIENT_ID
    from: /participant/PROVIDER_APP_CLIENT_ID
  - name: PROVIDER_USER_POOL_ID
    from: /participant/PROVIDER_USER_POOL_ID
  - name: SOLERA_AUTH_URL
    from: /participant/SOLERA_AUTH_URL
  - name: SOLERA_API_URL
    from: /participant/SOLERA_API_URL
  - name: RPM_API_KEY
    from: /participant/RPM_API_KEY
  - name: SLACK_SNS_TOPIC_ARN
    from: /participant/SLACK_SNS_TOPIC_ARN
