service_name: push-notifications
metadata:
  team: participant
  source: python
type: sam
internal: true
environment:
  - name: "Environment"
    value: "stg"
  - name: "Version"
    value: "0.0.1"
  - name: "IsNewEnv"
    value: "1"
  # - name: "DatadogApiKey" # Security flaw, it exposes the secret as plain text in CloudFormation and in the Lambda environment variables
  #   value: "Secret"
sam_iam_capabilities:
  - CAPABILITY_IAM
  - CAPABILITY_AUTO_EXPAND
sam_failure_policy: "DO_NOTHING"
security_groups:
  - name: "push-notifications-sg"
    description: "Security group for push-notifications"
    protocol: "tcp"
    internal: false
    ports:
      - 443
