repos:
-   hooks:
    -   args:
        - --fix
        id: ruff
    -   id: ruff-format
    repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.5
-   hooks:
    -   additional_dependencies:
        - mdformat-ruff
        id: mdformat
    repo: https://github.com/executablebooks/mdformat
    rev: 0.7.17
-   hooks:
    -   args:
        - -S
        id: black
    repo: https://github.com/psf/black
    rev: 24.8.0
-   hooks:
    -   id: trailing-whitespace
    -   id: requirements-txt-fixer
    -   id: end-of-file-fixer
    -   id: detect-private-key
    repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
-   repo: https://github.com/pycqa/pylint
    rev: v3.2.7
    hooks:
    -   id: pylint
        require_serial: true
        args: [ "-rn", "--disable=import-error "]
-   hooks:
    -   id: gitleaks
    repo: https://github.com/gitleaks/gitleaks
    rev: v8.19.2
#-   hooks:
#    -   id: actionlint
#    repo: https://github.com/rhysd/actionlint
#    rev: v1.7.1
-   hooks:
    -   id: commitizen
    -   id: commitizen-branch
        stages:
        - push
    repo: https://github.com/commitizen-tools/commitizen
    rev: v3.29.0
