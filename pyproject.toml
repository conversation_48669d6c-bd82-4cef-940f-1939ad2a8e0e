[project]
name = "ciba-iot-etl"
version = "1.0.7"
description = "package to handle the ETL process for the ciba iot data"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aerich>=0.8.0",
    "apache-airflow-client>=2.3.0",
    "arrow>=1.3.0",
    "asyncpg>=0.30.0",
    "bandit>=1.7.5",
    "boto3>=1.35.36",
    "coverage>=7.3.2",
    "fastapi[standard]>=0.115.12",
    "httpx==0.28.1",
    "loguru>=0.7.2",
    "mocker>=1.1.1",
    "mypy>=1.6.1",
    "mypy-json-report>=1.0.4",
    "pandas>=2.2.2",
    "pendulum>=3.0.0",
    "pre-commit>=4.2.0",
    "pydantic>=2.9.2",
    "pydantic-settings>=2.7.0",
    "pytest-asyncio>=0.25.2",
    "pytest-cov>=6.1.1",
    "pytest-mock>=3.11.1",
    "python-multipart>=0.0.9",
    "ratelimit>=2.2.1",
    "requests>=2.28.1",
    "ruff>=0.1.2",
    "s3fs>=2025.5.1",
    "sentry-sdk>=1.11.0",
    "sphinx>=7.2.6",
    "sqlalchemy>=2.0.21",
    "stackprinter>=0.2.12",
    "starlette-exporter>=0.15.1",
    "tortoise-orm>=0.21.7",
    "uvicorn>=0.34.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.aerich]
tortoise_orm = "src.ciba_iot_etl.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra"
testpaths = [
    "tests/unit",
    "tests/integration"
]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
branch = true
relative_files = true

[tool.coverage.report]
# Regexes for lines to exclude from consideration
exclude_also = [
    # Don't complain about missing debug-only code:
    "def __repr__",
    "if self\\.debug",

    # Don't complain if tests don't hit defensive assertion code:
    "raise AssertionError",
    "raise NotImplementedError",

    # Don't complain if non-runnable code isn't run:
    "if 0:",
    "if __name__ == .__main__.:",

    # Don't complain about abstract methods, they aren't run:
    "@(abc\\.)?abstractmethod",
]

ignore_errors = true


[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "0.0.1"
version_scheme = "pep440"
version_provider = "pep621"
update_changelog_on_bump = true
major_version_zero = true

[tool.uv]
dev-dependencies = [
    "respx>=0.22.0",
]

[tool.mypy]
[[tool.mypy.overrides]]
module = "pypika.*"
ignore_missing_imports = true
