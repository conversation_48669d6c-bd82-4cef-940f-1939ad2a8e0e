[project]
name = "ciba-mintvault"
version = "0.0.9"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aws-lambda-powertools",
    "aws-xray-sdk>=2.14.0",
    "ciba-participant",
    "pre-commit>=3.8.0",
    "psycopg2-binary>=2.9.9",
    "psycopg2-pool>=1.2",
    "pydantic>=2.9.1",
    "pydantic-settings>=2.5.2",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.14.0",
    "pytest>=8.3.3",
    "pytest-cov>=5.0.0",
    "commitizen>=3.29.0",
    "pylint>=3.2.7",
    "lambda-cache>=0.8.1",
    "psycopg-binary>=3.2.2",
    "psycopg>=3.2.2",
    "psycopg-pool>=3.2.3",
    "openpyxl>=3.1.5",
    "mimesis>=18.0.0",
    "freezegun>=1.5.1",
]

[tool.uv.sources]
ciba-participant = { git = "ssh://**************/Cibahealth/ciba-participant.git", rev = "main" }

[tool.coverage.run]
branch = true
relative_files = true

[tool.coverage.report]
# Regexes for lines to exclude from consideration
exclude_also = [
    # Don't complain about missing debug-only code:
    "def __repr__",
    "if self\\.debug",

    # Don't complain if tests don't hit defensive assertion code:
    "raise AssertionError",
    "raise NotImplementedError",

    # Don't complain if non-runnable code isn't run:
    "if 0:",
    "if __name__ == .__main__.:",

    # Don't complain about abstract methods, they aren't run:
    "@(abc\\.)?abstractmethod",
]

ignore_errors = true

[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "0.0.1"
version_scheme = "pep440"
version_provider = "pep621"
update_changelog_on_bump = true
major_version_zero = true
