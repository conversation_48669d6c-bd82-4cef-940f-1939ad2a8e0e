[project]
name = "pythoninit"
version = "1.0.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aws-lambda-powertools>=3.3.0",
    "ciba-participant",
    "commitizen>=3.29.0",
    "coverage>=7.6.1",
    "nox>=2024.4.15",
    "pre-commit>=3.8.0",
    "pydantic>=2.9.1",
    "pydantic-settings>=2.5.2",
    "pytest>=8.3.3",
    "pytest-asyncio>=0.26.0",
    "pytest-cov>=5.0.0",
    "testcontainers>=4.8.1",
]
[tool.uv.sources]
ciba-participant = { git = "ssh://**************/Cibahealth/ciba-participant.git", rev = "main" }

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra"
testpaths = [
    "tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.coverage.run]
branch = true
relative_files = true

[tool.coverage.report]
# Regexes for lines to exclude from consideration
exclude_also = [
    # Don't complain about missing debug-only code:
    "def __repr__",
    "if self\\.debug",

    # Don't complain if tests don't hit defensive assertion code:
    "raise AssertionError",
    "raise NotImplementedError",

    # Don't complain if non-runnable code isn't run:
    "if 0:",
    "if __name__ == .__main__.:",

    # Don't complain about abstract methods, they aren't run:
    "@(abc\\.)?abstractmethod",
]

ignore_errors = true

[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "0.0.1"
version_scheme = "pep440"
version_provider = "pep621"
update_changelog_on_bump = true
major_version_zero = true
