[build-system]
requires = ["setuptools"]

[project]
name = "participant-api"
version = "3.0.36"


description = ""
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "asgi-correlation-id>=4.3.4",
    "asgiref>=3.5.2",
    "aws-encryption-sdk>=3.1.1",
    "boto3>=1.26.10",
    "celery>=5.2.7",
    "ciba-participant",
    "cryptography>=43.0.1",
    "fastapi[standard]>=0.115.6",
    "libcst>=1.4.0",
    "loguru>=0.7.2",
    "pendulum>=3.1.0",
    "psycopg>=3.1.19",
    "psycopg-binary>=3.2.2",
    "psycopg-pool>=3.2.2",
    "psycopg2-binary>=2.9.9",
    "pycryptodome >=3.20.0",
    "pydantic-settings>=2.3.4",
    "pyjwt>=2.9.0",
    "python-dotenv>=1.0.1",
    "python-jose>=3.4.0",
    "python-multipart>=0.0.18",
    "python-redis-cache>=1.2.0",
    "python-retry>=0.0.1",
    "pytz>=2023.3.post1",
    "redis>=4.3.4",
    "requests>=2.32.3",
    "respx>=0.21.1",
    "ruff>=0.1.3",
    "sendgrid>=6.9.7",
    "sentry-sdk>=1.45.1",
    "stackprinter>=0.2.9",
    "strawberry-graphql>=0.243.0",
    "tortoise-orm>=0.21.6",
    "types-pytz>=2023.3.1.1",
    "typing-extensions>=4.12.2",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["app"]
exclude = ["migrations*"]

[tool.uv.sources]
ciba-participant = { git = "ssh://**************/Cibahealth/ciba-participant.git", rev = "main" }

[tool.uv]
dev-dependencies = [
    "anyio>=3.6.2",
    "black>=24.3.0",
    "commitizen>=3.29.0",
    "ddtrace>=2.10.4",
    "debugpy>=1.8.2",
    "deepdiff>=8.2.0",
    "flake8>=5.0.4",
    "isort>=5.10.1",
    "mimesis>=17.0.0",
    "mypy>=1.14",
    "pre-commit>=4.2.0",
    "pylint>=2.15.5",
    "pytest>=7.2.0",
    "pytest-asyncio>=0.23.7",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "responses>=0.25.3",
    "testcontainers[postgres]>=4.5.1",
    "trio>=0.30.0",
    "virtualenv>=20.26.6",
]

[tool.aerich]
tortoise_orm = "app.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[tool.black]
line-length = 79
target-version = ['py312']

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 79
include_trailing_comma = true

[tool.mypy]
python_version = '3.12'
exclude = ['^venv/$', "tests"]
plugins = 'strawberry.ext.mypy_plugin'
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
follow_untyped_imports = true

[[tool.mypy.overrides]]
module = [
    "redis",
    "requests",
    "dateutil.parser",
]
ignore_missing_imports = true


[tool.pytest.ini_options]
minversion = "7.0"
anyio_backend = "asyncio"
addopts = '-ra'
testpaths = [
    "tests/unit",
    "tests/integration",
]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "session"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.pylint]
ignore-paths = ['^migrations*', "^tests/test_*", "^tests/tasks/test_*"]

[tool.pylint.'MESSAGES CONTROL']
disable = [
    'import-error',
    'too-few-public-methods',
    'missing-module-docstring',
    'missing-class-docstring',
    'no-name-in-module',
    'protected-access'
]

[tool.pylint.'SIMILARITIES']
ignore-comments = ['yes']
ignore-docstrings = ['yes']
ignore-imports = ['yes']
min-similarity-lines = 4


[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 79
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
