from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" ADD "use_custom_meeting_link" BOOL   DEFAULT False;
        ALTER TABLE "live_session" ADD "custom_meeting_link" VARCHAR(255)   DEFAULT '';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" DROP COLUMN "use_custom_meeting_link";
        ALTER TABLE "live_session" DROP COLUMN "custom_meeting_link";"""
