from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "blood_pressures"."device" IS 'WITHINGS: 1
FITBIT: 2
DEXCOM: 3';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9
MG_DL: 10';
        COMMENT ON COLUMN "heart_rates"."device" IS 'WITHINGS: 1
FITBIT: 2
DEXCOM: 3';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9
MG_DL: 10';
        COMMENT ON COLUMN "weights"."device" IS 'WITHINGS: 1
FITBIT: 2
DEXCOM: 3';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9
MG_DL: 10';
        COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9
MG_DL: 10';
        COMMENT ON COLUMN "sleep"."device" IS 'WITHINGS: 1
FITBIT: 2
DEXCOM: 3';
        COMMENT ON COLUMN "activity"."device" IS 'WITHINGS: 1
FITBIT: 2
DEXCOM: 3';
        COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9
MG_DL: 10';
        CREATE TABLE IF NOT EXISTS "glucose_levels" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "device" SMALLINT NOT NULL,
    "unit" SMALLINT NOT NULL  DEFAULT 1,
    "metadata" JSONB,
    "value" INT NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_glucose_lev_created_c1f6e1" ON "glucose_levels" ("created_at");
CREATE INDEX IF NOT EXISTS "idx_glucose_lev_member__62f377" ON "glucose_levels" ("member_id", "created_at");
COMMENT ON COLUMN "glucose_levels"."device" IS 'WITHINGS: 1\nFITBIT: 2\nDEXCOM: 3';
COMMENT ON COLUMN "glucose_levels"."unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4\nMINUTE: 5\nSTEP: 6\nKM: 7\nMI: 8\nLB: 9\nMG_DL: 10';
COMMENT ON TABLE "glucose_levels" IS 'Table representing a glucose level measures.';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "sleep"."device" IS 'WITHINGS: 1
FITBIT: 2';
        COMMENT ON COLUMN "weights"."device" IS 'WITHINGS: 1
FITBIT: 2';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "activity"."device" IS 'WITHINGS: 1
FITBIT: 2';
        COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "heart_rates"."device" IS 'WITHINGS: 1
FITBIT: 2';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "blood_pressures"."device" IS 'WITHINGS: 1
FITBIT: 2';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        DROP TABLE IF EXISTS "glucose_levels";"""
