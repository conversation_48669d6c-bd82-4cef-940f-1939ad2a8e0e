from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "fitbit" ADD "old_refresh_token" VARCHAR(305);
        ALTER TABLE "fitbit" ADD "access_token_expires_at" TIMESTAMPTZ;
        ALTER TABLE "fitbit" ADD "refresh_token_expires_at" TIMESTAMPTZ;
        ALTER TABLE "withings" ADD "old_refresh_token" VARCHAR(305);
        ALTER TABLE "withings" ADD "old_refresh_token_expires_at" TIMESTAMPTZ;
        ALTER TABLE "withings" ADD "access_token_expires_at" TIMESTAMPTZ;
        ALTER TABLE "withings" ADD "refresh_token_expires_at" TIMESTAMPTZ;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "fitbit" DROP COLUMN "old_refresh_token";
        ALTER TABLE "fitbit" DROP COLUMN "access_token_expires_at";
        ALTER TABLE "fitbit" DROP COLUMN "refresh_token_expires_at";
        ALTER TABLE "withings" DROP COLUMN "old_refresh_token";
        ALTER TABLE "withings" DROP COLUMN "old_refresh_token_expires_at";
        ALTER TABLE "withings" DROP COLUMN "access_token_expires_at";
        ALTER TABLE "withings" DROP COLUMN "refresh_token_expires_at";"""
