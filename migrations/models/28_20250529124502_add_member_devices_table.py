from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "member_devices" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "linked_at" TIMESTAMPTZ,
    "last_synced_at" TIMESTAMPTZ NOT NULL,
    "disconnected_at" TIMESTAMPTZ,
    "disconnected" BOOL NOT NULL  DEFAULT False,
    "timezone" VARCHAR(64) NOT NULL  DEFAULT 'UTC',
    "external_id" VARCHAR(256) NOT NULL,
    "device_type" VARCHAR(128) NOT NULL,
    "model" VARCHAR(128),
    "vendor" SMALLINT NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_member_devi_member__1dee77" UNIQUE ("member_id", "external_id")
);
COMMENT ON COLUMN "member_devices"."vendor" IS 'WITHINGS: 1\nFITBIT: 2\nDEXCOM: 3';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "member_devices";"""
