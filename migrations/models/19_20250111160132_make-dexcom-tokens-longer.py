from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dexcom" ALTER COLUMN "access_token" TYPE VARCHAR(850) USING "access_token"::VARCHAR(850);
        ALTER TABLE "dexcom" ALTER COLUMN "refresh_token" TYPE VARCHAR(850) USING "refresh_token"::VA<PERSON><PERSON>R(850);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dexcom" ALTER COLUMN "access_token" TYPE VARCHAR(756) USING "access_token"::VA<PERSON>HAR(756);
        ALTER TABLE "dexcom" ALTER COLUMN "refresh_token" TYPE VARCHAR(756) USING "refresh_token"::VARCHAR(756);"""
