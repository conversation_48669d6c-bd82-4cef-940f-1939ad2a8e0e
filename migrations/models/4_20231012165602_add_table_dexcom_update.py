from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dexcom" ALTER COLUMN "refresh_token" TYPE VARCHAR(756) USING "refresh_token"::VA<PERSON><PERSON><PERSON>(756);
        ALTER TABLE "dexcom" ALTER COLUMN "access_token" TYPE VARCHAR(756) USING "access_token"::VA<PERSON><PERSON><PERSON>(756);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dexcom" ALTER COLUMN "refresh_token" TYPE VARCHAR(305) USING "refresh_token"::VA<PERSON><PERSON><PERSON>(305);
        ALTER TABLE "dexcom" ALTER COLUMN "access_token" TYPE VARCHAR(305) USING "access_token"::VARCHAR(305);"""
