from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "members" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "email" VARCHAR(255) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS "member_platforms" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "platform_type" VARCHAR(255) NOT NULL,
    "platform_id" UUID NOT NULL,
    "member_id" UUID NOT NULL references members(id),
    UNIQUE(platform_type, platform_id)
);
CREATE TABLE IF NOT EXISTS "withings" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "user_id" VARCHAR(255) NOT NULL UNIQUE,
    "access_token" VARCHAR(255) NOT NULL UNIQUE,
    "refresh_token" VARCHAR(255) NOT NULL UNIQUE,
    "expires_in" INT NOT NULL,
    "member_id" UUID NOT NULL references members(id),
    "start" BOOL NOT NULL  DEFAULT False
);
COMMENT ON TABLE "withings" IS 'Table to store withings access tokens.';
CREATE TABLE IF NOT EXISTS "oruraring" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "member_id" UUID NOT NULL references members(id),
    "user_id" VARCHAR(255) NOT NULL UNIQUE,
    "access_token" VARCHAR(255) NOT NULL UNIQUE,
    "refresh_token" VARCHAR(255) NOT NULL UNIQUE,
    "expires_in" INT NOT NULL,
    "start" BOOL NOT NULL  DEFAULT False
);
COMMENT ON TABLE "oruraring" IS 'Table to store oruraring access tokens.';
CREATE TABLE IF NOT EXISTS "fitbit" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "user_id" VARCHAR(255) NOT NULL UNIQUE,
    "access_token" VARCHAR(255) NOT NULL UNIQUE,
    "refresh_token" VARCHAR(255) NOT NULL UNIQUE,
    "expires_in" INT NOT NULL,
    "member_id" UUID NOT NULL references members(id),
    "start" BOOL NOT NULL  DEFAULT False
);
COMMENT ON TABLE "fitbit" IS 'Table to store Fitbit access tokens.';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
