from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "withings" ADD "deleted" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "oruraring" ADD "deleted" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "fitbit" ADD "deleted" BOOL NOT NULL  DEFAULT False;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "fitbit" DROP COLUMN "deleted";
        ALTER TABLE "withings" DROP COLUMN "deleted";
        ALTER TABLE "oruraring" DROP COLUMN "deleted";"""
