from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "authorized" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "email" VARCHAR(255) NOT NULL UNIQUE,
    "first_name" VARCHAR(255) NOT NULL,
    "last_name" VARCHAR(255) NOT NULL,
    "status" VARCHAR(8) NOT NULL  DEFAULT 'pending',
    "cognito_sub" UUID,
    "is_test" BOOL NOT NULL  DEFAULT False,
    "role" VARCHAR(12) NOT NULL  DEFAULT 'health_coach',
    "alternative_host" BOOL NOT NULL  DEFAULT False
);
COMMENT ON COLUMN "authorized"."status" IS 'ACTIVE: active\nPENDING: pending\nREJECTED: rejected\nDELETED: deleted';
COMMENT ON COLUMN "authorized"."role" IS 'ADMIN: admin\nHEALTH_COACH: health_coach\nPROVIDER: provider';
CREATE TABLE IF NOT EXISTS "participants" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "email" VARCHAR(255) NOT NULL UNIQUE,
    "first_name" VARCHAR(255) NOT NULL,
    "last_name" VARCHAR(255) NOT NULL,
    "group_id" UUID NOT NULL,
    "member_id" UUID NOT NULL,
    "status" VARCHAR(8) NOT NULL  DEFAULT 'pending',
    "cognito_sub" UUID,
    "medical_record" VARCHAR(255)  UNIQUE,
    "is_test" BOOL NOT NULL  DEFAULT False,
    "last_reset" TIMESTAMPTZ
);
COMMENT ON COLUMN "participants"."status" IS 'ACTIVE: active\nPENDING: pending\nREJECTED: rejected\nDELETED: deleted';
COMMENT ON TABLE "participants" IS 'Participant database model.';
CREATE TABLE IF NOT EXISTS "program" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "title" VARCHAR(255) NOT NULL UNIQUE,
    "description" TEXT NOT NULL
);
CREATE TABLE IF NOT EXISTS "cohort" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "name" VARCHAR(255)  UNIQUE,
    "started_at" TIMESTAMPTZ,
    "limit" INT,
    "created_by_id" UUID NOT NULL REFERENCES "authorized" ("id") ON DELETE CASCADE,
    "program_id" UUID NOT NULL REFERENCES "program" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "cohort_members" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "cohort_id" UUID NOT NULL REFERENCES "cohort" ("id") ON DELETE CASCADE,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_cohort_memb_cohort__70bb0d" UNIQUE ("cohort_id", "participant_id")
);
CREATE TABLE IF NOT EXISTS "heads_up_participants" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "heads_up_token" VARCHAR(255),
    "heads_up_id" VARCHAR(255),
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "participant_meta" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "metadata" JSONB,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "solera_participants" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "solera_id" UUID,
    "solera_key" VARCHAR(255) NOT NULL,
    "solera_program_id" VARCHAR(255) NOT NULL,
    "solera_enrollment_id" VARCHAR(255) NOT NULL,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "program_module" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "title" VARCHAR(255) NOT NULL,
    "short_title" VARCHAR(255) NOT NULL,
    "length" INT NOT NULL,
    "description" TEXT NOT NULL,
    "order" INT NOT NULL,
    "program_id" UUID NOT NULL REFERENCES "program" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "program_module"."length" IS 'Days duration of the module';
CREATE TABLE IF NOT EXISTS "cohort_program_modules" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "started_at" TIMESTAMPTZ NOT NULL,
    "ended_at" TIMESTAMPTZ NOT NULL,
    "metadata" JSONB,
    "cohort_id" UUID NOT NULL REFERENCES "cohort" ("id") ON DELETE CASCADE,
    "program_module_id" UUID NOT NULL REFERENCES "program_module" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_cohort_prog_cohort__5dfa28" UNIQUE ("cohort_id", "program_module_id")
);
CREATE TABLE IF NOT EXISTS "live_session" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "title" VARCHAR(255),
    "description" VARCHAR(255),
    "meeting_start_time" TIMESTAMPTZ NOT NULL,
    "zoom_id" VARCHAR(255),
    "zoom_occurrence_id" VARCHAR(255),
    "timezone" VARCHAR(255)   DEFAULT 'UTC',
    "has_conflict" BOOL   DEFAULT False,
    "cohort_id" UUID NOT NULL REFERENCES "cohort" ("id") ON DELETE CASCADE,
    "cohort_program_module_id" UUID NOT NULL REFERENCES "cohort_program_modules" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "program_module_section" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "metadata" JSONB NOT NULL,
    "activity_type" VARCHAR(16) NOT NULL,
    "activity_category" VARCHAR(10) NOT NULL,
    "program_module_id" UUID NOT NULL REFERENCES "program_module" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "program_module_section"."activity_type" IS 'ENROLL: enrollment_type\nWEIGHT: weight_type\nPLAY: video_type\nCOACH: chat_type\nRECIPES: recipe_type\nQUIZ: personal_success\nACTIVITY: activity_type\nARTICLE: curriculum\nGROUP: coaching_call';
COMMENT ON COLUMN "program_module_section"."activity_category" IS 'SYSTOLIC: systolic\nDIASTOLIC: diastolic\nHEART_RATE: heart_rate\nWEIGHT: weight\nBMI: bmi\nACTIVITY: activity\nSTEPS: steps';
CREATE TABLE IF NOT EXISTS "participant_activity" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "value" VARCHAR(255),
    "unit" VARCHAR(6) NOT NULL,
    "activity_device" VARCHAR(12) NOT NULL,
    "activity_category" VARCHAR(10) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "activity_type" VARCHAR(16) NOT NULL,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE,
    "section_id" UUID REFERENCES "program_module_section" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_participant_id_471ace" UNIQUE ("id", "created_at")
);
COMMENT ON COLUMN "participant_activity"."unit" IS 'ACTION: action\nKG: kg\nLB: lb';
COMMENT ON COLUMN "participant_activity"."activity_device" IS 'WITHINGS: withings\nMANUAL_INPUT: manual_input';
COMMENT ON COLUMN "participant_activity"."activity_category" IS 'SYSTOLIC: systolic\nDIASTOLIC: diastolic\nHEART_RATE: heart_rate\nWEIGHT: weight\nBMI: bmi\nACTIVITY: activity\nSTEPS: steps';
COMMENT ON COLUMN "participant_activity"."activity_type" IS 'ENROLL: enrollment_type\nWEIGHT: weight_type\nPLAY: video_type\nCOACH: chat_type\nRECIPES: recipe_type\nQUIZ: personal_success\nACTIVITY: activity_type\nARTICLE: curriculum\nGROUP: coaching_call';
COMMENT ON TABLE "participant_activity" IS 'Participant activities';
CREATE TABLE IF NOT EXISTS "authorized_cohort" (
    "authorized_id" UUID NOT NULL REFERENCES "authorized" ("id") ON DELETE CASCADE,
    "cohort_id" UUID NOT NULL REFERENCES "cohort" ("id") ON DELETE CASCADE
);
CREATE UNIQUE INDEX IF NOT EXISTS "uidx_authorized__authori_f4fd61" ON "authorized_cohort" ("authorized_id", "cohort_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
