from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "activity"."category_unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        COMMENT ON COLUMN "activity"."category_unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';"""
