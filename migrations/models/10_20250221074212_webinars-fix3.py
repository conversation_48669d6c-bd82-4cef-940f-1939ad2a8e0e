from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "booking" ALTER COLUMN "status" TYPE VARCHAR(8) USING "status"::VARCHAR(8);
        COMMENT ON COLUMN "booking"."status" IS 'BOOKED: booked
ATTENDED: attended
CANCELED: canceled';
        ALTER TABLE "live_session" ALTER COLUMN "meeting_type" TYPE SMALLINT USING "meeting_type"::SMALLINT;
        ALTER TABLE "webinar" ALTER COLUMN "recurrence" TYPE VARCHAR(7) USING "recurrence"::VARCHAR(7);
        COMMENT ON COLUMN "webinar"."recurrence" IS 'MONTHLY: monthly';
        ALTER TABLE "webinar" ALTER COLUMN "topic" TYPE VARCHAR(19) USING "topic"::VARCHAR(19);
        COMMENT ON COLUMN "webinar"."topic" IS 'FOOD: food
EDUCATIONAL: educational
ACTIVITY: activity
HEALTH_AND_WELLNESS: health_and_wellness
MENTAL_HEALTH: mental_health
INTRO_SESSION: intro_session';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "booking"."status" IS 'BOOKED: 1
ATTENDED: 2
CANCELED: 3';
        ALTER TABLE "booking" ALTER COLUMN "status" TYPE VARCHAR(1) USING "status"::VARCHAR(1);
        COMMENT ON COLUMN "webinar"."recurrence" IS 'MONTHLY: 1';
        ALTER TABLE "webinar" ALTER COLUMN "recurrence" TYPE VARCHAR(1) USING "recurrence"::VARCHAR(1);
        COMMENT ON COLUMN "webinar"."topic" IS 'FOOD: 1
EDUCATIONAL: 2
ACTIVITY: 3
HEALTH_AND_WELLNESS: 4
MENTAL_HEALTH: 5
INTRO_SESSION: 6';
        ALTER TABLE "webinar" ALTER COLUMN "topic" TYPE VARCHAR(1) USING "topic"::VARCHAR(1);
        ALTER TABLE "live_session" ALTER COLUMN "meeting_type" TYPE VARCHAR(1) USING "meeting_type"::VARCHAR(1);"""
