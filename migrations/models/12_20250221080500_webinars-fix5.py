from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" ALTER COLUMN "description" TYPE VARCHAR(500) USING "description"::VARCHAR(500);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" ALTER COLUMN "description" TYPE VARCHAR(255) USING "description"::VARCHAR(255);"""
