from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" ADD "time_of_day" VARCHAR(10);

        UPDATE live_session
            SET time_of_day =
                CASE
                    WHEN EXTRACT(HOUR FROM meeting_start_time) < 12 THEN 'morning'
                    WHEN EXTRACT(HOUR FROM meeting_start_time) BETWEEN 12 AND 17 THEN 'afternoon'
                    ELSE 'evening'
                END;
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" DROP COLUMN "time_of_day";"""
