from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

TIMEZONE_HEADER = "Time-Zone-Offset"


class TimeZoneMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        timezone = request.headers.get(TIMEZONE_HEADER)
        if request.url.path == "/graphql" and timezone is not None:
            response = await call_next(request)
            response.headers.append(TIMEZONE_HEADER, timezone)
            return response

        response = await call_next(request)
        return response
