from httpx import AsyncClient, codes
from ciba_participant.settings import get_settings
from ciba_participant.rpm_api.models import (
    DeviceStatus,
    DeviceStatusEnum,
    DeviceTypeEnum,
    DetailedConnectionStatus,
)
from loguru import logger


settings = get_settings()

PARTICIPANT_MEMBER_TYPE = "participant"


async def get_single_device_status(
    participant_id: str, device_type: DeviceTypeEnum
) -> DetailedConnectionStatus:
    """Given a participant id, and a device type,
    it queries the RPM service and returns detailed
    device status"""
    NOT_CONNECTED = DetailedConnectionStatus(
        healthy=False,
        device=device_type.value,
        status=DeviceStatusEnum.NOT_CONNECTED,
    )

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
        params={
            "type_device": device_type.value,
            "member_type": PARTICIPANT_MEMBER_TYPE,
            "member_id": participant_id,
        },
    ) as client:
        try:
            response = await client.get(
                "/devices/get_status",
            )
            if response.status_code == codes.OK:
                data: dict = response.json()
                device_status = DetailedConnectionStatus(
                    token=data.get("token"),
                    healthy=data.get("healthy"),
                    account_id=data.get("account_id"),
                    subscription=data.get("subscription"),
                    device=device_type.value,
                    status=DeviceStatusEnum.CONNECTED
                    if data.get("healthy") is True
                    else DeviceStatusEnum.RECONNECT,
                )
                return device_status
        except Exception as ex:
            logger.error(f"Failed to get rpm data: {ex}")
            return NOT_CONNECTED
    return NOT_CONNECTED


async def get_devices_status(participant_id: str) -> list[DeviceStatus]:
    """Given a participant id, it queries the RPM service and returns
    device statuses for each device, as a list"""

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
        params={
            "member_type": PARTICIPANT_MEMBER_TYPE,
            "member_id": participant_id,
        },
    ) as client:
        try:
            response = await client.get(
                "/devices/get_all_status",
            )
            if response.status_code == codes.OK:
                devices_status = list(
                    map(
                        lambda info: DeviceStatus(
                            device=info["device"],
                            status=info["status"],
                        ),
                        response.json(),
                    )
                )

                return devices_status

        except Exception as ex:
            logger.error(f"Failed to get rpm data: {ex}")
            return []
    return []


async def get_devices_status_by_participant_ids(participants: list[str]) -> dict:
    """Given a list of participants, it queries the RPM service and returns
    device statuses for each one as a dict"""

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            params = {
                "member_type": PARTICIPANT_MEMBER_TYPE,
                "members": participants,
            }
            response = await client.post(
                f"{settings.RPM_API_URL}devices/get_status_by_members", json=params
            )

            if response.status_code == codes.OK:
                statuses = {}
                for member in response.json():
                    statuses[member["member_id"]] = list(
                        map(
                            lambda info: DeviceStatus(
                                device=info["device"],
                                status=info["status"],
                            ),
                            member["devices"],
                        )
                    )

                return statuses

        except Exception as ex:
            logger.error(f"Failed to get rpm data: {ex}")
            return {}
    return {}
