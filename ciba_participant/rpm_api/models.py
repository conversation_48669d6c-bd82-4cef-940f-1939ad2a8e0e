from enum import StrEnum
from typing import Optional

import strawberry


@strawberry.enum
class DeviceStatusEnum(StrEnum):
    NOT_CONNECTED = "not_connected"
    CONNECTED = "connected"
    RECONNECT = "reconnect"


@strawberry.enum
class DeviceTypeEnum(StrEnum):
    WITHINGS = "withings"
    FITBIT = "fitbit"
    DEXCOM = "dexcom"
    OURARING = "ouraring"


@strawberry.type
class DeviceStatus:
    status: DeviceStatusEnum
    device: DeviceTypeEnum


@strawberry.type
class Subscription:
    expires_in: int


@strawberry.type
class DetailedConnectionStatus(DeviceStatus):
    token: Optional[str] = None
    healthy: Optional[bool] = None
    account_id: Optional[str] = None
    subscription: Optional[Subscription] = None
    auth_url: Optional[str] = None
