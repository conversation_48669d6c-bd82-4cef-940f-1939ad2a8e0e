from uuid import UUID

from httpx import AsyncClient, HTTPError, HTTPStatusError
from loguru import logger

from ciba_participant import get_settings
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.helpers import get_sync_start_date
from ciba_participant.rpm_api.models import LatestData

settings = get_settings()
PARTICIPANT_TYPE = "participant"
DEVICE_TYPE = "withings"


async def get_latest_data(participant_id: UUID) -> LatestData:
    last_measure = await get_sync_start_date(participant_id)

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=30,
        params={
            "device_type": DEVICE_TYPE,
            "member_type": PARTICIPANT_TYPE,
            "member_id": str(participant_id),
            "start_date": last_measure,
        },
    ) as client:
        try:
            response = await client.get("/measures/latest_data")
            response.raise_for_status()
            parsed_response = response.json()

            return LatestData.model_validate(parsed_response)
        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error retrieving latest data for participant {participant_id}, {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(
                f"An error occurred calling RPM latest data for participant {participant_id}: {error}"
            )
            raise RPMCallError("An error occurred calling RPM service") from error
