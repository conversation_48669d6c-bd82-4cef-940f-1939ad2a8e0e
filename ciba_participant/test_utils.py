import uuid
from typing import Optional
from datetime import datetime
import random
import pendulum
import mimesis

from enum import Enum

from ciba_participant.activity.schemas import ParticipantActivityInput
from ciba_participant.classes.models import (
    Webinar,
    LiveSession,
    Booking,
    TopicEnum,
    RecurrenceEnum,
    BookingStatusEnum,
)
from ciba_participant.cohort.crud.create_cohort import process
from ciba_participant.content_library.crud import ContentMaterialRepository
from ciba_participant.content_library.crud.create_content_material import (
    NewMaterialData,
)
from ciba_participant.content_library.enums import MaterialTag
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.participant.models import (
    AutorizedRole,
    ParticipantStatus,
)
from ciba_participant.program.crud import (
    ProgramRepository,
    ProgramModuleRepository,
    ProgramModuleSectionRepository,
)
from ciba_participant.cohort.crud import CohortMembersRepository
from ciba_participant.program.pydantic_models import (
    Program<PERSON>reate,
    ProgramModuleCreate,
    ProgramModuleSectionCreate,
    ProgramModuleSectionMetadata,
)
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityCategory,
    ActivityUnit,
    ParticipantActivityDevice,
)
from ciba_participant.activity.crud import ParticipantActivityRepository

from ciba_participant.participant.models import (
    Participant,
    Authorized,
    SoleraParticipant,
    ParticipantMeta,
)
from ciba_participant.program.models import (
    Program,
    ProgramModule,
    ProgramModuleSection,
)
from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
)


async def create_program(program_data: Optional[ProgramCreate] = None):
    if not program_data:
        program_data = ProgramCreate(
            title="NDPP", description=f"Test Program Description {1}"
        )
    program_resp = await ProgramRepository.create_program(program_data)

    return program_resp


async def create_modules(
    program_id: uuid.UUID,
    program_module_data: Optional[ProgramModuleCreate] = None,
):
    if not program_module_data:
        program_module_data = ProgramModuleCreate(
            title=f"Test Module {1}",
            short_title=f"Module {1}",
            length=random.randint(5, 21),
            description=f"Test Module Description {1}",
            program_id=program_id,
            order=1,
        )
    module_resp = await ProgramModuleRepository.create_program_module(
        program_module_data
    )
    return module_resp


async def create_program_module_section(
    program_module_id: uuid.UUID,
    program_module_section_data: Optional[ProgramModuleSectionCreate] = None,
):
    if not program_module_section_data:
        program_module_section_data = ProgramModuleSectionCreate(
            title="",
            description="",
            metadata=ProgramModuleSectionMetadata(url="https://google.com"),
            program_module_id=program_module_id,
            activity_type=ParticipantActivityEnum.ARTICLE,
            activity_category=ParticipantActivityCategory.ACTIVITY,
        )
    section_resp = await ProgramModuleSectionRepository.create_program_module_section(
        program_module_section_data
    )
    return section_resp


async def create_full_program():
    program_resp = await create_program()
    module_resp = await create_modules(program_id=program_resp.id)

    sections = [
        ProgramModuleSectionCreate(
            title=f"PMS #{i}",
            description=f"Descr {i}",
            metadata=(
                ProgramModuleSectionMetadata(form_id="123123")
                if i == ParticipantActivityEnum.QUIZ
                else ProgramModuleSectionMetadata(url="https://google.com")
            ),
            program_module_id=module_resp.id,
            activity_type=i,
            activity_category=(
                ParticipantActivityCategory.WEIGHT
                if i == ParticipantActivityEnum.WEIGHT
                else ParticipantActivityCategory.ACTIVITY
            ),
        )
        for i in list(ParticipantActivityEnum)
        if i != ParticipantActivityEnum.GROUP  # Skip coaching_calls
    ]

    for section in sections:
        section_resp = await create_program_module_section(
            program_module_id=module_resp.id,
            program_module_section_data=section,
        )
    return program_resp, module_resp, section_resp  # type: ignore


async def create_authorized_user(role: AutorizedRole = AutorizedRole.ADMIN):
    person = mimesis.Person()
    resp = await Authorized.create(
        email=person.email(),
        first_name=person.first_name(),
        last_name=person.last_name(),
        role=role,
        classes_admin=True,
        content_admin=True,
    )

    return resp


class DateWhen(Enum):
    NOW = 1
    FUTURE = 2
    PAST = 3
    YESTERDAY = 4


async def create_cohort(
    program_id: uuid.UUID, created_by: uuid.UUID, cohort_date: DateWhen
):
    now = pendulum.now()

    date_options = {
        DateWhen.NOW: [now],
        DateWhen.FUTURE: [now.add(days=days_n) for days_n in range(8, 100, 2)],
        DateWhen.PAST: [now.subtract(days=days_n) for days_n in range(8, 100, 2)],
        DateWhen.YESTERDAY: [now.subtract(days=1)],
    }

    date_candidates = date_options.get(cohort_date, [])
    start_date = now if not date_candidates else random.choice(date_candidates)

    datetime_string = start_date.to_datetime_string()

    cohort_resp = await process(
        name=f"Test Cohort {now.isoformat()}",
        program_id=program_id,
        limit=random.randint(0, 100),
        started_at=datetime.fromisoformat(datetime_string),
        created_by=created_by,
    )

    return cohort_resp


async def create_participant(disenrolled: bool = False):
    person = mimesis.Person()
    participant_id = uuid.uuid4()
    email = person.email()
    first_name = person.first_name()
    last_name = person.last_name()
    participant_resp = await Participant.create(
        id=participant_id,
        first_name=first_name,
        last_name=last_name,
        email=email,
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.DELETED if disenrolled else ParticipantStatus.ACTIVE,
        is_test=False,
        medical_record=person.username(),
        cognito_sub=participant_id,
    )
    program_id = random.choice(["NDPP", "IBC", "HWM"])
    solera_participant_resp = await SoleraParticipant.create(
        participant_id=participant_resp.id,
        solera_id=uuid.uuid4(),
        solera_key=person.username(),
        solera_program_id=program_id,
        solera_enrollment_id=person.username(),
    )

    metadata = {
        "id": random.randrange(1, **********),
        "email": email,
        "programId": program_id,
        "given_name": first_name,
        "family_name": last_name,
        "patientId": str(uuid.uuid4()),
        "gender": person.gender(),
        "address": "home",
        "payorId": str(uuid.uuid4()),
        "birthdate": person.birthdate().strftime("%m-%d-%Y"),
        "ethnicity": person.political_views(),
        "painScale": person.political_views(),
        "heightFeet": person.height(),
        "likertScore": person.height(),
        "programType": "DiabetesPrevention",
        "enrollmentId": str(uuid.uuid4()),
        "heightInches": person.height(),
        "phone_number": person.phone_number(),
        "insuranceType": person.political_views(),
        "commitmentDate": person.birthdate().strftime("%Y-%d-%m"),
        "languagePreference": person.language(),
        "physicalActivityLevel": person.political_views(),
        "milestoneStructureCode": "M7A3",
        "solera_uuid": str(uuid.uuid4()),
    }
    if disenrolled:
        metadata["disenrolledReason"] = "Reason"
        metadata["disenrolledDate"] = pendulum.now().isoformat()

    meta_resp = await ParticipantMeta.create(
        participant_id=participant_resp.id, metadata=metadata
    )
    return participant_resp, solera_participant_resp, meta_resp


async def add_participant_to_cohort(cohort_id: uuid.UUID, participant_id: uuid.UUID):
    resp = await CohortMembersRepository.create_cohort_member(
        cohort_id=cohort_id, participant_id=participant_id
    )
    return resp


async def create_participant_activity(participant_id):
    resp = await ParticipantActivityRepository.create_participant_activity(
        ParticipantActivityInput(
            participant_id=participant_id,
            value=random.randint(0, 100).__str__(),
            unit=ActivityUnit.ACTION,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT,
            activity_category=ParticipantActivityCategory.ACTIVITY,
            activity_type=ParticipantActivityEnum.ARTICLE,
        )
    )
    return resp


async def create_participant_weight_activity(participant_id):
    resp = await ParticipantActivityRepository.create_participant_activity(
        ParticipantActivityInput(
            participant_id=participant_id,
            value=int(200).__str__(),
            unit=ActivityUnit.KG,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT,
            activity_category=ParticipantActivityCategory.WEIGHT,
            activity_type=ParticipantActivityEnum.WEIGHT,
        )
    )
    return resp


async def create_webinar(
    host_id: uuid.UUID,
    topic: TopicEnum = TopicEnum.INTRO_SESSION,
):
    resp = await Webinar.create(
        topic=topic,
        title=f"Test Webinar  {pendulum.now().timestamp()}",
        description=f"Test Webinar Description {pendulum.now().timestamp()}",
        max_capacity=250,
        timezone="America/New_York",
        recurrence=RecurrenceEnum.WEEKLY,
        host_id=host_id,
        duration=60,
    )
    return resp


async def create_live_session(
    webinar_id: uuid.UUID,
    meeting_start_time: datetime = datetime.now(),
    add_recording: bool = False,
):
    resp = await LiveSession.create(
        webinar_id=webinar_id,
        meeting_start_time=meeting_start_time,
        title="Test Live Session",
        description="Test Live Session Description",
        recording_url=(
            "https://youtu.be/4JkIs37a2JE?si=oU6GFFcGbAIR-j0W"
            if add_recording
            else None,
        ),
    )
    return resp


async def create_booking(
    participant_id: uuid.UUID,
    live_session_id: uuid.UUID,
    status=BookingStatusEnum.BOOKED,
):
    resp = await Booking.create(
        participant_id=participant_id,
        live_session_id=live_session_id,
        status=status,
    )
    return resp


async def create_content_material(
    programs: list[uuid.UUID],
    activity_types: list[ParticipantActivityEnum] = None,
    user_id: Optional[uuid.UUID] = None,
) -> ContentMaterial:
    content_id = await ContentMaterialRepository.add_new_material(
        NewMaterialData(
            title="Test Material",
            description="Test Material Description",
            mime_type="form/type_form",
            form_id="123456789",
            activity_types=(
                activity_types if activity_types else [ParticipantActivityEnum.QUIZ]
            ),
            tags=[MaterialTag.ACTIVITY, MaterialTag.MOTIVATION],
            programs=programs,
        ),
        author_id=user_id,
    )

    content = await ContentMaterial.filter(id=content_id).first()

    return content


async def clear_db():
    await Program.all().delete()
    await ProgramModule.all().delete()
    await ProgramModuleSection.all().delete()
    await Authorized.all().delete()
    await Participant.all().delete()
    await SoleraParticipant.all().delete()
    await ParticipantMeta.all().delete()
    await Cohort.all().delete()
    await CohortMembers.all().delete()
    await Webinar.all().delete()
    await LiveSession.all().delete()
    await Booking.all().delete()
    await ContentMaterial.all().delete()
