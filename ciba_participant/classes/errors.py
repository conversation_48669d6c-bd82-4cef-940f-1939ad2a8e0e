class HostPermissionError(Exception):
    def __init__(self, message="Permission denied"):
        self.message = message
        super().__init__(self.message)


class LiveSessionError(Exception):
    def __init__(self, message="Live Session error"):
        self.message = message
        super().__init__(self.message)


class WebinarError(Exception):
    def __init__(self, message="Class error"):
        self.message = message
        super().__init__(self.message)
