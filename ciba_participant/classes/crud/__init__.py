from uuid import UUID

from tortoise.expressions import Q

from ciba_participant.classes.crud.live_sessions import (
    get,
    update,
    upload_recording,
    update_recording,
    delete_recording,
)
from ciba_participant.classes.crud import (
    create_webinar,
    get_webinars,
    get_webinar,
    update_webinar,
    delete_webinar,
)
from ciba_participant.classes.models import Booking, BookingStatusEnum
from ciba_participant.participant.models import RawParticipant


class LiveSessionRepository:
    @staticmethod
    async def get_participants_by_live_session_id(
        live_session_id: UUID,
    ) -> list[RawParticipant]:
        bookings = (
            await Booking.filter(
                Q(live_session_id=live_session_id) & ~Q(status=BookingStatusEnum.CANCELED)
            )
            .prefetch_related("participant")
            .all()
        )

        return [
            RawParticipant.model_validate(booking.participant) for booking in bookings
        ]

    update_live_session = staticmethod(update)
    get_live_session = staticmethod(get)
    upload_live_session_recording = staticmethod(upload_recording)
    update_live_session_recording = staticmethod(update_recording)
    delete_live_session_recording = staticmethod(delete_recording)


class WebinarRepository:
    get_webinar = staticmethod(get_webinar.process)
    get_webinars = staticmethod(get_webinars.process)
    create_webinar = staticmethod(create_webinar.process)
    update_webinar = staticmethod(update_webinar.process)
    delete_webinar = staticmethod(delete_webinar.process)
