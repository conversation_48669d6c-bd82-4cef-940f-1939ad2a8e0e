from uuid import UUID

from tortoise.transactions import in_transaction

from ciba_participant.classes.errors import HostPermissionError
from ciba_participant.classes.service import check_class_admin
from ciba_participant.participant.models import Authorized
from ciba_participant.classes.models import (
    Webinar,
)
from ciba_participant.classes.crud.live_sessions import create as create_live_sessions
from ciba_participant.classes.crud.types import WebinarCreate
from ciba_participant.error_messages.classes import (
    ERROR_NOT_HOST,
)


async def process(data: WebinarCreate, user_id: UUID) -> UUID:
    async with in_transaction():
        await check_class_admin(user_id)

        host = await Authorized.filter(id=data.host_id).first()

        if host is None:
            raise HostPermissionError(message=ERROR_NOT_HOST)

        webinar = await Webinar.create(
            **data.model_dump(
                exclude={
                    "number_of_sessions",
                }
            ),
        )

        await create_live_sessions(data=data, host=host, webinar_id=webinar.id)

        return webinar.id
