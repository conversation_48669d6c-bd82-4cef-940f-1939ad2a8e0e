from uuid import UUID

import pendulum
from tortoise.transactions import in_transaction

from ciba_participant.cohort.models import (
    CohortMembers,
    CohortMembershipStatus,
    Cohort,
    CohortStatusEnum,
)
from ciba_participant.common.aws_handler import SQSNotification, NotificationType
from ciba_participant.log.logging import logger
from ciba_participant.participant.models import ParticipantStatus, Participant


class CohortNotEndedException(ValueError):
    """Cohort is not near end"""


async def process(cohort_id: UUID) -> bool:
    """
    Process the end of a cohort by updating its status to COMPLETED,
    updating the status of all active participants to COMPLETED, and
    logging the result.

    Args:
        cohort_id (UUID): The ID of the cohort to process.

    Returns:
        bool: True if the processing was successful, False otherwise.

    Raises:
        ValueError: If the cohort is not found
        CohortNotEndedException: If the cohort is not near end
        Exception: If there is an error during the processing.
    """
    cohort = (
        await Cohort.filter(id=cohort_id).prefetch_related("program_modules").first()
    )

    if not cohort:
        raise ValueError(f"Cohort with ID {cohort_id} not found")

    if await cohort.end_date > pendulum.now("UTC").add(days=28):
        raise CohortNotEndedException

    try:
        async with in_transaction():
            await Cohort.filter(id=cohort_id).update(status=CohortStatusEnum.COMPLETED)

            participant_ids = await CohortMembers.filter(
                cohort_id=cohort_id, status=CohortMembershipStatus.ACTIVE
            ).values_list("participant_id", flat=True)

            if participant_ids:
                await Participant.filter(id__in=participant_ids).update(
                    status=ParticipantStatus.COMPLETED
                )

            logger.info(
                f"Successfully processed cohort {cohort_id} with {len(participant_ids)} participants"
            )


    except Exception as e:
        logger.error(f"Failed to process cohort end {cohort_id}: {str(e)}")
        raise

    notification = SQSNotification(
        type=NotificationType.SQS,
        email_event=EmailNotificationEvent.CANCELLED_SESSION,
        data={
            "email": participant.email,
            "first_name": participant.first_name,
            "class_name": live_session.title,
            "class_date": class_date,
        },
    )

    try:
        send_to_sqs(
            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
            message_body=notification.model_dump_json(),
        )
    except Exception as e:
        raise LiveSessionError(ERROR_SENDING_EMAIL) from e

    return True
