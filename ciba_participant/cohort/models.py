from datetime import datetime
from hashlib import md5
from typing import TYPE_CHECKING, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, ConfigDict
from tortoise.fields import (
    CASCADE,
    CharField,
    DatetimeField,
    ForeignKeyField,
    ForeignKeyRelation,
    IntField,
    JSONField,
    ManyToManyField,
    ManyToManyRelation,
    ReverseRelation,
    UUIDField,
    CharEnumField,
)
from tortoise.models import Model

from ciba_participant.common.models import TimestampMixin
from ciba_participant.participant.models import (
    Authorized,
    Participant,
    RawAuthorized,
    RawParticipant,
    ParticipantStatus,
)
from enum import Enum
from tortoise.manager import Manager

if TYPE_CHECKING:
    from ciba_participant.program.models import Program, ProgramModule


class CohortMembershipStatus(Enum):
    ACTIVE = "active"
    DELETED = "deleted"


class CohortStatusEnum(Enum):
    ACTIVE = "active"
    COMPLETED = "completed"


class Cohort(Model, TimestampMixin):
    id = UUIDField(primary_key=True)
    name = CharField(max_length=255, null=True, unique=True)
    started_at = DatetimeField(null=True)
    limit = IntField(null=True)
    status = CharEnumField(CohortStatusEnum, default=CohortStatusEnum.ACTIVE.value)

    program_id: UUID
    program: ForeignKeyRelation["Program"] = ForeignKeyField(
        "models.Program", related_name="cohorts"
    )

    created_by_id: UUID
    created_by: ForeignKeyRelation["Authorized"] = ForeignKeyField(
        "models.Authorized", related_name="created_cohorts", on_delete=CASCADE
    )

    participants: ManyToManyRelation["Participant"] = ManyToManyField(
        "models.Participant",
        related_name="cohort",
        through="cohort_members",
    )

    program_modules: ReverseRelation["CohortProgramModules"]

    class Meta:
        table = "cohort"

    @property
    def unique_name(self) -> str:
        """Generate unique group name"""
        return md5(f"{self.name}-{self.id}".encode()).hexdigest()

    # Define a property to get only active participants
    @property
    async def active_participants(self):
        """Get only active participants for this cohort."""
        active_memberships = await CohortMembers.filter(
            cohort_id=self.id,
            status=CohortMembershipStatus.ACTIVE,
            participant__status=ParticipantStatus.ACTIVE,
        ).prefetch_related("participant")
        return [membership.participant for membership in active_memberships]

    @property
    async def end_date(self) -> Optional[datetime]:
        """Get the latest end date from all program modules in this cohort."""
        latest_module = await self.program_modules.order_by("-ended_at").first()
        return latest_module.ended_at if latest_module else None


class RawCohort(BaseModel):
    """
    Raw representation of cohort as develivered from database with no relationships

    Use this to convert from Cohort to a Pydantic model

        cohort = await Cohort.get(id=cohort_id)
        cohort_output = RawCohort.model_validate(cohort)
    """

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    name: Optional[str] = None
    started_at: Optional[datetime] = None
    limit: Optional[int] = None
    status: Optional[CohortStatusEnum] = None
    end_date: Optional[datetime] = None
    program_id: UUID
    created_by_id: UUID


class FullCohort(RawCohort):
    """
    Full representation of cohort as develivered from database with all relationships
    """

    program: Optional["RawProgram"]
    participants: Optional[list["RawParticipant"]]
    created_by: Optional["RawAuthorized"]
    program_modules: Optional[list["RawCohortProgramModules"]]


class ActiveCohortMembersManager(Manager):
    """CohortMembers manager to return only active members."""

    def get_queryset(self):
        return (
            super(ActiveCohortMembersManager, self)
            .get_queryset()
            .filter(status=CohortMembershipStatus.ACTIVE)
        )


class CohortMembers(Model, TimestampMixin):
    cohort_id: UUID
    cohort = ForeignKeyField("models.Cohort")
    participant = ForeignKeyField(
        "models.Participant", related_name="cohorts", on_delete=CASCADE
    )
    status = CharEnumField(
        CohortMembershipStatus, default=CohortMembershipStatus.ACTIVE
    )

    objects = ActiveCohortMembersManager()
    all_objects = Manager()

    class Meta:
        table = "cohort_members"
        unique_together = (("cohort", "participant_id"),)
        ordering = ["created_at"]


class CohortProgramModules(Model):
    id = UUIDField(primary_key=True, default=uuid4)
    cohort_id: UUID
    cohort: ForeignKeyRelation["Cohort"] = ForeignKeyField(
        "models.Cohort", related_name="program_modules", on_delete=CASCADE
    )
    program_module_id: UUID
    program_module: ForeignKeyRelation["ProgramModule"] = ForeignKeyField(
        "models.ProgramModule", related_name="cohort"
    )
    started_at = DatetimeField()
    ended_at = DatetimeField()
    metadata = JSONField(null=True)

    class Meta:
        table = "cohort_program_modules"
        unique_together = (("cohort", "program_module"),)

    def __str__(self):
        return f"{self.cohort.name} - {self.program_module.title}"


class RawCohortProgramModules(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    cohort_id: UUID
    program_module_id: UUID
    started_at: datetime
    ended_at: datetime
    metadata: Optional[dict] = None


from ciba_participant.program.models import RawProgram  # noqa: E402

FullCohort.model_rebuild()
