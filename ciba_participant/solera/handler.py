from typing import Optional, Union
from uuid import uuid4

from fastapi import HTT<PERSON><PERSON>x<PERSON>
from starlette.status import HTTP_404_NOT_FOUND
from tortoise.transactions import in_transaction

from ciba_participant.chat_api.chat_api import assign_participant_to_chat
from ciba_participant.participant.user_service import UserService
from ciba_participant.program.models import Program
from ciba_participant.solera.api import SoleraAPIAsyncClient
from ciba_participant.log.logging import logger
from ciba_participant.participant.models import Participant, ParticipantStatus
from ciba_participant.cohort.models import Cohort
from ciba_participant.program.data_preparer import DataPreparer
from ciba_participant.settings import ENV, get_settings
from datetime import datetime, timedelta
from ciba_participant.common.aws_handler import send_to_sqs, SQSNotification, NotificationType, EmailNotificationEvent

settings = get_settings()


class RegistrationFailed:
    pass


class ParticipantDuplicatedError:
    pass


class SoleraHandler:
    """Service that keeps enroll flow logic for solera."""

    program_dict = {
        "NDPP": "NDPP",
        "IBC": "IBC",
        "HWM": "HWM",
    }

    def __init__(self) -> None:
        self.solera_company = "Solera"

    async def get_solera_user_info(
            self, solera_key: str
    ) -> dict | HTTPException:
        response = await SoleraAPIAsyncClient(
            client_id=settings.SOLERA_CLIENT_ID,
            client_secret=settings.SOLERA_CLIENT_SECRET,
            environment=settings.ENV,
        ).get_user_info(solera_key)
        return response

    async def create_user_no_cohort(self, key: str) -> Union[Participant, ParticipantDuplicatedError, RegistrationFailed]:
        solera_user_info = await self._get_solera_user_info_safely(key)
        if not solera_user_info:
            return RegistrationFailed()

        email = solera_user_info.pop("email", "").lower()
        if not email:
            logger.error("Email not found in Solera user info.")
            return RegistrationFailed()

        participant = await self._get_existing_participant(email)
        if participant:
            if participant.status in [ParticipantStatus.PENDING, ParticipantStatus.ACTIVE]:
                return participant
            else:
                return ParticipantDuplicatedError()

        user_dict = self._prepare_user_dict(email, solera_user_info, key)

        logger.info(f"Solera user info: {solera_user_info}")
        logger.info(f"Program ID: {user_dict.get('solera_program_id')}")

        async with in_transaction():
            cohort_exists = await self._has_available_cohort(user_dict["solera_program_id"])
            if not cohort_exists:
                return RegistrationFailed()

            participant = await self._create_new_user(user_dict)
            if participant:
                return participant
            else:
                return RegistrationFailed()

    async def _get_solera_user_info_safely(self, key: str) -> Optional[dict]:
        try:
            solera_user_info = await self.get_solera_user_info(key)
            if isinstance(solera_user_info, dict):
                return solera_user_info
        except HTTPException as e:
            logger.exception(f"HTTPException while fetching Solera user info: {e}")
        except Exception as e:
            logger.exception(f"Exception while fetching Solera user info: {e}")
        return None

    async def _get_existing_participant(self, email: str) -> Optional[Participant]:
        return await Participant.filter(email=email).first()

    def _prepare_user_dict(self, email: str, solera_user_info: dict, key: str) -> dict:
        program_id = solera_user_info.pop("programId", "")
        if not program_id:
            logger.error("Program ID is missing from Solera user info.")
            raise ValueError("Program ID is required")

        user_dict = {
            "email": email,
            "first_name": solera_user_info.pop("given_name", ""),
            "last_name": solera_user_info.pop("family_name", ""),
            "group_id": uuid4(),
            "member_id": uuid4(),
            "medical_record": Participant.generate_medical_record(),
            "is_test": settings.ENV in [ENV.TEST, ENV.DEV, ENV.LOCAL],
            "solera_key": key,
            "solera_id": solera_user_info.pop("patientId", ""),
            "solera_program_id": program_id,
            "solera_enrollment_id": solera_user_info.get("enrollmentId", ""),
            "metadata": solera_user_info,
        }
        return user_dict

    async def _has_available_cohort(self, program_id: str) -> bool:
        tomorrow = datetime.now().date() + timedelta(days=1)
        return await Cohort.filter(
            started_at__gte=tomorrow,
            program__title=program_id
        ).exists()

    async def _create_new_user(self, user_dict: dict) -> Optional[Participant]:
        user_service = UserService()
        try:
            is_created = await user_service.create_user(user_dict)
            if is_created:
                return user_service.participant
            else:
                logger.error("UserService failed to create user.")
        except Exception as e:
            logger.exception(f"Exception while creating new user: {e}")
        return None

    async def create_user(
            self,
            key: str,
            correlation_id: str = "-"
    ) -> Optional[Participant]:

        solera_user_info = await self.get_solera_user_info(key)
        if isinstance(solera_user_info, dict):
            email = solera_user_info.pop("email").lower()

            # check if user exists
            exist = await Participant.filter(email=email).exists()
            if exist:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail="User already exists",
                )
            program_id = solera_user_info.pop("programId")
            user_dict = {
                "email": email,
                "first_name": solera_user_info.pop("given_name"),
                "last_name": solera_user_info.pop("family_name"),
                "group_id": str(uuid4()),
                "member_id": str(uuid4()),
                "medical_record": Participant.generate_medical_record(),
                "solera_key": key,
                "solera_id": solera_user_info.pop("patientId"),
                "solera_program_id": program_id,
                "solera_enrollment_id": solera_user_info["enrollmentId"],
                "metadata": solera_user_info,
            }
            logger.info(solera_user_info)
            logger.info(program_id)
            async with in_transaction():
                await self._check_program(program_id)
                user_service = UserService()
                await user_service.create_user(user_dict)

                participant = user_service.participant
                await self._add_program(participant, program_id)
                if participant is not None:
                    if settings.ENV == ENV.PROD:
                        notification = SQSNotification(
                            type=NotificationType.SQS,
                            email_event=EmailNotificationEvent.NEW_PARTICIPANT,
                            data={
                                "participant_id": participant.id,
                            },
                            correlation_id=correlation_id
                        )

                        send_to_sqs(
                            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                            message_body=notification.model_dump_json()
                        )
                    await user_service.update_status(ParticipantStatus.ACTIVE)
                return participant
        return None

    async def _check_program(
            self, program_type: str
    ) -> Program | None:
        program_type = (
            program_type if program_type else self.program_dict["NDPP"]
        )
        cohort_list = await DataPreparer().prepare_data(program_type)
        if not cohort_list:
            return Program()
        return None

    async def _add_program(
            self,
            participant: Optional[Participant] = None,
            program_type: Optional[str] = None,
    ) -> None:
        if participant is not None:
            if program_type is None:
                program_type = self.program_dict["NDPP"]
            program_group_list = await DataPreparer().prepare_data(
                program_type
            )
            if not program_group_list:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail="Program group not found",
                )
            if program_group_list and program_group_list[0]["id"]:
                cohort = await Cohort.filter(
                    id=str(program_group_list[0]["id"])
                ).prefetch_related("participants").get()
                await cohort.participants.add(participant)
                # not needed for now
                if settings.ENV != ENV.LOCAL:
                    await assign_participant_to_chat(
                        cohort.unique_name,
                        participant.id,
                        participant.chat_identity,
                    )
