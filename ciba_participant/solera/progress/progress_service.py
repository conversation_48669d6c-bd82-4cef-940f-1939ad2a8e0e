from abc import ABC, abstractmethod

from tortoise.transactions import atomic


class SoleraProgress(ABC):
    """ABC class for template_method for diff types of activities"""

    @atomic()
    async def create_progress(self) -> None:
        """template_method that have all flow to progree in solera"""

        await self.add_activity()
        await self.add_milestone()
        await self.send_activity()

    @abstractmethod
    async def add_activity(self) -> None:
        """add activity to bd return uuiid"""

        pass

    @abstractmethod
    async def add_milestone(self) -> None:
        """add milestone to bd"""

        pass

    @abstractmethod
    async def send_activity(self) -> None:
        """send activity to solera return reffId"""

        pass
