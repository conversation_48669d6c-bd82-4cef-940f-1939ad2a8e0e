import asyncio
from ciba_participant.solera.api import SoleraAPIAsyncClient
import json
from ciba_participant.settings import get_settings

settings = get_settings()


async def main(lookup_key):
    client = SoleraAPIAsyncClient(
        settings.SOLERA_CLIENT_ID,
        settings.SOLERA_CLIENT_SECRET,
        environment=settings.ENV,
    )
    user_info = await client.get_user_info(lookup_key)
    print("User Info:", json.dumps(user_info, indent=2))

    activities = [
        {
            "userId": "example_user_id",
            "enrollmentId": "example_enrollment_id",
            "referenceId": "example_reference_id",
            "programId": "example_program_id",
            "timestamp": "2024-06-19T01:02:03.000Z",
            "data": {"Enrollment": True},
        }
    ]
    activity_response = await client.submit_activity(activities)
    print("Activity Response:", json.dumps(activity_response, indent=2))

    request_id = "example_request_id"
    activity_status = await client.get_activity_status(request_id)
    print("Activity Status:", json.dumps(activity_status, indent=2))

    program_id = "example_program_id"
    user_id = "example_user_id"
    enrollment_status = await client.get_enrollment_status(program_id, user_id)
    print("Enrollment Status:", json.dumps(enrollment_status, indent=2))

    milestone_status = await client.get_milestone_status(program_id, user_id)
    print("Milestone Status:", json.dumps(milestone_status, indent=2))

    corrected_activity_response = await client.correct_activity(activities)
    print(
        "Corrected Activity Response:",
        json.dumps(corrected_activity_response, indent=2),
    )


def init_solera_client():
    return SoleraAPIAsyncClient(
        settings.SOLERA_CLIENT_ID,
        settings.SOLERA_CLIENT_SECRET,
        environment=settings.ENV,
    )


async def run_enrollment(client: SoleraAPIAsyncClient):
    enrolled_users_df = pd.read_csv("corrected_enrollment_data_ready_to_send.csv")

    correction_data = []
    for activity in enrolled_users_df.to_dict(orient="records"):
        # solera_enrollment_id = activity['solera_enrollment_id']
        created_at = activity["created_at"]
        activity_with_ref = json.loads(activity["data"])["activities"]

        corrected_activities_payloads = await client.correct_activity(activity_with_ref)
        for corrected_activity, correction_payload in corrected_activities_payloads:
            correction_data.append(
                (
                    created_at,
                    json.dumps(corrected_activity),
                    json.dumps(correction_payload),
                )
            )

    corrected_activity_map = {
        solera_id: corrected_activity
        for solera_id, corrected_activity, _ in correction_data
    }
    correction_payload_map = {
        solera_id: correction_payload
        for solera_id, _, correction_payload in correction_data
    }

    # Map the new columns to the DataFrame
    enrolled_users_df["corrected_activity"] = enrolled_users_df["created_at"].map(
        corrected_activity_map
    )
    enrolled_users_df["correction_payload"] = enrolled_users_df["created_at"].map(
        correction_payload_map
    )

    enrolled_users_df.to_csv("ready_corrected_enrollment_data.csv", index=False)


async def run_other_activities(client: SoleraAPIAsyncClient):
    activities_df = pd.read_csv("final_filtered_no_solera_milestones.csv")
    correction_data = []
    for activity in activities_df.to_dict(orient="records"):
        # solera_enrollment_id = activity['solera_enrollment_id']
        created_at = activity["created_at"]
        activity_with_ref = json.loads(activity["data"])["activities"]

        corrected_activities_payloads = await client.correct_activity(activity_with_ref)
        for corrected_activity, correction_payload in corrected_activities_payloads:
            correction_data.append(
                (
                    created_at,
                    json.dumps(corrected_activity),
                    json.dumps(correction_payload),
                )
            )
    corrected_activity_map = {
        solera_id: corrected_activity
        for solera_id, corrected_activity, _ in correction_data
    }
    correction_payload_map = {
        solera_id: correction_payload
        for solera_id, _, correction_payload in correction_data
    }

    # Map the new columns to the DataFrame
    activities_df["corrected_activity"] = activities_df["created_at"].map(
        corrected_activity_map
    )
    activities_df["correction_payload"] = activities_df["created_at"].map(
        correction_payload_map
    )
    activities_df.to_csv("ready_other_activities.csv", index=False)


async def submit_enroll_activities(client: SoleraAPIAsyncClient):
    enrolls = pd.read_csv("ready_corrected_enrollment_data.csv")
    results = []

    for enroll in enrolls.to_dict(orient="records"):
        enroll_correction_payload = json.loads(enroll["correction_payload"])
        enroll_corrected_activity = json.loads(enroll["corrected_activity"])

        try:
            activity_resp = await client.submit_activity(enroll_corrected_activity)
        except Exception as e:
            print(
                f"Error submitting activity for {enroll['solera_enrollment_id']}: {e}"
            )
            enroll["status"] = "error"
            enroll["error"] = str(e)
        else:
            enroll["status"] = "success"
            enroll["activity_response"] = json.dumps(activity_resp)

        try:
            correction_resp = await client.submit_activity(enroll_correction_payload)
        except Exception as e:
            print(
                f"Error submitting correction for {enroll['solera_enrollment_id']}: {e}"
            )
            enroll["correction_status"] = "error"
            enroll["correction_error"] = str(e)
        else:
            enroll["correction_status"] = "success"
            enroll["correction_response"] = json.dumps(correction_resp)

        # Append the result for each enroll
        results.append(enroll)

    # Create a new DataFrame from the results
    results_df = pd.DataFrame(results)
    # Save the results to a new CSV file
    results_df.to_csv("submitted_corrected_enrollment_data.csv", index=False)


async def submit_other_activities(client: SoleraAPIAsyncClient):
    others = pd.read_csv("ready_other_activities.csv")
    results = []

    for other in others.to_dict(orient="records"):
        other_correction_payload = json.loads(other["correction_payload"])
        other_corrected_activity = json.loads(other["corrected_activity"])

        try:
            activity_resp = await client.submit_activity(other_corrected_activity)
        except Exception as e:
            print(f"Error submitting activity for {other['solera_enrollment_id']}: {e}")
            other["status"] = "error"
            other["error"] = str(e)
        else:
            other["status"] = "success"
            other["activity_response"] = json.dumps(activity_resp)

        try:
            correction_resp = await client.submit_activity(other_correction_payload)
        except Exception as e:
            print(
                f"Error submitting correction for {other['solera_enrollment_id']}: {e}"
            )
            other["correction_status"] = "error"
            other["correction_error"] = str(e)
        else:
            other["correction_status"] = "success"
            other["correction_response"] = json.dumps(correction_resp)

        # Append the result for each other
        results.append(other)

    # Create a new DataFrame from the results
    results_df = pd.DataFrame(results)
    # Save the results to a new CSV file
    results_df.to_csv("submitted_corrected_other_data.csv", index=False)


a = 1


if __name__ == "__main__":
    import pandas as pd

    client = init_solera_client()
    asyncio.run(submit_enroll_activities(client))
    asyncio.run(submit_other_activities(client))
