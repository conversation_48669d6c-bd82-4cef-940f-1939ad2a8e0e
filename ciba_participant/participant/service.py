import base64
import datetime
import json
import logging
from typing import Any, Callable
from uuid import UUID, uuid4

import pendulum
from dateutil.parser import parse
from pydantic import BaseModel, Field
from strawberry.types import Info
from tortoise.transactions import atomic

from ciba_participant.participant.utils import generate_random_password
from ciba_participant.common.cognito import (
    admin_add_user_to_group,
    admin_confirm_sign_up,
    admin_create_user,
    admin_disable_user,
    admin_enable_user,
    admin_set_user_password,
    admin_update_email,
    forgot_password,
)
from ciba_participant.participant.models import (
    Participant,
    ParticipantStatus,
)

# from ciba_participant.solera.progress.progress_enrolled_service import SoleraProgressEnrolledService
from ciba_participant.settings import get_settings, ENV
from ciba_participant.utils import AESCipher
from ciba_participant.common.aws_handler import (
    send_to_sqs,
    SQSNotification,
    EmailNotificationEvent,
    NotificationType,
)
from ciba_participant.activity.models import (
    ActivityUnit,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ParticipantActivityEnum,
)
from ciba_participant.activity.models import ParticipantActivity

settings = get_settings()

RESET_PASSWORD_SALT = "resetPassword"
RESET_PASSWORD_LINK_EXPIRATION = 30  # days


class ParticipantService(BaseModel):
    participant_id: UUID | None = Field(default=None)
    email: str | None = Field(default=None)
    first_name: str | None = Field(max_length=255, default=None)
    last_name: str | None = Field(max_length=255, default=None)
    member_id: UUID | None = Field(default=None)
    group_id: UUID | None = Field(default=None)
    status: str | None = Field(max_length=100, default=None)
    is_test: bool = Field(default=False)

    async def sign_up(self, info: Info) -> Participant | None:
        """Participant signup logic.

        Make sure participant is not duplicated.
        Send welcome email.
        """
        correlation_id = info.context.request.headers.get(
            "X-Request-ID", str(pendulum.now().int_timestamp)
        )
        if await Participant.filter(email=self.email).count():
            return None
        response = self.invoke_cognito(
            admin_create_user,
            self.email,
        )

        self.invoke_cognito(
            admin_add_user_to_group,
            self.email,
        )

        participant = Participant(
            id=response["User"]["Username"],
            email=self.email,
            first_name=self.first_name,
            last_name=self.last_name,
            member_id=self.member_id,
            group_id=self.group_id,
            is_test=self.is_test,
            medical_record=Participant.generate_medical_record(),
        )
        await participant.save()
        notification = SQSNotification(
            type=NotificationType.SQS,
            email_event=EmailNotificationEvent.NEW_PARTICIPANT,
            data={
                "participant_id": participant.id,
            },
            correlation_id=correlation_id,
        )
        send_to_sqs(
            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
            message_body=notification.model_dump_json(),
        )
        return participant

    async def verify_change_password_token(self, token: str, info: Info) -> dict:
        """Verify change password token.

        If data is empty or any other error has happened return error.
        """
        data = self.decode_activation_token(token)
        if not data:
            return {"InvalidVerifyChangePasswordToken": "Invalid token"}
        data["email"] = data["email"].lower()
        participant = await Participant.filter(email=data["email"]).get()
        # if token has been used, return VerifyChangePasswordTokenExpired
        if (
            participant.last_reset
            and parse(data["last_reset"]) < participant.last_reset
        ):
            return {"VerifyChangePasswordTokenExpired": "Token expired"}
        # if token has been created more than RESET_PASSWORD_LINK_EXPIRATION
        # days, return VerifyChangePasswordTokenExpired
        if parse(data["last_reset"]) + datetime.timedelta(
            days=RESET_PASSWORD_LINK_EXPIRATION
        ) < datetime.datetime.now(datetime.timezone.utc):
            return {"VerifyChangePasswordTokenExpired": "Token expired"}
        if participant.last_reset is None:
            solera_participant = await participant.solera_participant.filter(
                participant_id=participant.id
            ).first()
            if solera_participant.solera_program_id is not None:
                notification = SQSNotification(
                    type=NotificationType.SQS,
                    email_event=EmailNotificationEvent.WELCOME_PARTICIPANT,
                    data={"participant_id": participant.id},
                    correlation_id=info.context.request.headers.get(
                        "X-Request-ID", str(pendulum.now().int_timestamp)
                    ),
                )
                send_to_sqs(
                    queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                    message_body=notification.model_dump_json(),
                )
        participant.last_reset = datetime.datetime.now(datetime.timezone.utc)
        await participant.save()
        return {"email": data["email"], "tmp_password": data["tmp_password"]}

    @staticmethod
    def decode_activation_token(token: str) -> dict:
        """Decode activation token."""
        try:
            decoded_token = base64.urlsafe_b64decode(token.encode())
            decoded = AESCipher(settings.SECRET_KEY + RESET_PASSWORD_SALT).decrypt(
                decoded_token
            )
            data = json.loads(decoded)
        except Exception as ex:  # pylint: disable=broad-except
            logging.error(ex)
            data = {}
        return data

    async def forgot_password(self, email: str) -> bool:
        """Forgot password flow."""
        participant = await Participant.filter(email=email).first()
        if participant:
            self.invoke_cognito(
                forgot_password,
                participant.email,
            )
        return True

    @atomic()
    async def update_status(self, info: Info) -> Participant | None:
        """Participant change status logic.

        On confirm create user in Cognito and send confirm email.
        On reject send reject email.
        """
        participant = await Participant.filter(id=self.participant_id).get_or_none()
        if not participant:
            return None
        status_changed = self.status != participant.status

        # handle status changes
        if status_changed and self.status == ParticipantStatus.ACTIVE.value:
            await self._on_active(info, participant)
        if status_changed and self.status == ParticipantStatus.REJECTED.value:
            self._on_reject(info, participant)
        if status_changed and self.status == ParticipantStatus.PENDING.value:
            self._on_pending(participant)

        participant.status = self.status  # type: ignore
        await participant.save()
        return participant

    @atomic()
    async def update_email(self) -> bool:
        """Changes the registered email of a participant.

        First, it replaces the old email in the DB.
        Then, it also updates the email in cognito.
        Finally, it sends a password reset email.
        """
        participant = await Participant.filter(id=self.participant_id).get_or_none()

        if not participant:
            return False

        old_email = participant.email

        if self.email and self.email != old_email:
            participant.email = self.email
            logging.info(f"Updating DB - {old_email} -> {self.email}")
            await participant.save()

            logging.info(f"Updating cognito user email: {old_email} -> {self.email}")
            self.invoke_cognito(
                admin_update_email,
                old_email,
                self.email,
            )

            logging.info("Resetting password")
            self.invoke_cognito(
                forgot_password,
                self.email,
            )

            return True
        else:
            logging.error(f"Invalid email: {self.email}")
            return False

    async def set_new_password(
        self, info: Info, email: str, new_password: str, correlation_id: str = "-"
    ) -> Participant | None:
        correlation_id = (
            info.context.request.headers.get(
                "X-Request-ID", str(pendulum.now().int_timestamp)
            )
            if correlation_id == "-"
            else correlation_id
        )
        participant = (
            await Participant.filter(email=email)
            .prefetch_related("participant_meta")
            .first()
        )
        if not participant:
            return None

        if participant.status == ParticipantStatus.DELETED:
            self.invoke_cognito(admin_enable_user, participant.email)

        resp = self.invoke_cognito(
            admin_set_user_password, participant.email, new_password, True
        )
        logging.info(f"Set passwd: {resp}")
        participant.status = ParticipantStatus.ACTIVE.value
        participant.cognito_sub = participant.id
        await participant.save()
        await ParticipantActivity(
            participant_id=participant.id,
            value="1",
            unit=ActivityUnit.ACTION,
            activity_device=ParticipantActivityDevice.MANUAL_INPUT,
            activity_category=ParticipantActivityCategory.ACTIVITY,
            activity_type=ParticipantActivityEnum.ENROLL,
            section_id=None,
        ).save()

        notifications = [
            SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.WELCOME_PARTICIPANT,
                data={
                    "participant_id": participant.id,
                },
                correlation_id=correlation_id,
            )
        ]
        if settings.ENV == ENV.PROD:
            notification = SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.NEW_PARTICIPANT,
                data={
                    "participant_id": participant.id,
                },
                correlation_id=correlation_id,
            )
            notifications.append(notification)

        for notification in notifications:
            send_to_sqs(
                queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                message_body=notification.model_dump_json(),
            )
        return participant

    async def _on_active(self, info: Info, participant: Participant) -> None:
        notification = SQSNotification(
            type=NotificationType.SQS,
            correlation_id=info.context.request.headers.get(
                "X-Request-ID", str(pendulum.now().int_timestamp)
            ),
        )

        if not participant.cognito_sub:
            tmp_password = generate_random_password()
            self.invoke_cognito(
                admin_confirm_sign_up,
                participant.email,
            )
            self.invoke_cognito(
                admin_set_user_password,
                participant.email,
                tmp_password,
            )
            participant.cognito_sub = participant.id
            notification.email_event = EmailNotificationEvent.CONFIRM_BY_PARTICIPANT
            notification.data = {
                "email": participant.email,
                "first_name": participant.first_name,
                "tmp_password": self.get_totp(participant, tmp_password),
            }
        else:
            self.invoke_cognito(admin_enable_user, participant.email)
            notification.email_event = EmailNotificationEvent.ACTIVATE_PARTICIPANT
            notification.data = {
                "email": participant.email,
                "first_name": participant.first_name,
            }
        send_to_sqs(
            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
            message_body=notification.model_dump_json(),
        )

    def _on_reject(self, info: Info, participant: Participant) -> None:
        if participant.cognito_sub:
            self.invoke_cognito(admin_disable_user, participant.email)
        notification = SQSNotification(
            type=NotificationType.SQS,
            email_event=EmailNotificationEvent.REJECT_PARTICIPANT,
            data={
                "email": participant.email,
                "first_name": participant.first_name,
            },
            correlation_id=info.context.request.headers.get(
                "X-Request-ID", str(pendulum.now().int_timestamp)
            ),
        )
        send_to_sqs(
            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
            message_body=notification.model_dump_json(),
        )

    def _on_pending(self, participant: Participant) -> None:
        if participant.cognito_sub:
            self.invoke_cognito(admin_disable_user, participant.email)

    @staticmethod
    def get_totp(participant: Participant, tmp_password: str) -> str:
        """Generate one time token."""
        token = (
            AESCipher(settings.SECRET_KEY + RESET_PASSWORD_SALT)
            .encrypt(
                json.dumps(
                    {
                        "email": participant.email,
                        "tmp_password": tmp_password,
                        "last_reset": participant.last_reset.isoformat()
                        if participant.last_reset
                        else datetime.datetime.now(datetime.timezone.utc).isoformat(),
                    }
                )
            )
            .decode()
        )
        return base64.urlsafe_b64encode(token.encode()).decode()

    def invoke_cognito(self, command: Callable, *args: Any, **kwargs: Any) -> Any:
        """Invoke cognito command."""
        if settings.ENV in [ENV.LOCAL, ENV.TEST]:
            return {"User": {"Username": uuid4()}, "UserStatus": "CONFIRMED"}
        return command(*args, **kwargs)
