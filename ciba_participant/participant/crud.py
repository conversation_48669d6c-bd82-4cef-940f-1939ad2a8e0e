import math
from datetime import datetime, timezone
from typing import List, Optional
from enum import StrEnum, auto
from uuid import UUID

import pendulum
from loguru import logger
from strawberry.types import Info
from pydantic import BaseModel
from tortoise.expressions import Q
from tortoise.queryset import QuerySet

from ciba_participant import get_settings
from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.cohort.pydantic_models import CohortInfo
from ciba_participant.common.cognito import (
    admin_create_provider,
    admin_set_provider_password,
)
from ciba_participant.participant.models import (
    Authorized,
    HeadsUpParticipant,
    Participant,
    ParticipantMeta,
    RawAuthorized,
    SoleraParticipant,
    RawParticipant,
)
from ciba_participant.participant.pydantic_models import (
    AuthorizedCreate,
    AuthorizedPydantic,
    AuthorizedUpdate,
    HeadsUpParticipantCreate,
    HeadsUpParticipantPydantic,
    HeadsUpParticipantsList,
    HeadsUpParticipantUpdate,
    MetaParticipantsList,
    ParticipantCreate,
    ParticipantMetaCreate,
    ParticipantMetaPydantic,
    ParticipantMetaUpdate,
    ParticipantPydantic,
    ParticipantsList,
    ParticipantStatus,
    ParticipantUpdate,
    SoleraParticipantCreate,
    SoleraParticipantPydantic,
    SoleraParticipantsList,
    SoleraParticipantUpdate,
)
from ciba_participant.participant.service import ParticipantService
from ciba_participant.program.models import RawProgram, Program
from ciba_participant.settings import ENV

settings = get_settings()


class DateRange(BaseModel):
    start: datetime
    end: datetime


class FilterInput(BaseModel):
    search: Optional[str] = None
    cohort_id: Optional[UUID] = None
    created_by_id: Optional[UUID] = None
    program_id: Optional[UUID] = None
    date_enrolled_range: Optional[DateRange] = None
    participant_status: Optional[ParticipantStatus] = None


class FieldEnum(StrEnum):
    first_name = auto()
    last_name = auto()
    email = auto()
    cohort_name = auto()
    created_by_name = auto()
    program_title = auto()
    current_week = auto()


class OrderEnum(StrEnum):
    asc = auto()
    desc = auto()


class SortInput(BaseModel):
    field: Optional[FieldEnum] = None
    order: Optional[OrderEnum] = None


class ParticipantInfo(RawParticipant):
    phone: Optional[str] = None
    last_activity_date: Optional[datetime] = None
    date_enrolled: Optional[datetime] = None
    cohort: Optional[CohortInfo] = None
    dissenrolled_reason: Optional[str] = None
    disenrollment_date: Optional[datetime] = None


class GetParticipantsOutput(BaseModel):
    participants: list[ParticipantInfo]
    total_pages: int


SORT_KEY_MAPPING = {
    FieldEnum.first_name: "first_name",
    FieldEnum.last_name: "last_name",
    FieldEnum.email: "email",
    FieldEnum.cohort_name: "cohort__name",
    FieldEnum.current_week: "cohort__started_at",
    FieldEnum.program_title: "cohort__program__title",
    FieldEnum.created_by_name: "cohort__created_by__first_name",
}


def process_participant_cohort(participant: Participant) -> Optional[CohortInfo]:
    if not participant.cohort:
        return None
    if len(participant.cohort) > 1:
        logger.warning(
            "Participant {} has more than one cohort",
            participant.id,
        )
    for cohort in participant.cohort:
        current_week_program_module_raw = [
            module
            for module in cohort.program_modules
            if module.started_at <= datetime.now(timezone.utc) < module.ended_at
        ]

        current_week_title: Optional[str] = None
        current_week_module: Optional[str] = None

        if current_week_program_module_raw:
            current_week_program_module = current_week_program_module_raw[
                0
            ].program_module
            current_week_title = current_week_program_module.short_title
            current_week_module = f"Module {current_week_program_module.order}"

        program = RawProgram.model_validate(cohort.program)
        created_by = RawAuthorized.model_validate(cohort.created_by)

        cohort_info = CohortInfo(
            id=cohort.id,
            name=cohort.name,
            created_at=cohort.created_at,
            updated_at=cohort.updated_at,
            limit=cohort.limit,
            started_at=cohort.started_at,
            program_id=program.id,
            created_by_id=created_by.id,
            program=program,
            created_by=created_by,
            live_sessions=None,
            participants=None,
            program_modules=None,
            current_week=current_week_title,
            current_module=current_week_module,
            total_weeks=len(cohort.program_modules),
        )

        return cohort_info


def process_participant_meta(
    participant: Participant,
) -> tuple[Optional[str], Optional[str], Optional[datetime]]:
    participant_meta = participant.participant_meta
    if not participant_meta:
        logger.warning("Participant {} has no metadata", participant.id)
        return None, None, None
    else:
        phone = participant_meta[0].metadata.get("phone_number", None)
        disenrolled_reason = participant_meta[0].metadata.get("disenrolledReason", None)
        disenrollment_date = participant_meta[0].metadata.get("disenrollmentDate", None)
        if disenrollment_date:
            disenrollment_date = pendulum.parse(disenrollment_date)

        return phone, disenrolled_reason, disenrollment_date


def process_participant_activity(
    participant: Participant,
) -> tuple[Optional[datetime], Optional[datetime]]:
    if not participant.activities:
        return None, None
    else:
        activities_sorted = sorted(
            participant.activities, key=lambda x: x.created_at, reverse=True
        )
        last_activity_date = activities_sorted[0].created_at
        enrolled_activity_model = [
            activity
            for activity in activities_sorted
            if activity.activity_type == ParticipantActivityEnum.ENROLL
        ]
        if not enrolled_activity_model:
            logger.warning(
                "Participant {} has activities, but no enrolled activity",
                participant.id,
            )
            enrolled_activity_date = None
        else:
            enrolled_activity_date = enrolled_activity_model[0].created_at

    return last_activity_date, enrolled_activity_date


def process_filters(query: QuerySet, filters: FilterInput) -> QuerySet:
    if filters.search:
        filters.search = filters.search.strip()
        search_terms = filters.search.split()
        if len(search_terms) > 1:  # Support for full name search
            query = query.filter(
                (
                    Q(first_name__icontains=search_terms[0])
                    & Q(last_name__icontains=search_terms[1])
                )
                | (
                    Q(first_name__icontains=search_terms[1])
                    & Q(last_name__icontains=search_terms[0])
                )
            )

        else:
            query = query.filter(
                Q(first_name__icontains=filters.search)
                | Q(last_name__icontains=filters.search)
                | Q(email__icontains=filters.search)
                | Q(medical_record__icontains=filters.search)
            )

    if filters.cohort_id:
        query = query.filter(cohort__id=filters.cohort_id)
    if filters.created_by_id:
        query = query.filter(cohort__created_by_id=filters.created_by_id)
    if filters.program_id:
        query = query.filter(cohort__program_id=filters.program_id)
    if filters.date_enrolled_range:
        # We can also use Participant.created_at as an enroll date
        query = query.filter(
            activities__activity_type=ParticipantActivityEnum.ENROLL,
            activities__created_at__gte=filters.date_enrolled_range.start,
            activities__created_at__lte=filters.date_enrolled_range.end,
        )
    if filters.participant_status:
        query = query.filter(status=filters.participant_status)

    return query


class ParticipantRepository:
    @staticmethod
    async def get_participant_ids() -> List[UUID]:
        return await Participant.all().values_list("id", flat=True)

    @staticmethod
    async def get_participants(page: int, per_page: int) -> ParticipantsList:
        participants = (
            await Participant.all()
            .prefetch_related("activities")
            .offset((page - 1) * per_page)
            .limit(per_page)
        )
        participants_list = []
        if participants:
            for participant in participants:
                participant_procesed = await ParticipantPydantic.from_orm(participant)
                participants_list.append(participant_procesed)
        return ParticipantsList(participants=participants_list)

    @staticmethod
    async def get_paginated_participants(
        *,
        page: int,
        per_page: int,
        filters: Optional[FilterInput] = None,
        sort: Optional[SortInput] = None,
    ):
        """
        Fetches a paginated list of participants with optional filtering and sorting.

        Args:
            page (int): The current page number to fetch.
            per_page (int): The number of participants per page.
            filters (Optional[FilterInput], optional): Filtering options based on participant fields, Defaults to None.
            sort (Optional[SortInput], optional): Sorting options for participant fields. Defaults to None.

        Returns:
            GetParticipantsOutput: An object containing the list of participants and total number of pages.

        Raises:
            ValueError: If invalid sorting or filtering options are provided.
            Exception: If there is an issue with querying or fetching the participants.

        The function fetches participants with pagination, applies filters (like search terms, cohort, etc.),
        and sorts based on the specified fields.

        The result contains the total number of pages and a list of participants.
        """
        query = Participant.all_participants.all()

        prefetch_list = [
            "cohort__program_modules__program_module",
            "cohort__program",
            "cohort__created_by",
            "activities",
            "participant_meta",
        ]
        query = query.prefetch_related(*prefetch_list)

        if filters:
            query = process_filters(query, filters)

        query = query.distinct()

        total_participants = await query.count()
        total_pages = math.ceil(total_participants / per_page)

        sort_by: str = "first_name"  # Default sort by first name
        if sort:
            # Map FieldEnum values to corresponding model relations
            sort_by = SORT_KEY_MAPPING[sort.field]

            if sort.order == OrderEnum.desc:
                sort_by = f"-{sort_by}"

        participants = (
            await query.offset((page - 1) * per_page).limit(per_page).order_by(sort_by)
        )

        participants_output: list[ParticipantInfo] = []

        for participant in participants:
            last_activity_date, enrolled_activity_date = process_participant_activity(
                participant
            )

            phone, disenrolled_reason, disenrollment_date = process_participant_meta(
                participant
            )

            cohort_info = process_participant_cohort(participant)

            participant_info = ParticipantInfo(
                id=participant.id,
                created_at=participant.created_at,
                updated_at=participant.updated_at,
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
                phone=phone,
                group_id=participant.group_id,
                member_id=participant.member_id,
                status=participant.status,
                cognito_sub=participant.cognito_sub,
                medical_record=participant.medical_record,
                is_test=participant.is_test,
                last_reset=participant.last_reset,
                chat_identity=participant.chat_identity,
                last_activity_date=last_activity_date,
                date_enrolled=enrolled_activity_date,
                cohort=cohort_info,
                dissenrolled_reason=disenrolled_reason,
                disenrollment_date=disenrollment_date if disenrollment_date else None,
            )

            participants_output.append(participant_info)

        return GetParticipantsOutput(
            participants=participants_output, total_pages=total_pages
        )

    @staticmethod
    async def create_participant(participant: ParticipantCreate) -> ParticipantPydantic:
        participant_obj = await Participant.create(**participant.model_dump())
        return await ParticipantPydantic.from_orm(participant_obj)

    @staticmethod
    async def get_participant(participant_id: UUID) -> Optional[ParticipantPydantic]:
        participant = await Participant.get_or_none(id=participant_id)
        if participant:
            return await ParticipantPydantic.from_orm(participant)
        return None

    @staticmethod
    async def update_participant(
        participant_id: UUID, participant: ParticipantUpdate
    ) -> Optional[ParticipantPydantic]:
        await Participant.filter(id=participant_id).update(**participant.model_dump())
        updated_participant = await Participant.get(id=participant_id)
        return await ParticipantPydantic.from_orm(updated_participant)

    @staticmethod
    async def delete_participant(participant_id: UUID) -> None:
        await Participant.filter(id=participant_id).update(
            status=ParticipantStatus.DELETED
        )

    @staticmethod
    async def delete_participant_full_flow(email: str, info: Info) -> bool | None:
        return await ParticipantService().delete(email=email, info=info)

    @staticmethod
    async def sign_up(
        participant: ParticipantService, info: Info
    ) -> Participant | None:
        participant_obj = await participant.sign_up(info)
        return participant_obj  # TODO: Use this sign_up in create_participant

    @staticmethod
    async def resend_confirmation_link(
        info: Info,
        email: str = None,
        token: str = None,
    ) -> bool:
        if token:
            data = ParticipantService().decode_activation_token(token)
            if not data:
                return False
            email = data["email"].lower()

        return await ParticipantService().resend_activation_link(email=email, info=info)

    @staticmethod
    async def forgot_password(email: str) -> bool:
        return await ParticipantService().forgot_password(email=email)

    @staticmethod
    async def set_new_password(
        email: str, new_password: str, info: Info, correlation_id: str = "-"
    ) -> Participant | None:
        participant_obj = await ParticipantService().set_new_password(
            info=info,
            email=email,
            new_password=new_password,
            correlation_id=correlation_id,
        )
        return participant_obj

    @staticmethod
    async def verify_change_password_token(token: str, info: Info) -> dict:
        return await ParticipantService().verify_change_password_token(
            token=token, info=info
        )

    @staticmethod
    async def update_status(
        participant_id: UUID, status: str, info: Info
    ) -> Participant | None:
        participant_obj = await ParticipantService(
            participant_id=participant_id, status=status
        ).update_status(info)
        return participant_obj

    @staticmethod
    async def update_email(participant_id: UUID, email: str) -> bool:
        result = await ParticipantService(
            participant_id=participant_id, email=email
        ).update_email()
        return result

    @staticmethod
    async def get_participant_program(participant_id: UUID) -> Optional[Program]:
        participant = await Participant.filter(id=participant_id).get_or_none()

        if not participant:
            return None

        cohorts = await participant.cohort

        if not cohorts:
            return None

        return await cohorts[0].program


class SoleraParticipantRepository:
    @staticmethod
    async def get_solera_participants(
        page: int, per_page: int
    ) -> SoleraParticipantsList:
        solera_participants = (
            await SoleraParticipant.all().offset((page - 1) * per_page).limit(per_page)
        )

        processed_solera_participants = []
        if solera_participants:
            for sp in solera_participants:
                sp_processed = await SoleraParticipantPydantic.from_orm(sp)
                processed_solera_participants.append(sp_processed)
        return SoleraParticipantsList(participants=processed_solera_participants)

    @staticmethod
    async def create_solera_participant(
        sp: SoleraParticipantCreate,
    ) -> SoleraParticipantPydantic:
        sp_obj = await SoleraParticipant.create(**sp.model_dump())
        return await SoleraParticipantPydantic.from_orm(sp_obj)

    @staticmethod
    async def get_solera_participant(
        participant_id: UUID,
    ) -> Optional[SoleraParticipantPydantic]:
        sp = await SoleraParticipant.get_or_none(participant_id=participant_id)
        if sp:
            return await SoleraParticipantPydantic.from_orm(sp)
        return None

    @staticmethod
    async def update_solera_participant(
        participant_id: UUID, sp: SoleraParticipantUpdate
    ) -> Optional[SoleraParticipantPydantic]:
        await SoleraParticipant.filter(participant_id=participant_id).update(
            **sp.model_dump()
        )
        updated_sp = await SoleraParticipant.get(participant_id=participant_id)
        return await SoleraParticipantPydantic.from_orm(updated_sp)

    @staticmethod
    async def delete_solera_participant(participant_id: UUID) -> None:
        await SoleraParticipant.filter(participant_id=participant_id).delete()


class HeadsUpParticipantRepository:
    @staticmethod
    async def get_heads_up_participants(
        page: int, per_page: int
    ) -> HeadsUpParticipantsList:
        heads_up_participants = (
            await HeadsUpParticipant.all().offset((page - 1) * per_page).limit(per_page)
        )
        processed_hup_list = []
        if heads_up_participants:
            for hup in heads_up_participants:
                processed_hup = await HeadsUpParticipantPydantic.from_orm(hup)
                processed_hup_list.append(processed_hup)
        return HeadsUpParticipantsList(heads_up_participants=processed_hup_list)

    @staticmethod
    async def create_heads_up_participant(
        hup: HeadsUpParticipantCreate,
    ) -> HeadsUpParticipantPydantic:
        hup_obj = await HeadsUpParticipant.create(**hup.model_dump())
        return await HeadsUpParticipantPydantic.from_orm(hup_obj)

    @staticmethod
    async def get_heads_up_participant(
        participant_id: UUID,
    ) -> Optional[HeadsUpParticipantPydantic]:
        hup = await HeadsUpParticipant.get_or_none(participant_id=participant_id)
        if hup:
            return await HeadsUpParticipantPydantic.from_orm(hup)
        return None

    @staticmethod
    async def update_heads_up_participant(
        participant_id: UUID, hup: HeadsUpParticipantUpdate
    ) -> Optional[HeadsUpParticipantPydantic]:
        await HeadsUpParticipant.filter(participant_id=participant_id).update(
            **hup.model_dump()
        )
        updated_hup = await HeadsUpParticipant.get(participant_id=participant_id)
        return await HeadsUpParticipantPydantic.from_orm(updated_hup)

    @staticmethod
    async def delete_heads_up_participant(participant_id: UUID) -> None:
        await HeadsUpParticipant.filter(participant_id=participant_id).delete()


class ParticipantMetaRepository:
    @staticmethod
    async def get_participant_metas(page: int, per_page: int) -> MetaParticipantsList:
        participant_metas = (
            await ParticipantMeta.all().offset((page - 1) * per_page).limit(per_page)
        )
        processed_pm_list = []
        if participant_metas:
            for pm in participant_metas:
                processed_pm = await ParticipantMetaPydantic.from_orm(pm)
                processed_pm_list.append(processed_pm)
        return MetaParticipantsList(participant_metas=processed_pm_list)

    @staticmethod
    async def create_participant_meta(
        pm: ParticipantMetaCreate,
    ) -> ParticipantMetaPydantic:
        pm_obj = await ParticipantMeta.create(**pm.model_dump())
        return await ParticipantMetaPydantic.from_orm(pm_obj)

    @staticmethod
    async def get_participant_meta(
        participant_id: UUID,
    ) -> Optional[ParticipantMetaPydantic]:
        pm = await ParticipantMeta.get_or_none(participant_id=participant_id)
        if pm:
            return await ParticipantMetaPydantic.from_orm(pm)
        return None

    @staticmethod
    async def update_participant_meta(
        pm: ParticipantMetaUpdate,
    ) -> Optional[ParticipantMetaPydantic]:
        await ParticipantMeta.filter(id=pm.id).update(metadata=pm.metadata)
        updated_pm = await ParticipantMeta.get(id=pm.id)
        return await ParticipantMetaPydantic.from_orm(updated_pm)

    @staticmethod
    async def delete_participant_meta(participant_id: UUID) -> None:
        await ParticipantMeta.filter(participant_id=participant_id).delete()


class AuthorizedRepository:
    @staticmethod
    async def list_authorized() -> List[RawAuthorized]:
        all_authorized = await Authorized.all()

        return [
            RawAuthorized.model_validate(authorized) for authorized in all_authorized
        ]

    @staticmethod
    async def list_active_authorized() -> List[RawAuthorized]:
        active_authorized = await Authorized.filter(
            status=ParticipantStatus.ACTIVE,
            is_test=False,
        ).all()

        return [
            RawAuthorized.model_validate(authorized) for authorized in active_authorized
        ]

    @staticmethod
    async def get_alternative_hosts() -> List[RawAuthorized]:
        alternative_hosts = await Authorized.filter(alternative_host=True)

        return [
            RawAuthorized.model_validate(authorized) for authorized in alternative_hosts
        ]

    @staticmethod
    async def get_supports() -> List[RawAuthorized]:
        supports = await Authorized.filter(support_in_chat=True)

        return [RawAuthorized.model_validate(support) for support in supports]

    @staticmethod
    async def get_class_admins() -> List[RawAuthorized]:
        class_admins = await Authorized.filter(classes_admin=True)

        return [RawAuthorized.model_validate(admin) for admin in class_admins]

    @staticmethod
    async def get_authorized(authorized_id: UUID) -> Optional[RawAuthorized]:
        authorized = await Authorized.get_or_none(id=authorized_id)

        if not authorized:
            return None

        return RawAuthorized.model_validate(authorized)

    @staticmethod
    async def get_by_schedule_id(schedule_id: int) -> Optional[RawAuthorized]:
        authorized = await Authorized.get_or_none(supersaas_schedule_id=schedule_id)

        if not authorized:
            return None

        return RawAuthorized.model_validate(authorized)

    @staticmethod
    async def create_authorized(
        authorized: AuthorizedCreate, password: str
    ) -> RawAuthorized:
        """Create an authorized user in Cognito and save the user in the database."""
        exist = await Authorized.filter(email=authorized.email).exists()
        if exist:
            raise ValueError(
                f"Authorized with email: {authorized.email}, already exists"
            )

        response = admin_create_provider(
            email=authorized.email,
            is_participant_admin=True,
        )
        logger.info(f"Create provider response: {response}")

        cognito_sub = response["User"]["Username"]

        resp = admin_set_provider_password(authorized.email, password, True)
        logger.info(f"Set provider password response: {resp}")

        authorized_user = Authorized(
            id=cognito_sub,
            email=authorized.email,
            first_name=authorized.first_name,
            last_name=authorized.last_name,
            cognito_sub=cognito_sub,
            status=ParticipantStatus.ACTIVE,
            role=authorized.role,
        )
        await authorized_user.save()
        return RawAuthorized.model_validate(authorized_user)

    @staticmethod
    async def update_authorized(
        authorized_id: UUID, authorized: AuthorizedUpdate
    ) -> AuthorizedPydantic:
        await Authorized.filter(id=authorized_id).update(
            **authorized.model_dump(exclude_none=True, exclude={"id"})
        )
        updated_authorized = await Authorized.get(id=authorized_id)
        return await AuthorizedPydantic.from_orm(updated_authorized)

    @staticmethod
    async def delete_authorized(authorized_id: UUID) -> None:
        user = await Authorized.filter(id=authorized_id).get_or_none()

        if user:
            cohorts = await user.cohorts
            if len(cohorts) > 0:
                raise Exception("Cannot delete authorized user with associated cohorts")
            await user.delete()
