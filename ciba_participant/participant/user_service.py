import base64
import datetime
import json
import logging
from enum import Enum
from typing import Any, Callable, Optional
from uuid import UUID, uuid4

from fastapi import HTT<PERSON>Exception
from tortoise.transactions import atomic
from tortoise.queryset import Q

from ciba_participant.common.converters import from_utc_to_pst
from ciba_participant.common.cognito import (
    admin_add_user_to_group,
    admin_confirm_sign_up,
    admin_create_user,
    admin_set_user_password,
    admin_enable_user,
)
from ciba_participant.log.logging import logger
from ciba_participant.participant.models import (
    Participant,
    ParticipantStatus,
    SoleraParticipant,
    ParticipantMeta,
)
from ciba_participant.participant.data_preparer import DataPreparer
from ciba_participant.settings import ENV, get_settings
from ciba_participant.utils import AESCipher, generate_random_password
import pendulum
from ciba_participant.cohort.models import Cohort

settings = get_settings()
RESET_PASSWORD_SALT = "resetPassword"


async def check_re_enrolled_participant(
    email: Optional[str] = None,
) -> bool:
    """
    Function to check if participant is re-enrolled

    Args:
        email: participant email

    Returns:
        bool: True if participant is re-enrolled, False otherwise
    """
    return await Participant.filter(
        Q(email__startswith="deleted") & Q(email__icontains=email)
    ).exists()


class Devices(Enum):
    """Enum for device"""

    SCALE = "is_weight"
    WATCH = "is_activity"


class UserService:
    def __init__(
        self,
        participant: Optional[Participant] = None,
    ) -> None:
        self.participant = participant

    async def create_user(self, kwargs: dict) -> bool:
        response = await self.invoke_cognito(
            admin_create_user,
            kwargs["email"],
        )
        logger.info(f"User: {response}")
        cognito_username = response["User"]["Username"]
        if not cognito_username:
            return False

        await self.invoke_cognito(
            admin_add_user_to_group,
            kwargs["email"],
        )

        kwargs["id"] = cognito_username

        participant = Participant(
            id=cognito_username,
            email=kwargs.get("email"),
            first_name=kwargs.get("first_name"),
            last_name=kwargs.get("last_name"),
            group_id=kwargs.get("group_id"),
            member_id=kwargs.get("member_id"),
            medical_record=kwargs.get("medical_record"),
            is_test=kwargs.get("is_test"),
            cognito_sub=cognito_username,
        )
        solera_participant = SoleraParticipant(
            participant_id=participant.id,
            solera_id=kwargs.get("solera_id"),
            solera_program_id=kwargs.get("solera_program_id"),
            solera_enrollment_id=kwargs.get("solera_enrollment_id"),
            solera_key=kwargs.get("solera_key"),
        )
        participant_meta = ParticipantMeta(
            participant_id=participant.id,
            metadata=kwargs.get("metadata"),
        )
        await participant.save()
        await solera_participant.save()
        await participant_meta.save()
        self.participant = participant
        return True

    @atomic()
    async def re_enroll_user(self, kwargs: dict) -> bool:
        """Re-enroll user."""

        participant = await Participant.all_participants.filter(
            email=kwargs["email"]
        ).get_or_none()
        if not participant:
            raise HTTPException(status_code=404, detail="Participant not found")
        participant_meta = await ParticipantMeta.filter(
            participant_id=participant.id
        ).get_or_none()
        participant.status = ParticipantStatus.PENDING
        await participant.save()

        solera_participant = SoleraParticipant(
            participant_id=participant.id,
            solera_id=kwargs.get("solera_id"),
            solera_program_id=kwargs.get("solera_program_id"),
            solera_enrollment_id=kwargs.get("solera_enrollment_id"),
            solera_key=kwargs.get("solera_key"),
        )
        if participant_meta:
            participant_meta.metadata = kwargs.get("metadata")
        else:
            # Create new participant_meta if it doesn't exist
            participant_meta = ParticipantMeta(
                participant_id=participant.id,
                metadata=kwargs.get("metadata"),
            )
        await solera_participant.save()
        await participant_meta.save()

        try:
            await self.invoke_cognito(
                admin_enable_user,
                kwargs["email"],
            )
        except Exception as e:
            logger.error(f"Error enabling user {kwargs['email']} in Cognito: {e}")
            raise HTTPException(status_code=500, detail="Enrollment error")

        return True

    @atomic()
    async def update_status(self, status: ParticipantStatus) -> None:
        """Participant change status logic.

        On confirm create user in Cognito and send confirm email.
        On reject send reject email.
        """

        if status.value not in [
            ParticipantStatus.ACTIVE.value,
            ParticipantStatus.REJECTED.value,
            ParticipantStatus.PENDING.value,
        ]:
            raise HTTPException(status_code=400, detail="Invalid status")
        if self.participant is not None:
            status_changed = status != self.participant.status

            # handle status changes
            if status_changed and status == ParticipantStatus.ACTIVE.value:
                await self._on_active()
            if status_changed and status == ParticipantStatus.REJECTED.value:
                await self._on_reject()
            if status_changed and status == ParticipantStatus.PENDING.value:
                await self._on_pending()

            self.participant.status = status  # type: ignore
            await self.participant.save()

    @staticmethod
    async def format_greeting_email(participant_id: str) -> dict:
        """Send greeting email."""
        if not participant_id:
            logging.error("Participant id is required")
            return

        participant = await Participant.filter(id=participant_id).get()

        if participant.status != ParticipantStatus.ACTIVE:
            logging.error(f"Participant {participant_id} is not active")
            return

        solera_participant = await SoleraParticipant.filter(
            participant_id=participant_id
        ).get_or_none()
        if not solera_participant:
            logging.error(f"Participant {participant_id} don't have solera program")

        data = await DataPreparer(participant_id=participant_id).prepare_data()
        if not data:
            logging.error(f"Participant {participant_id} dont have participants")
            return
        if "started_call" not in data:
            logging.error(f"Cohort {str(data['id'])}  dont have started call")
        if not data["started_call"]:
            logging.error(f"Cohort {str(data['id'])}  dont have started call")

        logger.debug(
            f"ParticipantID:{participant_id} | started_call: {data['started_call']}"
        )

        started_call = from_utc_to_pst(data["started_call"])
        started_program = data["started_program"]

        data["program_date"] = started_program.date().strftime("%m/%d/%Y")
        data["call_date"] = started_call.date().strftime("%m/%d/%Y")
        data["call_time"] = started_call.time().strftime("%I:%M %p")

        logger.debug(
            f"US program_date: {data['program_date']} | US call_date: {data['call_date']} | US call_time: {data['call_time']}"
        )

        return data

    @staticmethod
    async def format_disenroll_email(participant_id: str) -> dict:
        """
        Formats the disenrollment email data for a given participant.

        Returns a dictionary containing email, participant details,
        solera program details, and disenrollment information.
        """
        if not participant_id:
            logging.error("Participant id is required")
            return {}

        # Retrieve participant; if not found, log and return empty dict
        participant = await Participant.filter(id=participant_id).get_or_none()
        if not participant:
            logging.error(f"Participant {participant_id} does not exist")
            return {}

        # Initialize email data with defaults and participant details.
        data = {
            "email": participant.email,
            "first_name": participant.first_name,
            "program_name": None,
            "program_date": None,
            "disenrolledReason": "No provided reason",
            "disenrollmentDate": pendulum.now("UTC").to_date_string(),
        }

        # Retrieve the solera participant record.
        solera_participant = await SoleraParticipant.filter(
            participant_id=participant_id
        ).get_or_none()
        if solera_participant:
            data["program_name"] = solera_participant.solera_program_id
        else:
            logging.error(
                f"Participant {participant_id} does not have a solera program"
            )

        # Retrieve participant metadata.
        participant_meta = await ParticipantMeta.filter(
            participant_id=participant_id
        ).get_or_none()
        if participant_meta:
            # Use defaults if keys are missing from metadata.
            data["disenrolledReason"] = participant_meta.metadata.get(
                "disenrolledReason", data["disenrolledReason"]
            )
            data["disenrollmentDate"] = participant_meta.metadata.get(
                "disenrollmentDate", data["disenrollmentDate"]
            )
        else:
            logging.error(f"Participant {participant_id} does not have metadata")
        cohort = await Cohort.filter(participants__id=participant.id).first()
        data["program_date"] = cohort.started_at

        return data

    async def _on_active(self) -> None:
        if self.participant is not None:
            if not self.participant.cognito_sub:
                tmp_password = generate_random_password()
                await self.invoke_cognito(
                    admin_confirm_sign_up,
                    self.participant.email,
                )
                await self.invoke_cognito(
                    admin_set_user_password,
                    self.participant.email,
                    tmp_password,
                )
                self.participant.cognito_sub = self.participant.id

                # if self.background_tasks is not None:
                #     self.background_tasks.add_task(           # TODO: Look how to resolve this background task
                #         ConfirmParticipantByProgramTask().apply_async,
                #         args=[
                #             self.participant.email,
                #             self.participant.first_name,
                #             self.get_totp(self.participant, tmp_password),
                #             self.participant.solera_program_id,
                #         ],
                #     )

    async def _on_reject(self) -> None:
        pass

    async def _on_pending(self) -> None:
        pass

    async def invoke_cognito(self, command: Callable, *args: Any, **kwargs: Any) -> Any:
        """Invoke cognito command."""
        if settings.ENV in [ENV.LOCAL, ENV.TEST]:
            return {"User": {"Username": uuid4()}, "UserStatus": "CONFIRMED"}
        return command(*args, **kwargs)

    async def _get_participant_by_id(self, id: UUID) -> None:
        self.participant = await Participant.filter(id=id).get_or_none()

    async def update_device_issetup(
        self, participant_id: UUID, device: Devices
    ) -> None:
        is_setup = True
        """Update device is_setup."""
        if self.participant is None:
            await self._get_participant_by_id(participant_id)
        if self.participant and getattr(self.participant, device.value) != is_setup:
            setattr(self.participant, device.value, True)
            # self.participant.is_setup = is_setup
            await self.participant.save()

    @staticmethod
    def get_totp(participant: Participant, tmp_password: str) -> str:
        """Generate one time token."""
        token = (
            AESCipher(settings.SECRET_KEY + RESET_PASSWORD_SALT)
            .encrypt(
                json.dumps(
                    {
                        "email": participant.email,
                        "tmp_password": tmp_password,
                        "last_reset": participant.last_reset.isoformat()
                        if participant.last_reset
                        else datetime.datetime.now(datetime.timezone.utc).isoformat(),
                    }
                )
            )
            .decode()
        )
        return base64.urlsafe_b64encode(token.encode()).decode()
