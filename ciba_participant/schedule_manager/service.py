from datetime import datetime
from contextlib import asynccontextmanager
from typing import Optional
from json import JSONDecodeError

from httpx import AsyncClient, Response, ConnectError
from loguru import logger

from ciba_participant.schedule_manager.types import (
    ZoomRecurrenceType,
    Occurrence,
    BaseZoomMeeting,
    CreateZoomMeetingOutput,
    GetZoomMeetingOutput,
    GetRecordingOutput,
    ScheduleManagerError,
)
from ciba_participant import get_settings
from ciba_participant.error_messages.schedule_manager import (
    SCHEDULE_MANAGER_CLIENT_ERROR,
    SCHEDULE_MANAGER_IS_DOWN,
    NO_DATA,
    UNEXPECTED_ERROR_MESSAGE,
)


# sunday = 1, monday = 2, ..., saturday = 7
def get_zoom_week_day(date: datetime) -> int:
    return (date.toordinal() + 1) % 7 or 7


def process_response_data(response: Response):
    try:
        json_data = response.json()
    except JSONDecodeError:
        json_data = {}

    if json_data and "error" in json_data:
        logger.error(json_data["error"])
        raise ScheduleManagerError(json_data["error"])

    if response.is_error:
        raise ScheduleManagerError(UNEXPECTED_ERROR_MESSAGE)

    return json_data


@asynccontextmanager
async def get_client(
    api_endpoint: str,
):
    try:
        client = AsyncClient(base_url=api_endpoint, timeout=60)
    except Exception as err:
        raise ScheduleManagerError(SCHEDULE_MANAGER_CLIENT_ERROR) from err
    else:
        try:
            yield client
        finally:
            await client.aclose()


class ScheduleManager:
    def __init__(self, endpoint=None):
        settings = get_settings()
        self.api_endpoint = endpoint or settings.SCHEDULE_MANAGER_API_ENDPOINT

    async def create_zoom_meetings(
        self,
        *,
        start_time: datetime,
        timezone: str = "UTC",
        duration: int = 60,
        repeat_interval: int = 4,
        repeat_type: ZoomRecurrenceType,
        repeat_count: int,
        user_id: str,
        alternative_hosts: list[str],
        title: str = "",
        description: str = "",
    ) -> CreateZoomMeetingOutput:
        async with get_client(self.api_endpoint) as client:
            payload = {
                "time": {
                    "start_time": start_time.isoformat(),
                    "timezone": timezone,
                    "duration": duration,
                },
                "recurrence": {
                    "type": repeat_type,
                    "repeat_interval": repeat_interval,
                    "weekly_days": str(get_zoom_week_day(start_time)),
                    "end_times": repeat_count,
                },
                "user_id": user_id,
                "alternative_hosts": ";".join(alternative_hosts),
                "topic": title,
                "agenda": description,
            }

            logger.info(f"Creating Zoom meeting with payload: {payload}")

            try:
                response = await client.post("/meetings", json=payload)

                json_data = process_response_data(response)

                logger.info(f"Zoom meeting created: {json_data}")

                output = CreateZoomMeetingOutput(
                    meeting_id=str(json_data["id"]),
                    start_url=json_data["start_url"],
                    join_url=json_data["join_url"],
                    host_email=json_data["host_email"],
                    occurrences=[
                        Occurrence(
                            id=str(occurrence["occurrence_id"]),
                            start_time=occurrence["start_time"],
                            duration=occurrence["duration"],
                        )
                        for occurrence in json_data["occurrences"]
                    ],
                )

                return output
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def update_title_and_description(
        self,
        meeting_id: str,
        *,
        title: Optional[str] = None,
        description: Optional[str] = None,
    ) -> None:
        updates = {}

        if title:
            updates["topic"] = title

        if description:
            updates["agenda"] = description

        async with get_client(self.api_endpoint) as client:
            try:
                response = await client.patch(f"/meetings/{meeting_id}", json=updates)
                process_response_data(response)
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def update_zoom_meeting_occurrence(
        self,
        meeting_id: str,
        occurrence_id: str,
        *,
        start_time: Optional[datetime] = None,
        timezone: Optional[str] = None,
        topic: Optional[str] = None,
        agenda: Optional[str] = None,
    ) -> BaseZoomMeeting:
        updates = {}

        if start_time:
            updates["start_time"] = start_time.isoformat()

        if timezone:
            updates["timezone"] = timezone

        if topic:
            updates["topic"] = topic

        if agenda:
            updates["agenda"] = agenda

        async with get_client(self.api_endpoint) as client:
            logger.info(
                f"Updating zoom occurrence {occurrence_id} for meeting {meeting_id}"
            )

            try:
                response = await client.patch(
                    f"/meetings/{meeting_id}/occurrences/{occurrence_id}", json=updates
                )

                json_data = process_response_data(response)

                if json_data is None:
                    raise ScheduleManagerError(NO_DATA)

                return BaseZoomMeeting(
                    meeting_id=str(json_data["meeting_id"]),
                    start_url=json_data["start_url"],
                    join_url=json_data["join_url"],
                    host_email=json_data["host_email"],
                )
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def get_zoom_meeting(
        self,
        meeting_id: str,
    ) -> GetZoomMeetingOutput:
        async with get_client(self.api_endpoint) as client:
            logger.info(f"Fetching zoom meeting {meeting_id}")

            try:
                response = await client.get(f"/meetings/{meeting_id}")
                json_data = process_response_data(response)

                output = GetZoomMeetingOutput(
                    meeting_id=str(json_data["id"]),
                    start_url=json_data["start_url"],
                    join_url=json_data["join_url"],
                    host_email=json_data["host_email"],
                    type=json_data["type"],
                    alternative_hosts=json_data["settings"]["alternative_hosts"],
                    topic=json_data["topic"],
                    time_zone=json_data["timezone"],
                )

                if "occurrences" in json_data:
                    output.occurrences = [
                        Occurrence(
                            id=str(occurrence["occurrence_id"]),
                            start_time=occurrence["start_time"],
                            duration=occurrence["duration"],
                        )
                        for occurrence in json_data["occurrences"]
                    ]

                return output
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def get_meeting_recordings(self, meeting_id: str) -> GetRecordingOutput:
        async with get_client(self.api_endpoint) as client:
            try:
                response = await client.get(f"/meetings/{meeting_id}/recordings")
                json_data = process_response_data(response)

                logger.info(f"Zoom meeting recordings fetched: {json_data}")

                url = next(
                    (
                        link["play_url"]
                        for link in json_data["recording_files"]
                        if link["recording_type"] == "shared_screen"
                    ),
                    "",
                )

                if not url:
                    raise ValueError(
                        "Shared Screen record not found. Please add video instead."
                    )

                output = GetRecordingOutput(recording_url=url)

                return output
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def change_meeting_host(
        self,
        meeting_id: str,
        new_host_email: str,
    ) -> None:
        logger.info(f"Changing host for Zoom meeting: {meeting_id}")

        payload = {"meeting_id": meeting_id, "new_host_email": new_host_email}

        async with get_client(self.api_endpoint) as client:
            try:
                response = await client.post("/hosts/update", json=payload)

                process_response_data(response)
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def delete_zoom_meeting(
        self,
        meeting_id: str,
    ) -> None:
        logger.info(f"Deleting Zoom meeting: {meeting_id}")
        async with get_client(self.api_endpoint) as client:
            try:
                response = await client.delete(f"/meetings/{meeting_id}")

                process_response_data(response)
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce

    async def delete_zoom_meeting_occurrence(
        self,
        meeting_id: str,
        occurrence_id: str,
    ) -> None:
        logger.info(f"Deleting Zoom meeting occurrence: {meeting_id}/{occurrence_id}")
        async with get_client(self.api_endpoint) as client:
            try:
                response = await client.delete(
                    f"/meetings/{meeting_id}/occurrences/{occurrence_id}"
                )

                process_response_data(response)
            except ConnectError as ce:
                raise ScheduleManagerError(SCHEDULE_MANAGER_IS_DOWN) from ce
