from typing import Optional
from uuid import UUID

from tortoise.transactions import in_transaction
from ciba_participant.activity.models import (
    ActivityUnit,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ParticipantActivity,
)
from ciba_participant.activity.utils import determine_activity_date
from ciba_participant.content_library.exceptions import ContentMaterialForbiddenError
from ciba_participant.content_library.models import (
    ContentInteractions,
    ContentActivityType,
    ContentMaterial,
)
from ciba_participant.log.logging import logger
from ciba_participant.participant.crud import AuthorizedRepository


async def record_content_progress(
    participant_id: UUID,
    content_material_id: UUID,
    download: bool = False,
) -> bool:
    """
    Records content progress for a participant by logging their interaction
    with a specific content material and creating an associated activity record.

    If the participant interacts with a content material, an activity entry is created
    for each associated activity type. Additionally, the interaction count for the
    content material is updated.

    Args:
        participant_id (UUID): The unique identifier of the participant.
        content_material_id (UUID): The unique identifier of the content material.
        download (bool, optional): Indicates if the content material was downloaded.

    Returns:
        bool: True if the content progress is successfully recorded.

    Raises:
        ValueError: If the specified content material is not found.
    """
    content_material_types = await ContentActivityType.filter(
        material_id=content_material_id
    ).values_list("activity_type", flat=True)

    if not content_material_types:
        logger.warning(f"Content material with ID {content_material_id} not found")
        raise ValueError("Content material not found")

    activity_date = await determine_activity_date(participant_id)

    async with in_transaction():
        activities = [
            ParticipantActivity(
                created_at=activity_date,
                participant_id=participant_id,
                value=1,
                unit=ActivityUnit.ACTION,
                activity_device=ParticipantActivityDevice.MANUAL_INPUT,
                activity_category=ParticipantActivityCategory.ACTIVITY,
                activity_type=activity_type,
                is_content_related=True,
            )
            for activity_type in content_material_types
        ]

        await ParticipantActivity.bulk_create(activities)

        interaction = await ContentInteractions.filter(
            participant_id=participant_id,
            material_id=content_material_id,
        ).first()

        if interaction:
            interaction.interactions += 1
            if download:
                interaction.is_downloaded = True

        else:
            interaction = ContentInteractions(
                participant_id=participant_id,
                material_id=content_material_id,
                interactions=1,
                is_completed=True,
                is_downloaded=download,
            )

        await interaction.save()

    return True


async def handle_favorite_status(
    participant_id: UUID, content_material_id: UUID
) -> bool:
    """
    Handles the favorite status of a content material for a participant.

    If interaction is not favorite, it will be marked as favorite and vice versa.

    Args:
        participant_id (UUID): The unique identifier of the participant.
        content_material_id (UUID): The unique identifier of the content material.

    Returns:
        bool: True if the favorite status is successfully handled.

    Raises:
        ValueError: If the specified content material is not found.
    """
    content_material = await ContentMaterial.get_or_none(id=content_material_id)

    if not content_material:
        logger.warning(f"Content material with ID {content_material_id} not found")
        raise ValueError("Content material not found")

    interaction = await ContentInteractions.filter(
        participant_id=participant_id,
        material_id=content_material_id,
    ).first()

    if interaction:
        interaction.is_favorite = not interaction.is_favorite
        await interaction.save()

    else:
        interaction = ContentInteractions(
            participant_id=participant_id,
            material_id=content_material_id,
            is_favorite=True,
        )

        await interaction.save()

    return True


async def check_permissions(user_id: UUID) -> bool:
    """
    Method to check if a user has permission to perform a content activity operation.
    :param user_id: ID of the user to be authorized.
    :return: True if the user has permission to perform a content activity operation.
    """
    allowed_users = await AuthorizedRepository.get_content_library_admins()

    if user_id not in [user.id for user in allowed_users]:
        raise ContentMaterialForbiddenError()

    return True


async def check_duplicated_material_titles(
    title: str,
    excluded_material_id: Optional[UUID] = None,
) -> bool:
    """
    Method to check if a title already exists in the content library.
    :param title: content material title.
    :param excluded_material_id: content material ID to exclude from the verification.
    :return: boolean flag indicating if the title already exists.
    """
    base_query = ContentMaterial.filter(title__iexact=title)

    if excluded_material_id:
        base_query = base_query.filter(id__not=excluded_material_id)

    return await base_query.exists()
