from uuid import UUID

from pydantic import BaseModel
from tortoise.transactions import in_transaction

from ciba_participant import get_settings
from ciba_participant.common.aws_handler import delete_s3_object
from ciba_participant.content_library.crud.create_content_material import (
    NewMaterialData,
)
from ciba_participant.content_library.exceptions import ContentMaterialNotFoundError
from ciba_participant.content_library.helpers import (
    adjust_mime_type,
    sanitize_file_name,
)
from ciba_participant.content_library.models import (
    ContentMaterial,
    ProgramContent,
    ContentTag,
    ContentActivityType,
)
from ciba_participant.content_library.service import (
    check_permissions,
    check_duplicated_material_titles,
)

settings = get_settings()


class UpdateResponse(BaseModel):
    """
    Class that represents the response from the content material update.
    """

    material_id: UUID
    needs_file_upload: bool


async def process(
    content_material_id: UUID, material_data: NewMaterialData, author_id: UUID
) -> UpdateResponse:
    """
    Method that handles updating a content material.
    :param content_material_id: Content material ID
    :param material_data: Content material new data
    :param author_id: Modification author ID
    :return: The updated content material ID
    """
    await check_permissions(author_id)

    requested_material = await ContentMaterial.filter(id=content_material_id).first()

    if not requested_material:
        raise ContentMaterialNotFoundError()

    if await check_duplicated_material_titles(material_data.title, content_material_id):
        raise ValueError(f"Duplicated material title: {material_data.title}")
    mime_type = (
        adjust_mime_type(material_data.content_url, material_data.form_id)
        if material_data.content_url or material_data.form_id
        else material_data.mime_type
    )
    needs_file_upload = get_file_upload_flag(requested_material, material_data)

    async with in_transaction():
        if requested_material.full_path and (
            material_data.form_id or material_data.content_url
        ):
            delete_s3_object(
                bucket_name=settings.CONTENT_LIBRARY_BUCKET_NAME,
                object_path=requested_material.full_path,
            )

        requested_material.updated_by = author_id
        requested_material.title = material_data.title
        requested_material.description = material_data.description
        requested_material.mime_type = mime_type
        requested_material.content_url = material_data.content_url
        requested_material.file_name = sanitize_file_name(material_data.file_name)
        requested_material.file_size = (
            material_data.file_size if material_data.file_size else 0
        )
        requested_material.file_location = material_data.file_location
        requested_material.form_id = material_data.form_id

        await update_programs(requested_material, material_data.programs)
        await update_tags(requested_material, material_data.tags)
        await update_activity_types(requested_material, material_data.activity_types)
        await requested_material.save()

    return UpdateResponse(
        material_id=requested_material.id,
        needs_file_upload=needs_file_upload,
    )


def get_update_difference(current: set, new: set) -> (set, set):
    """
    Method to get the new inserts and deletes between two sets
    :param current: Current values
    :param new: Incoming new values
    :return: A tuple with new inserts, deletes between compared sets
    """
    to_insert = new - current
    to_delete = current - new

    return to_insert, to_delete


async def update_programs(content_material: ContentMaterial, new_programs: list):
    """
    Method that handle the content material programs update.
    :param content_material: Content material to update
    :param new_programs: New programs list
    """
    current_programs = await content_material.programs
    programs_set = {element.program_id for element in current_programs}
    to_insert, to_delete = get_update_difference(programs_set, set(new_programs))

    if to_delete:
        await ProgramContent.filter(
            material_id=content_material.id, program_id__in=to_delete
        ).delete()

    if to_insert:
        programs = [
            ProgramContent(
                program_id=program_id,
                material=content_material,
            )
            for program_id in to_insert
        ]
        await ProgramContent.bulk_create(programs)


async def update_tags(content_material: ContentMaterial, new_tags: list):
    """
    Method that handle the content material tags update.
    :param content_material: Content material to update
    :param new_tags: New tags list
    """
    current_tags = await content_material.tags
    tags_set = {element.tag for element in current_tags}
    to_insert, to_delete = get_update_difference(tags_set, set(new_tags))

    if to_delete:
        await ContentTag.filter(
            material_id=content_material.id, tag__in=to_delete
        ).delete()

    if to_insert:
        tags = [ContentTag(tag=tag, material=content_material) for tag in to_insert]
        await ContentTag.bulk_create(tags)


async def update_activity_types(
    content_material: ContentMaterial, new_activity_types: list
):
    """
    Method that handle the content material activity types update.
    :param content_material: Content material to update
    :param new_activity_types: New activity types list
    """
    current_activity_types = await content_material.activity_types
    activity_types_set = {element.activity_type for element in current_activity_types}
    to_insert, to_delete = get_update_difference(
        activity_types_set, set(new_activity_types)
    )

    if to_delete:
        await ContentActivityType.filter(
            material_id=content_material.id, activity_type__in=to_delete
        ).delete()

    if to_insert:
        activity_types = [
            ContentActivityType(
                activity_type=activity_type,
                material=content_material,
            )
            for activity_type in to_insert
        ]
        await ContentActivityType.bulk_create(activity_types)


def get_file_upload_flag(
    content_material: ContentMaterial, new_data: NewMaterialData
) -> bool:
    """
    Method that determines whether a new file should be uploaded.
    :param content_material: Content material to update
    :param new_data: New content material data
    :return: True if a new file upload is needed else False
    """
    if new_data.content_url or new_data.form_id:
        return False

    if (
        content_material.file_size != new_data.file_size
        or content_material.file_location != new_data.file_location
        or sanitize_file_name(content_material.file_name)
        != sanitize_file_name(new_data.file_name)
    ):
        return True

    return False
