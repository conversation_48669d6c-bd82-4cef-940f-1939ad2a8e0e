from ciba_participant.content_library.crud import (
    create_content_material,
    get_content_material,
    delete_content_material,
    edit_content_material,
    update_archive_status,
)


class ContentMaterialRepository:
    add_new_material = staticmethod(create_content_material.process)
    delete_material = staticmethod(delete_content_material.process)
    edit_material = staticmethod(edit_content_material.process)
    get_material = staticmethod(get_content_material.process)
    update_archive_status = staticmethod(update_archive_status.process)
