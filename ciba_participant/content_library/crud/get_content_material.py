import math
from datetime import datetime
from typing import Optional, List
from uuid import UUID

from tortoise.queryset import QuerySet, Q
from pydantic import BaseModel

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.content_library.enums import ContentMaterialStatus, MaterialTag
from ciba_participant.content_library.models import ContentMaterial


class MaterialFilters(BaseModel):
    tags: Optional[List[MaterialTag]] = None
    activity_types: Optional[List[ParticipantActivityEnum]] = None
    programs: Optional[List[UUID]] = None
    program_id: Optional[UUID] = None
    status: Optional[ContentMaterialStatus] = None
    search: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class MaterialPaginatedList(BaseModel):
    total: int
    total_pages: int
    items: List


def validate_filters(filters: MaterialFilters = None):
    """
    Method to validate the provided filters.
    """
    if filters:
        if filters.program_id and filters.programs:
            raise ValueError("Combination of program_id and programs filters is not supported.")

        if filters.start_date and filters.end_date and filters.start_date > filters.end_date:
            raise ValueError("Start date cannot be greater than end date.")


def apply_filters(query: QuerySet, filters: MaterialFilters = None) -> QuerySet[ContentMaterial]:
    """
    Method to apply filters to a content material queryset.
    """
    if filters is None:
        return query

    filtered_query = query

    if filters.activity_types:
        filtered_query = filtered_query.filter(activity_types__activity_type__in=filters.activity_types)
    if filters.programs:
        filtered_query = filtered_query.filter(programs__program_id__in=filters.programs)
    if filters.tags:
        filtered_query = filtered_query.filter(tags__tag__in=filters.tags)
    if filters.program_id:
        filtered_query = filtered_query.filter(programs__program_id=filters.program_id)
    if filters.status:
        filtered_query = filtered_query.filter(status=filters.status)
    if filters.start_date:
        filtered_query = filtered_query.filter(created_at__gte=filters.start_date)
    if filters.end_date:
        filtered_query = filtered_query.filter(created_at__lte=filters.end_date)
    if filters.search:
        filtered_query = filtered_query.filter(
            Q(title__icontains=filters.search) | Q(description__icontains=filters.search)
        )

    return filtered_query


async def process(page:int = 1, per_page: int = 10, filters: MaterialFilters = None):
    """
    Method to query the requested content material.
    """
    validate_filters(filters)

    query = apply_filters(ContentMaterial.all(), filters)

    total = await query.count()
    total_pages = math.ceil(total / per_page)

    page_elements = await (
        query
        .prefetch_related("programs", "activity_types", "tags")
        .order_by("created_at")
        .offset((page - 1) * per_page)
        .limit(per_page)
    )

    return MaterialPaginatedList(
        total=total,
        total_pages=total_pages,
        items=page_elements,
    )
