import time
import functools
from typing import Any, Dict, <PERSON><PERSON>


def cache_for_one_hour(func):
    cache: Dict[<PERSON><PERSON>, <PERSON><PERSON>[float, Any]] = {}

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Create a cache key from args and kwargs
        key = (*args, frozenset(kwargs.items()))

        current_time = time.time()
        if key in cache:
            timestamp, result = cache[key]
            if current_time - timestamp < 3600:
                return result

        result = func(*args, **kwargs)
        cache[key] = (current_time, result)
        return result

    return wrapper
