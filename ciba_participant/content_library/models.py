from uuid import uuid4, UUID

from tortoise import Model
from tortoise.fields import (
    BooleanField,
    CharEnumField,
    CharField,
    IntEnumField,
    IntField,
    ForeignKeyField,
    ForeignKeyRelation,
    UUIDField,
    ReverseRelation,
)

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.common.models import TimestampMixin, AuditMixin
from ciba_participant.content_library.enums import ContentMaterialStatus, MaterialTag
from ciba_participant.content_library.helpers import get_file_url


CONTENT_MATERIAL_MODEL = "models.ContentMaterial"


class ContentMaterial(Model, TimestampMixin, AuditMixin):
    id = UUIDField(pk=True, default=uuid4)
    is_healthy = BooleanField(default=True, null=True)
    title = CharField(max_length=255)
    description = CharField(max_length=500)
    content_url = CharField(max_length=255, null=True)
    mime_type = CharField(max_length=255)
    form_id = Char<PERSON>ield(max_length=50, null=True)
    file_size = IntField(unsigned=True, default=0)
    file_location = Char<PERSON>ield(max_length=255, null=True)
    file_name = Char<PERSON>ield(max_length=255, null=True)
    status = IntEnumField(ContentMaterialStatus, default=ContentMaterialStatus.ACTIVE)
    tags = ReverseRelation["ContentTag"]
    activity_types = ReverseRelation["ContentActivityType"]
    programs = ReverseRelation["ContentProgram"]
    interactions = ReverseRelation["ContentInteractions"]

    @property
    def link(self):
        if self.content_url:
            return self.content_url
        if self.form_id:
            return f"https://form.typeform.com/to/{self.form_id}"
        return get_file_url(self.id, self.file_location, self.file_name)

    @property
    def full_path(self) -> str:
        if self.file_location is None or self.file_name is None:
            return ""
        return f"{self.file_location}/{self.id}-{self.file_name}"

    class Meta:
        table = "content_material"
        ordering = ["created_at"]


class ContentTag(Model):
    id = UUIDField(primary_key=True, default=uuid4)
    tag = CharEnumField(MaterialTag, max_length=100)

    material_id: UUID
    material: ForeignKeyRelation[ContentMaterial] = ForeignKeyField(
        CONTENT_MATERIAL_MODEL, related_name="tags"
    )

    class Meta:
        table = "content_tags"


class ContentActivityType(Model):
    id = UUIDField(primary_key=True, default=uuid4)
    activity_type = CharEnumField(ParticipantActivityEnum, max_length=100)

    material_id: UUID
    material: ForeignKeyRelation[ContentMaterial] = ForeignKeyField(
        CONTENT_MATERIAL_MODEL, related_name="activity_types"
    )

    class Meta:
        table = "content_activity_types"


class ProgramContent(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)

    program_id: UUID
    program = ForeignKeyField("models.Program")

    material_id: UUID
    material = ForeignKeyField(CONTENT_MATERIAL_MODEL, related_name="programs")

    class Meta:
        table = "program_content"
        unique_together = (("program_id", "material_id"),)


class ContentInteractions(Model, TimestampMixin):
    """
    Model to track content interactions.
    """

    id = UUIDField(primary_key=True, default=uuid4)

    participant_id: UUID
    participant = ForeignKeyField("models.Participant")

    material_id: UUID
    material = ForeignKeyField(CONTENT_MATERIAL_MODEL)

    interactions = IntField(default=0)
    is_completed = BooleanField(default=False)
    is_downloaded = BooleanField(default=False)
    is_favorite = BooleanField(default=False)

    class Meta:
        table = "content_interactions"
        unique_together = (("participant_id", "material_id"),)
