from tortoise import <PERSON><PERSON><PERSON>
from ..settings import get_settings, Settings

settings = get_settings()


def get_tortoise_orm_config(db_url: str) -> dict:
    """Init tortoise orm config."""
    return {
        "connections": {"default": db_url},
        "apps": {
            "models": {
                "models": [
                    "aerich.models",
                    "ciba_participant.program.models",
                    "ciba_participant.participant.models",
                    "ciba_participant.activity.models",
                    "ciba_participant.cohort.models",
                    "ciba_participant.classes.models",
                    "ciba_participant.content_library.models",
                ],
                "default_connection": "default",
            },
        },
    }


async def init_db():
    await Tortoise.init(get_tortoise_orm_config(settings.default_db_url))


async def custom_init_db(custom_settings: Settings):
    await Tortoise.init(get_tortoise_orm_config(custom_settings.default_db_url))


async def close_db():
    await Tortoise.close_connections()
