import base64
import hashlib
import hmac
import logging

import boto3
from botocore.client import Base<PERSON><PERSON>
from botocore.exceptions import ClientError

from ciba_participant.settings import Settings, get_settings

settings: Settings = get_settings()

logger = logging.getLogger(__name__)


def get_cognito_client() -> BaseClient:
    """Init and return aws cognito client."""
    return boto3.client(
        "cognito-idp",
        region_name=settings.AWS_REGION,
    )


def get_secret_hash(username: str) -> str:
    """Generate secret hash for a user."""
    msg = username + settings.COGNITO_SERVER_CLIENT_ID
    dig = hmac.new(
        str(settings.COGNITO_SERVER_CLIENT_SECRET).encode("utf-8"),
        msg=str(msg).encode("utf-8"),
        digestmod=hashlib.sha256,
    ).digest()
    secret_hash = base64.b64encode(dig).decode()
    return secret_hash


def admin_create_user(email: str) -> dict:
    """Create user on cognito."""
    response = {}
    try:
        response = get_cognito_client().admin_create_user(
            UserPoolId=settings.COGNITO_USER_POOL_ID,
            Username=email,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "email_verified", "Value": "true"},
            ],
            MessageAction="SUPPRESS",
        )
    except ClientError as error:
        # User already exists in the pool
        if error.response["Error"]["Code"] == "UsernameExistsException":
            # To satisfy the response from admin_create_user
            # wrap in User.
            response["User"] = admin_get_user(email)
        else:
            raise error

    return response


def admin_update_email(old_email:str, new_email: str) -> dict:
    """Update user email on cognito."""
    response = get_cognito_client().admin_update_user_attributes(
        UserPoolId=settings.COGNITO_USER_POOL_ID,
        Username=old_email,
        UserAttributes=[
            {"Name": "email", "Value": new_email},
            {"Name": "email_verified", "Value": "true"},
        ],
    )

    return response


def admin_add_user_to_group(email: str) -> bool:
    """Add user to cognito group."""
    try:
        get_cognito_client().admin_add_user_to_group(
            UserPoolId=settings.COGNITO_USER_POOL_ID,
            Username=email,
            GroupName=settings.COGNITO_USER_GROUP,
        )
    except ClientError as error:
        # Check if the error is because group does not exists
        if error.response["Error"]["Code"] == "ResourceNotFoundException":
            logger.warning("Failed to add to group, group does not exist")
            return False
        else:
            raise

    return True


def admin_set_user_password(
        email: str, tmp_password: str | None = None, permanent: bool | None = False
) -> dict:
    """Admin set user password on cognito."""
    logger.debug("admin_set_user_password")
    response = get_cognito_client().admin_set_user_password(
        UserPoolId=settings.COGNITO_USER_POOL_ID,
        Username=email,
        Password=tmp_password,
        Permanent=permanent,
    )
    return response


def admin_get_user(email: str) -> dict:
    """Admin get user from cognito."""
    logger.debug("admin_get_user")
    try:
        response = get_cognito_client().admin_get_user(
            UserPoolId=settings.COGNITO_USER_POOL_ID,
            Username=email,
        )
    except ClientError as error:
        # Check if the error is because user not exists
        if error.response["Error"]["Code"] == "UserNotFoundException":
            response = {}
    return response


def forgot_password(email: str) -> dict:
    """Invoke forgot password flow on cognito."""
    logger.debug("forgot_password")
    response = get_cognito_client().forgot_password(
        ClientId=settings.COGNITO_SERVER_CLIENT_ID,
        SecretHash=get_secret_hash(email),
        Username=email,
    )
    return response


def admin_confirm_sign_up(email: str) -> dict:
    """Create user on cognito."""
    logger.debug("admin_confirm_sign_up")
    response = get_cognito_client().admin_update_user_attributes(
        UserPoolId=settings.COGNITO_USER_POOL_ID,
        Username=email,
        UserAttributes=[
            {"Name": "email_verified", "Value": "true"},
        ],
    )
    return response


def admin_disable_user(email: str) -> dict:
    """Disable user on cognito."""
    logger.debug("admin_disable_user")
    response = get_cognito_client().admin_disable_user(
        UserPoolId=settings.COGNITO_USER_POOL_ID, Username=email
    )
    return response


def admin_delete_user(email: str) -> dict:
    """Delete user on cognito."""
    logger.debug("admin_delete_user")
    response = get_cognito_client().admin_delete_user(
        UserPoolId=settings.COGNITO_USER_POOL_ID, Username=email
    )
    return response


def admin_enable_user(email: str) -> dict:
    """Enable user on cognito."""
    logger.debug("admin_enable_user")
    response = get_cognito_client().admin_enable_user(
        UserPoolId=settings.COGNITO_USER_POOL_ID, Username=email
    )
    return response


def admin_get_provider(email: str) -> dict:
    """Admin get user from cognito."""
    logger.debug("admin_get_provider")
    try:
        response = get_cognito_client().admin_get_user(
            UserPoolId=settings.PROVIDERS_COGNITO_USER_POOL_ID,
            Username=email,
        )
    except ClientError as error:
        # Check if the error is because user not exists
        if error.response["Error"]["Code"] == "UserNotFoundException":
            response = {}
    return response


def admin_set_provider_password(
        email: str, tmp_password: str | None = None, permanent: bool | None = False
) -> dict:
    """Admin set provider password on cognito."""
    logger.debug("admin_set_provider_password")
    response = get_cognito_client().admin_set_user_password(
        UserPoolId=settings.PROVIDERS_COGNITO_USER_POOL_ID,
        Username=email,
        Password=tmp_password,
        Permanent=permanent,
    )
    return response



def admin_create_provider(
        email: str,
        is_participant_admin: bool = False,
        is_admin: bool = False,
) -> dict:
    """Create provider in cognito provider pool."""
    response = {}
    try:
        response = get_cognito_client().admin_create_user(
            UserPoolId=settings.PROVIDERS_COGNITO_USER_POOL_ID,
            Username=email,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "email_verified", "Value": "true"},
                {"Name": "custom:isParticipantAdmin", "Value": str(int(is_participant_admin))},
                {"Name": "custom:isAdmin", "Value": str(int(is_admin))},
            ],
            MessageAction="SUPPRESS",
        )
    except ClientError as error:
        # User already exists in the pool
        if error.response["Error"]["Code"] == "UsernameExistsException":
            # To satisfy the response from admin_create_user
            # wrap in User.
            response["User"] = admin_get_provider(email)
        else:
            raise error

    return response
