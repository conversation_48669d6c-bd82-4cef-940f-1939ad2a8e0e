from typing import Optional, List, Dict, Union

import boto3
from loguru import logger
from botocore.exceptions import (
    NoCredentialsError,
    PartialCredentialsError,
    BotoCoreError,
    ClientError,
)

from pydantic import BaseModel
from enum import Enum
from botocore.client import Config


class NotificationType(Enum):
    EMAIL = "email"
    PUSH = "push"
    SQS = "sqs"
    PROGRESS = "progress"


class Platform(Enum):
    PARTICIPANT = "participant"


class EmailNotificationEvent(Enum):
    WELCOME_PARTICIPANT = "welcome_participant"
    CONFIRM_BY_PROGRAM = "confirm_by_program"
    CONFIRM_BY_PARTICIPANT = "confirm_by_participant"
    RESET_PASSWORD = "reset_password"
    REJECT_PARTICIPANT = "reject_participant"
    ACTIVATE_PARTICIPANT = "activate_participant"
    NEW_PARTICIPANT = "new_participant"
    DISENROLL_PARTICIPANT = "disenroll_participant"
    CANCELLED_SESSION = "cancelled_session"
    COHORT_ENDED = "cohort_ended"


class SQSNotification(BaseModel):
    type: NotificationType
    email_event: Optional[EmailNotificationEvent] = None
    push_event: Optional[str] = None
    data: Optional[dict] = None
    correlation_id: Optional[str] = "-"
    platform: Optional[Platform] = Platform.PARTICIPANT


class SlackNotification(BaseModel):
    environment: str
    is_test: bool
    source: str
    title: Optional[str] = ""
    url: Optional[str] = ""
    type: Optional[str] = ""
    details: Optional[str] = ""
    additional_info: Optional[str] = ""


# Custom exception class for S3 delete operations
class S3DeleteError(Exception):
    """Exception raised when S3 object deletion fails."""

    pass


def get_parameter(
    parameter_name: str, with_decryption: bool = True, region_name="us-east-2"
) -> str | None:
    """
    Fetches a parameter from AWS Systems Manager Parameter Store.

    :param parameter_name: The name of the parameter
    :param with_decryption: Whether to decrypt the parameter value (if it is encrypted)
    :return: The parameter value as a string
    """
    ssm_client = boto3.client(
        "ssm",
        region_name=region_name,
    )
    try:
        response = ssm_client.get_parameter(
            Name=parameter_name, WithDecryption=with_decryption
        )
    except ssm_client.exceptions.ParameterNotFound:
        logger.error(f"Parameter '{parameter_name}' not found in AWS SSM.")
        return None
    return response["Parameter"]["Value"]


def get_parameters_batch(
    parameter_names: List[str],
    with_decryption: bool = True,
    region_name: str = "us-east-2",
) -> Dict[str, str]:
    """
    Fetches multiple parameters in batches from AWS SSM Parameter Store.

    :param parameter_names: List of full parameter names to fetch
    :param with_decryption: Whether to decrypt the parameter values
    :param region_name: AWS region name
    :return: A dictionary of parameter names (without path) and their values
    """
    ssm_client = boto3.client("ssm", region_name=region_name)
    parameters = {}

    # SSM allows a maximum of 10 parameters per batch
    batch_size = 10

    for i in range(0, len(parameter_names), batch_size):
        batch = parameter_names[i : i + batch_size]

        try:
            response = ssm_client.get_parameters(
                Names=batch, WithDecryption=with_decryption
            )

            # Process successful parameter retrievals
            for param in response["Parameters"]:
                # Get the parameter name without the path
                param_name = param["Name"].split("/")[-1]
                parameters[param_name] = param["Value"]

            # Log any parameters that weren't found
            if response["InvalidParameters"]:
                logger.warning(f"Invalid parameters: {response['InvalidParameters']}")

        except (
            NoCredentialsError,
            PartialCredentialsError,
            BotoCoreError,
            ClientError,
        ) as e:
            logger.error(f"Error fetching parameters batch {batch}: {e}")

        except Exception as e:
            logger.error(f"Unexpected error fetching parameters batch {batch}: {e}")

    return parameters


def build_parameter_paths(
    parameter_names: List[str],
    prefixes: Union[str, List[str]],
    fallback_prefix: Optional[str] = None,
) -> List[str]:
    """
    Builds full parameter paths by combining parameter names with prefixes.

    :param parameter_names: List of parameter names to fetch
    :param prefixes: List of prefixes or single prefix to try for each parameter
    :param fallback_prefix: Optional fallback prefix if provided prefixes don't work
    :return: List of full parameter paths to fetch
    """
    if not parameter_names:
        return []

    if isinstance(prefixes, str):
        prefixes = [prefixes]

    if not prefixes:
        return []

    # Create all possible parameter paths to check
    parameter_paths = []

    for param_name in parameter_names:
        for prefix in prefixes:
            clean_prefix = prefix.rstrip("/")
            parameter_paths.append(f"{clean_prefix}/{param_name}")

        # Add fallback path if provided
        if fallback_prefix:
            clean_fallback = fallback_prefix.rstrip("/")
            parameter_paths.append(f"{clean_fallback}/{param_name}")

    return parameter_paths


def load_specific_parameters(
    parameter_names: List[str],
    env: str,
    service_prefix: str = "participant",
    is_new_env: bool = False,
    global_params_prefix: Optional[str] = None,
    region_name: str = "us-east-2",
) -> Dict[str, str]:
    """
    Load specific parameters, supporting mixed environments.

    :param parameter_names: List of parameter names to fetch (without prefix)
    :param env: Environment name (e.g., 'dev', 'prod')
    :param service_prefix: Service-specific prefix (e.g., 'participant')
    :param is_new_env: Flag to determine if environment prefix should be used
    :param global_params_prefix: Optional global prefix for parameters shared across environments
    :param region_name: AWS region name
    :return: Dictionary of parameters
    """
    # Determine the environment-specific prefix
    env_prefix = "" if is_new_env else f"/{env}"
    service_path = f"{env_prefix}/{service_prefix}"

    # Create a list of prefixes to check for each parameter
    prefixes = [service_path]

    if global_params_prefix:
        prefixes.append(global_params_prefix)

    # Build all parameter paths to fetch
    parameter_paths = build_parameter_paths(parameter_names, prefixes)

    # Fetch parameters in batches
    return get_parameters_batch(parameter_paths, region_name=region_name)


def get_s3_client(region_name="us-east-2") -> boto3.client:
    """Init and return s3 client."""
    return boto3.client(
        "s3",
        region_name=region_name,
        config=Config(s3={"addressing_style": "path"}, signature_version="s3v4"),
    )


def get_dynamodb_table(table_name: str, region_name="us-east-2"):
    """Init and return dynamodb resource."""
    resource = boto3.resource("dynamodb", region_name=region_name)
    return resource.Table(table_name)


def put_csv_to_s3(
    file_path: str, bucket_name: str, s3_key: str, region_name="us-east-2"
):
    """
    Uploads a file to an S3 bucket at the specified key.

    :param file_path: The local path to the file to upload.
    :param bucket_name: The name of the S3 bucket.
    :param s3_key: The destination key (path) in the S3 bucket.
    :param region_name: The AWS region where the bucket is located.
    :return: The S3 URI of the uploaded file.
    """
    s3_client = boto3.client("s3", region_name=region_name)
    s3_client.upload_file(file_path, bucket_name, s3_key)
    return f"s3://{bucket_name}/{s3_key}"


def generate_presigned_url(
    bucket_name: str,
    object_name: str,
    content_type: str,
    expiration=3600,
    region_name="us-east-2",
):
    response = get_s3_client(region_name).generate_presigned_post(
        Bucket=bucket_name,
        Key=object_name,
        Fields={"Content-Type": content_type},
        Conditions=[{"Content-Type": content_type}],
        ExpiresIn=expiration,
    )
    return response


def create_folder_s3(
    bucket_name: str, folder_name: str, region_name="us-east-2"
) -> bool:
    """Create a folder in an S3 bucket."""
    s3_client = get_s3_client(region_name)
    # List of subfolders to create
    subfolders = [
        f"programs/{folder_name}/",
        f"programs/{folder_name}/pdf/",
        f"programs/{folder_name}/video/src/",
    ]

    # Create each subfolder in S3
    for subfolder in subfolders:
        s3_client.put_object(Bucket=bucket_name, Key=subfolder)
        logger.info(
            f"Subfolder '{subfolder}' created successfully in '{bucket_name}' bucket."
        )

    return True


def delete_s3_object(bucket_name, object_path, region_name="us-east-2"):
    """Delete an object from an S3 bucket."""
    try:
        s3_client = get_s3_client(region_name)
        response = s3_client.delete_object(Bucket=bucket_name, Key=object_path)

        # Check for error in response
        if "Error" in response:
            error_message = response.get("Error", {}).get("Message", "Unknown error")
            logger.error(
                f"Error deleting object '{object_path}' from '{bucket_name}' bucket: {error_message}"
            )
            raise S3DeleteError(f"S3 returned error: {error_message}")

        logger.info(
            f"Object '{object_path}' deleted successfully from '{bucket_name}' bucket."
        )
        return True

    except Exception as e:
        logger.error(
            f"Failed to delete object '{object_path}' from '{bucket_name}' bucket. Error: {str(e)}"
        )
        raise S3DeleteError(f"Failed to delete S3 object: {str(e)}")


def delete_folder_s3(
    bucket_name: str, folder_name: str, region_name="us-east-2"
) -> bool:
    """Delete a folder in an S3 bucket."""
    s3_client = get_s3_client(region_name)
    objects_to_delete = s3_client.list_objects_v2(
        Bucket=bucket_name, Prefix=folder_name
    )
    # Check if the folder contains objects
    if "Contents" in objects_to_delete:
        # Create a list of objects to delete
        delete_keys = {
            "Objects": [{"Key": obj["Key"]} for obj in objects_to_delete["Contents"]]
        }

        s3_client.delete_objects(Bucket=bucket_name, Delete=delete_keys)

        logger.info(
            f"Folder '{folder_name}' and its contents deleted successfully from '{bucket_name}' bucket."
        )
    else:
        logger.info(f"Folder '{folder_name}' does not exist or is already empty.")
    # Delete folder from s3 bucket
    s3_client.delete_object(Bucket=bucket_name, Key=(folder_name + "/"))
    logger.info(
        f"Folder '{folder_name}' deleted successfully from '{bucket_name}' bucket."
    )
    return True


def create_presigned_url_view(
    bucket_name: str,
    object_name: str,
    expiration: int = 604800,
    region_name="us-east-2",
) -> str:
    """Generate a presigned URL to share an S3 object.

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string.
    """
    return get_s3_client(region_name).generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": bucket_name, "Key": object_name},
        ExpiresIn=expiration,
        HttpMethod="GET",
    )


def send_to_sqs(
    queue_url, message_body, message_attribute=None, region_name="us-east-2"
):
    sqs_client = boto3.client("sqs", region_name=region_name)
    try:
        response = sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=message_body,
            MessageAttributes=message_attribute or {},
        )
    except (NoCredentialsError, PartialCredentialsError) as e:
        logger.error(f"Error: {e}")
        return None
    except Exception as e:
        logger.exception(f"Error: {e}")
        return None
    return response


def publish_to_sns(sns_client, sns_topic_arn, message: str):
    logger.info(f"Publishing to SNS: {message}")
    try:
        response = sns_client.publish(TopicArn=sns_topic_arn, Message=message)
        logger.info(f"Published to SNS: {response}")
    except Exception as e:
        logger.error(f"Failed to publish to SNS: {e}")
        raise e


class KinesisProducer:
    def __init__(self, stream_name: str, region_name: str = "us-east-2"):
        self.stream_name = stream_name
        self.client = boto3.client("kinesis", region_name=region_name)

    def put_event(self, partition_key: str, data: str) -> dict:
        try:
            response = self.client.put_record(
                StreamName=self.stream_name, Data=data, PartitionKey=partition_key
            )
            logger.info(
                f"Record sent to Kinesis Stream: {self.stream_name}, Response: {response}"
            )
            return response
        except (BotoCoreError, ClientError) as e:
            logger.error(f"Failed to send record to Kinesis Stream: {e}")
            raise
