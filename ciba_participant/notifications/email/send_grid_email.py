import os
import csv
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

import pendulum
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import (
    Attachment,
    Disposition,
    FileContent,
    FileName,
    FileType,
    Mail,
    Content,
)
import base64
from pathlib import Path
from tortoise.functions import Max

from ciba_participant.cohort.crud import CohortRepository
from ciba_participant.common.aws_handler import get_parameter, put_csv_to_s3
from ciba_participant.participant.user_service import (
    UserService,
    check_re_enrolled_participant,
)
from ciba_participant.settings import get_settings, ENV
from ciba_participant.log.logging import logger
from ciba_participant.notifications.email.data_models import (
    ContentType,
    ScaleToParticpant,
)
from ciba_participant.participant.models import (
    Participant,
    Authorized,
    AutorizedRole,
    ParticipantStatus,
    SoleraParticipant,
)
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
)
from ciba_participant.cohort.models import (
    CohortProgramModules,
    CohortStatusEnum,
    Co<PERSON>t,
    CohortMembers,
    CohortMembershipStatus,
)
from ciba_participant.utils import decrypt

settings = get_settings()


class EmailHandler:
    def __init__(self):
        self.sg = SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
        self.clean_up = False

    async def generate_message(
        self,
        to_emails: tuple | str,
        subject: str,
        content_type: ContentType,
        content: Content = None,
        body: str = None,
        bcc_emails_list: list = None,
        dynamic_template_data: dict = None,
        template_id: str = None,
    ) -> Mail:
        message = Mail(
            from_email=settings.DO_NOT_REPLY,
            to_emails=to_emails,
            subject=subject,
        )
        if bcc_emails_list:
            for bcc_email in bcc_emails_list:
                message.add_bcc(bcc_email)

        if content_type == ContentType.HTML:
            message.add_content(content)

        if dynamic_template_data:
            message.dynamic_template_data = dynamic_template_data

        if template_id:
            message.template_id = template_id

        return message

    async def send_email(
        self,
        message: Mail,
    ):
        request_body = message.get()

        try:
            response = self.sg.client.mail.send.post(
                request_body=request_body, timeout=settings.SENDGRID_REQUEST_TIMEOUT
            )
            logger.info(response)
        except Exception as e:
            logger.error(e)

    async def generate_attachment(self, attachment_path: Path) -> Attachment:
        attachment = Attachment()
        encoded_file = await self.encode_file(str(attachment_path))
        attachment.file_content = FileContent(encoded_file)
        attachment.file_type = FileType(ContentType.CSV)
        attachment.file_name = FileName(attachment_path.name)
        attachment.disposition = Disposition("attachment")
        return attachment

    async def encode_file(self, file_path: str) -> str:
        with open(file_path, "rb") as f:
            file_data = f.read()
            return base64.b64encode(file_data).decode()

    async def send_new_participant_email(self):
        subject = "New Participants"

        attachment_path = await get_participants_for_last_24_hours_csv()
        put_csv_to_s3(
            file_path=str(attachment_path),
            bucket_name=settings.AWS_BUCKET_NAME,
            s3_key=f"{str(attachment_path).strip('/tmp')}",
        )

        bcc = await get_admin_emails()

        bcc.remove(settings.DEV_EMAIL) if settings.DEV_EMAIL in bcc else None

        html_content = Content(
            mime_type=ContentType.HTML,
            content=f"<strong>New participant {pendulum.now().strftime('%Y-%m-%d-%H-%M-%S')} {settings.ENV}</strong>",
        )

        message = await self.generate_message(
            to_emails=(settings.DEV_EMAIL, "Participant"),
            subject=subject,
            content=html_content,
            content_type=ContentType.HTML,
            bcc_emails_list=bcc,
        )
        attachment = await self.generate_attachment(attachment_path=attachment_path)
        message.add_attachment(attachment)
        logger.info(f"Sending new participant email with attachment {attachment_path}")

        await self.send_email(
            message=message,
        )
        os.remove(attachment_path)

        s3_path = f"{str(attachment_path).strip('/tmp')}"

        return s3_path

    async def send_new_module_starting_email(self):
        TEMPLATE_ID = "d-41f708443bf74a8091e775503eccc8ca"
        TEMPLATE_SUBJECT = "New module started"

        participants_list = (
            await get_list_of_participants_and_their_new_modules_starting()
        )
        for data in participants_list:
            try:
                # pg.name, pm.started_at, pm.title, pm.short_title, p.email, p.first_name, p.last_name

                dynamic_template_data = {
                    "program_modules_title": data["title"],
                    "program_modules_short_title": data["short_title"],
                    "firstName": data["first_name"],
                    "last_name": data["last_name"],
                }
                logger.info(f"Sending new module starting email for {data['email']}")
                message = await self.generate_message(
                    to_emails=(data["email"], None),
                    subject=TEMPLATE_SUBJECT,
                    template_id=TEMPLATE_ID,
                    content_type=ContentType.PLAIN_TEXT,
                    dynamic_template_data=dynamic_template_data,
                )

                await self.send_email(
                    message=message,
                )
            except Exception as e:
                logger.error(e)

                continue

    async def send_reset_password_email(
        self, email: str, reset_code: str, user_type: str, user_id: str = ""
    ) -> bool:
        """Send reset password email to a participant or patient."""
        TEMPLATE_ID = "d-1747e52e203945d78945a2a8c203e89d"
        TEMPLATE_SUBJECT = "Reset password"

        secrets_prefix = "" if settings.IS_NEW_ENV else f"/{settings.ENV}"

        match user_type:
            case "participant":
                host_param_path = f"{secrets_prefix}/participant/UI_HOST"
                reset_link_body = "reset-password"

            case "patient":
                host_param_path = f"{secrets_prefix}/cibahealth/PATIENT_CLIENT_URL"
                reset_link_body = "forgot-password"

            case _:
                logger.warning(f"Unknown user type: {user_type}")
                return False

        host = get_parameter(host_param_path)
        decrypted_code, _ = decrypt(reset_code)
        username = user_id or email

        reset_link = (
            f"{host}/{reset_link_body}?username={username}&reset_code={decrypted_code}"
        )

        message = await self.generate_message(
            to_emails=email,
            subject=TEMPLATE_SUBJECT,
            template_id=TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data={
                "reset_link": reset_link,
                "reset_code": decrypted_code,
            },
        )

        await self.send_email(message)
        logger.info(f"Send reset password email to {user_type} {email}")

        return True

    async def send_welcome_email(self, participant_id):
        """Send confirm email to a signed-up user. Based on program."""
        TEMPLATE_ID = "d-99273db1cc6142fc964264ccf4a617ec"
        TEMPLATE_SUBJECT = "Welcome to Ciba Health"

        data = await UserService.format_greeting_email(participant_id=participant_id)

        if settings.ENV == ENV.PROD:
            dashboard_url = "https://participant.cibahealth.com/login"
        else:
            dashboard_url = "https://participant-dev.cibahealth.com/login"

        dynamic_template_data = {
            "email": data["email"],
            "first_name": data["first_name"],
            "program_name": data["program_name"],
            "program_date": data["program_date"],
            "dashboard_url": dashboard_url,
            "call_date": data["call_date"],
            "call_time": data["call_time"],
        }

        logger.info(
            f"Sending welcome email to participant {data['email']} with first name "
            f"{data['first_name']}"
        )
        logger.info("Dynamic template data: %s", dynamic_template_data)
        logger.info("Template ID: %s", TEMPLATE_ID)
        logger.info("Template Subject: %s", TEMPLATE_SUBJECT)
        logger.info("Dev email: %s", settings.DEV_EMAIL)

        message = await self.generate_message(
            to_emails=data["email"],
            subject=TEMPLATE_SUBJECT,
            template_id=TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        await self.send_email(message)
        logger.info("Send Welcome email")

    async def new_participant_joined_email(self, participant_id):
        """Send email to admin when a new participant joins."""
        participant = await Participant.filter(id=participant_id).get()
        initials = "".join(
            [part[0].upper() for part in participant.full_name().split(" ")]
        )
        payload = {
            "participant_initials": initials,
            "participant_name": participant.full_name(),
            "participant_profile_url": (
                f"{settings.ADMIN_UI_HOST}/participants/details/"
                f"{participant.id}/summary"
            ),
            "participant_list_url": (f"{settings.ADMIN_UI_HOST}/participants/new"),
            "participant_email": participant.email,
            "participant_mrn": participant.medical_record,
            "participant_company_name": "Solera",
            "participant_member_id": str(participant.member_id),
            "participant_group_id": str(participant.group_id),
        }
        TEMPLATE_ID = "d-b3d0d2ac7a454b93bbdc9378b4a43618"
        TEMPLATE_SUBJECT = "New participant on the platform"
        admin_emails = await get_admin_emails()
        for admin_email in admin_emails:
            generated_message = await self.generate_message(
                to_emails=admin_email,
                subject=TEMPLATE_SUBJECT,
                template_id=TEMPLATE_ID,
                content_type=ContentType.PLAIN_TEXT,
                bcc_emails_list=[settings.DEV_EMAIL],
                dynamic_template_data=payload,
            )
            await self.send_email(generated_message)
            logger.info(
                f"Admin {admin_email} has been notified about new participant {participant.id}"
            )
        return True

    async def send_disenrolled_email(self, participant_id):
        """Send email when user has been disenrolled on solera side"""
        TEMPLATE_ID = "d-e9aa37072ed84399bbc5a4ade17d963f"
        TEMPLATE_SUBJECT = "Participant disenrollment email"
        data = await UserService.format_disenroll_email(participant_id=participant_id)

        dynamic_template_data = {
            "email": data["email"],
            "first_name": data["first_name"],
            "program_name": data["program_name"],
            # "program_date": data["program_date"],
            "disenrolledReason": data["disenrolledReason"],
            "disenrollmentDate": data["disenrollmentDate"],
        }
        message = await self.generate_message(
            to_emails=data["email"],
            subject=TEMPLATE_SUBJECT,
            template_id=TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        await self.send_email(message)
        logger.info("Disenroll email sent successfully")

    async def send_cancelled_session_email(
        self, email, first_name, class_name, class_date
    ):
        """Send email when user has been disenrolled on solera side"""
        TEMPLATE_ID = "d-64c8d34fccbe4672a17452068187c086"
        TEMPLATE_SUBJECT = f"Important Class Update: {class_name} Has Been Canceled"

        dynamic_template_data = {
            "first_name": first_name,
            "class_name": class_name,
            "Class_Date": class_date,
        }

        message = await self.generate_message(
            to_emails=email,
            subject=TEMPLATE_SUBJECT,
            template_id=TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,  # type: ignore
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        await self.send_email(message)
        logger.info("Cancellation email sent successfully")

    async def send_cohort_ending_in_28_days_email(self):
        """Send email when a cohort is ending in 28 days"""
        TEMPLATE_ID = "d-35d3c87d59ad4ab0ac8e5eef28e966e1"
        TEMPLATE_SUBJECT = "Your 28-Day AscendWell Countdown"

        participants = await get_participants_in_cohorts_ending_in_28_days()

        for participant in participants:
            dynamic_template_data = {
                "first_name": participant["first_name"],
                "last_name": participant["last_name"],
                "cohort_name": participant["cohort_name"],
            }

            message = await self.generate_message(
                to_emails=participant["email"],
                subject=TEMPLATE_SUBJECT,
                template_id=TEMPLATE_ID,
                content_type=ContentType.PLAIN_TEXT,
                bcc_emails_list=[settings.DEV_EMAIL],
                dynamic_template_data=dynamic_template_data,
            )

            await self.send_email(message)

        logger.info(
            f"Sent cohort ending in 28 days email to {len(participants)} participants"
        )

    async def send_cohort_ended_email(self, cohort_id):
        """Send email when a cohort has ended"""
        TEMPLATE_ID = "d-35d3c87d59ad4ab0ac8e5eef28e966e1"
        TEMPLATE_SUBJECT = "You've reached the AscendWell finish line. Here's what's next..."

        participants = (
            await CohortMembers.filter(
                cohort_id=cohort_id, status=CohortMembershipStatus.ACTIVE
            )
            .prefetch_related("participant__solera_participant")
            .all()
        )

        for participant in participants:
            dynamic_template_data = {}


async def get_admin_emails() -> list[str]:
    """Return list of admin emails."""

    admin_emails = (
        await Authorized.filter(role=AutorizedRole.ADMIN)
        .all()
        .values_list("email", flat=True)
    )
    return admin_emails


def generate_participants_csv_file(
    headers: list, participants: list[ScaleToParticpant]
) -> Path:
    current_datetime = pendulum.now()
    filepath = Path(f"/tmp/enrolled/{current_datetime.year}/{current_datetime.month}/")
    filepath.mkdir(parents=True, exist_ok=True)
    filename = Path(f"{filepath}/{current_datetime.strftime('%Y-%m-%d_%H-%M')}.csv")
    with open(filename, mode="w", newline="", encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=headers)
        writer.writeheader()
        for participant in participants:
            writer.writerow(participant.model_dump())

    return filename


async def get_participants_for_last_24_hours_csv() -> Path:
    """Retrieve participants enrolled in the last 24 hours and generate a CSV file.

    Returns:
        Path: Path to the generated CSV file
    """
    headers = [
        "created_at",
        "email",
        "phone_number",
        "first_name",
        "last_name",
        "street1",
        "street2",
        "zipCode",
        "city",
        "state",
        "solera_program_id",
        "weight",
        "status",
        "re_enrolled",
    ]

    try:
        # Query for enrollment activities in the last 24 hours
        enrolled = (
            await ParticipantActivity.filter(
                created_at__gte=pendulum.now().subtract(hours=24),
                activity_type=ParticipantActivityEnum.ENROLL,
                participant__status=ParticipantStatus.ACTIVE,
            )
            .prefetch_related(
                "participant__solera_participant", "participant__participant_meta"
            )
            .all()
        )

        logger.info(f"Found {len(enrolled)} enrollment activities in the last 24 hours")

        ready_participants = []
        for activity in enrolled:
            participant_data = await process_participant_for_csv(activity)
            if participant_data:
                ready_participants.append(participant_data)

        participants_csv_path = generate_participants_csv_file(
            headers=headers, participants=ready_participants
        )
        logger.info(
            f"Generated CSV file at {participants_csv_path} with {len(ready_participants)} participants"
        )
        return participants_csv_path
    except Exception as e:
        logger.error(f"Error generating participants CSV: {e}")
        # Create an empty CSV file to avoid breaking the email sending process
        empty_csv_path = Path(
            f"/tmp/empty_participants_{pendulum.now().strftime('%Y-%m-%d-%H-%M-%S')}.csv"
        )
        with open(empty_csv_path, "w") as f:
            f.write(",".join(headers) + "\n")
        return empty_csv_path


async def process_participant_for_csv(
    activity: ParticipantActivity,
) -> Optional[ScaleToParticpant]:
    """Process a participant activity and prepare data for CSV.

    Args:
        activity: The enrollment activity record

    Returns:
        ScaleToParticpant object or None if processing fails
    """
    participant = activity.participant

    # Skip test participants
    if participant.is_test:
        return None

    try:
        # Get the most recent Solera participant record
        solera_participant = await get_latest_solera_participant(participant)
        if not solera_participant:
            logger.error(
                f"Participant {participant.id} has no solera participant record"
            )
            return None

        # Get the most recent metadata record
        metadata = await get_participant_metadata(participant)
        if not metadata:
            logger.error(f"Participant {participant.id} has no metadata record")
            return None

        # Check if participant is re-enrolled
        re_enrolled = await check_re_enrollment_status(participant)

        # Extract address information with proper error handling
        address_data = extract_address_data(metadata)
        if not address_data:
            logger.error(f"Participant {participant.id} has invalid address data")
            return None

        # Extract phone number
        phone_number = metadata.get("phone_number", "")

        # Create the participant data object
        return ScaleToParticpant(
            created_at=activity.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            email=participant.email,
            phone_number=phone_number,
            first_name=participant.first_name,
            last_name=participant.last_name,
            street1=address_data["street1"],
            street2=address_data["street2"],
            zipCode=address_data["zipCode"],
            city=address_data["city"],
            state=address_data["state"],
            solera_program_id=solera_participant.solera_program_id,
            weight=str(metadata.get("user_reported_weight", "")),
            status=participant.status.value,
            re_enrolled=re_enrolled,  # Uncommented this field
        )
    except Exception as e:
        logger.error(f"Error processing participant {participant.id}: {e}")
        return None


async def get_latest_solera_participant(
    participant: Participant,
) -> Optional[SoleraParticipant]:
    """Get the most recent Solera participant record.

    Args:
        participant: The participant object

    Returns:
        The most recent SoleraParticipant record or None
    """
    solera_participants = participant.solera_participant
    if not solera_participants:
        return None

    if len(solera_participants) > 1:
        logger.warning(
            f"Participant {participant.id} has {len(solera_participants)} solera participant records"
        )
        # Sort by created_at to get the most recent one
        return sorted(solera_participants, key=lambda x: x.created_at, reverse=True)[0]

    return solera_participants[0]


async def get_participant_metadata(participant: Participant) -> Optional[dict]:
    """Get the participant metadata.

    Args:
        participant: The participant object

    Returns:
        The metadata dictionary or None
    """
    participant_meta = participant.participant_meta
    if not participant_meta:
        return None

    if len(participant_meta) > 1:
        logger.warning(
            f"Participant {participant.id} has {len(participant_meta)} metadata records"
        )
        # Sort by created_at to get the most recent one
        return sorted(participant_meta, key=lambda x: x.created_at, reverse=True)[
            0
        ].metadata

    return participant_meta[0].metadata


async def check_re_enrollment_status(participant: Participant) -> bool:
    """Check if a participant is re-enrolled using multiple methods.

    Args:
        participant: The participant object

    Returns:
        True if the participant is re-enrolled, False otherwise
    """
    # Method 1: Check for deleted emails containing the current email
    deleted_email_exists = await check_re_enrolled_participant(email=participant.email)

    # Method 2: Check if participant has multiple solera_participant records
    multiple_solera_records = len(participant.solera_participant) > 1

    return deleted_email_exists or multiple_solera_records


def extract_address_data(metadata: dict) -> Optional[dict]:
    """Extract address data from metadata with proper error handling.

    Args:
        metadata: The participant metadata dictionary

    Returns:
        Dictionary with address fields or None if invalid
    """
    try:
        if not metadata or "address" not in metadata:
            return None

        address = metadata["address"]
        required_fields = ["street1", "zipCode", "city", "state"]

        # Check if all required fields exist
        if not all(field in address for field in required_fields):
            return None

        # Process street2 field
        street2 = address.get("street2", "")

        return {
            "street1": address["street1"],
            "street2": street2,
            "zipCode": address["zipCode"],
            "city": address["city"],
            "state": address["state"],
        }
    except Exception as e:
        logger.error(f"Error extracting address data: {e}")
        return None


async def get_list_of_participants_and_their_new_modules_starting() -> list[dict]:
    one_day_ago = datetime.now() - timedelta(days=1)

    # Get program modules that started in the last day
    program_modules = (
        await CohortProgramModules.filter(
            started_at__gte=one_day_ago, started_at__lt=datetime.now()
        )
        .prefetch_related("cohort", "program_module")
        .all()
    )

    # Extract cohort IDs from the program modules
    cohort_ids = [pm.cohort.id for pm in program_modules]

    if not cohort_ids:
        return []

    # Create mappings for quick lookup
    cohort_name_map = {pm.cohort.id: pm.cohort.name for pm in program_modules}
    module_data_map = {
        pm.cohort.id: {
            "started_at": pm.started_at,
            "title": pm.program_module.title,
            "short_title": pm.program_module.short_title,
        }
        for pm in program_modules
    }

    # Fetch active cohort members for these cohorts
    cohort_members = (
        await CohortMembers.filter(
            cohort_id__in=cohort_ids, status=CohortMembershipStatus.ACTIVE
        )
        .prefetch_related("participant")
        .all()
    )

    # Build result using cohort members
    result = []
    for member in cohort_members:
        if member.participant.status == ParticipantStatus.ACTIVE:
            module_data = module_data_map[member.cohort_id]
            result.append(
                {
                    "cohort_name": cohort_name_map[member.cohort_id],
                    "started_at": module_data["started_at"],
                    "title": module_data["title"],
                    "short_title": module_data["short_title"],
                    "email": member.participant.email,
                    "first_name": member.participant.first_name,
                    "last_name": member.participant.last_name,
                }
            )

    return result


async def get_participants_in_cohorts_ending_in_28_days() -> list[dict]:
    """
    Returns a list of participant that are in cohort that end in 28 days and the participants in those cohorts.
    """
    days_ahead = 28
    target_date = pendulum.now().add(days=days_ahead)
    target_date_start = target_date.start_of("day")
    target_date_end = target_date.end_of("day")

    cohorts = (
        await Cohort.filter(
            status=CohortStatusEnum.ACTIVE.value,
            cohort_end_date__gte=target_date_start,
            cohort_end_date__lte=target_date_end,
        )
        .annotate(cohort_end_date=Max("program_modules__ended_at"))
        .prefetch_related("program")
        .all()
    )

    cohort_ids = [cohort.id for cohort in cohorts]
    cohort_map = {cohort.id: cohort for cohort in cohorts}

    if not cohort_ids:
        return []

    cohort_members = (
        await CohortMembers.filter(
            cohort_id__in=cohort_ids, status=CohortMembershipStatus.ACTIVE
        )
        .prefetch_related("participant")
        .all()
    )

    result = []
    for member in cohort_members:
        if member.participant.status == ParticipantStatus.ACTIVE:
            cohort = cohort_map[member.cohort_id]

            result.append(
                {
                    "email": member.participant.email,
                    "first_name": member.participant.first_name,
                    "program_name": cohort.program.title,
                    "end_date": (await cohort.end_date).strftime("%Y-%m-%d"),
                }
            )

    return result
