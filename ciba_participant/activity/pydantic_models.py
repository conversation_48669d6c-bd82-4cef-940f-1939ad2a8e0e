from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from ciba_participant.activity.models import (
    ActivityUnit,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ParticipantActivityEnum,
)
from ciba_participant.program.pydantic_models import ProgramModuleSectionOutput
from ciba_participant.solera.data_preparer import SoleraData


class ParticipantActivityBase(BaseModel):
    participant_id: UUID
    value: str
    unit: ActivityUnit
    activity_device: ParticipantActivityDevice
    activity_category: ParticipantActivityCategory
    activity_type: ParticipantActivityEnum
    section_id: Optional[UUID] = None
    live_session_id: Optional[UUID] = None
    is_content_related: bool = False


class ParticipantActivityCreate(ParticipantActivityBase):
    pass


class ParticipantActivityUpdate(ParticipantActivityBase):
    pass


class ParticipantActivityOutput(ParticipantActivityBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        return cls(**obj_dict)


class ParticipantSectionProgress(BaseModel):
    section: ProgramModuleSectionOutput
    activities: Optional[list[ParticipantActivityOutput]] = None


class PlatformType(Enum):
    participant = "participant"
    patient = "patient"


class BaseProgressData(BaseModel):
    platform: PlatformType = PlatformType.participant


class ProgressData(BaseProgressData):
    participant_id: str = Field(..., description="Participant ID")
    activity_category: str = Field(..., description="Category of the activity")
    activity_type: str = Field(..., description="Type of the activity")
    activity_device: Optional[str] = Field(
        default="manual_input", description="Device of the activity"
    )
    unit: Optional[str] = Field(default="action", description="Unit of the activity")
    program_module_id: Optional[str] = Field(
        default=None, description="ID of the program module"
    )
    program_module_section_id: Optional[str] = Field(
        default=None, description="ID of the program module section"
    )
    live_session_id: Optional[str] = Field(
        default=None, description="ID of the live session"
    )
    title: Optional[str] = Field(
        default=None, description="Title of the program module section"
    )
    description: Optional[str] = Field(
        default=None, description="Description of the program module section"
    )
    metadata: Optional[dict] = Field(
        default=None, description="Metadata of the program module section"
    )
    data: dict = Field(..., description="Data related to the activity.")
    created_at: str = Field(..., description="Timestamp of the activity.")
    provider_data: Optional[SoleraData] = Field(
        default=None, description="Data related to the participants provider"
    )
    original_timestamp: Optional[str] = Field(
        default=None, description="Original timestamp of the activity"
    )
