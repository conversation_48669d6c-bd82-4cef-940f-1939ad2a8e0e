from datetime import datetime
from typing import List, Optional
from uuid import UUID

from .schemas import (
    ParticipantActivityInput,
)
from .pydantic_models import ParticipantActivityOutput, ParticipantSectionProgress

from loguru import logger
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
)
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.program.pydantic_models import ProgramModuleSectionOutput


class ParticipantActivityRepository:
    @staticmethod
    async def get_activities_by_participant_ids(
        participant_ids: List[UUID],
    ) -> List[ParticipantActivity]:
        return await ParticipantActivity.filter(participant_id__in=participant_ids)

    @staticmethod
    async def get_activities_ids_by_participant_id(participant_id: UUID) -> List[UUID]:
        return await ParticipantActivity.filter(
            participant_id=participant_id
        ).values_list("id", flat=True)

    @staticmethod
    async def get_participant_activities(
        page: int,
        per_page: int,
        participant_id: Optional[UUID] = None,
        activity_type_id: Optional[UUID] = None,
    ) -> List[ParticipantActivityOutput]:
        query = ParticipantActivity.all().offset((page - 1) * per_page).limit(per_page)
        if participant_id:
            query = query.filter(participant_id=participant_id)
        if activity_type_id:
            query = query.filter(activity_id=activity_type_id)
        activities = await query
        return [
            await ParticipantActivityOutput.from_orm(activity)
            for activity in activities
        ]

    @staticmethod
    async def get_participant_module_progress(
        module: CohortProgramModules,
        activities: list[ParticipantActivity],
        include_milestone_activities: bool = False,
    ) -> (
        list[ParticipantSectionProgress]
        | tuple[
            list[ParticipantSectionProgress],
            list[ParticipantActivity],
            list[ParticipantActivity],
        ]
    ):
        """
        Assigns the participant's activities to each section in a module.

        Args:
            module: A CohortProgramModules object containing sections.
            activities: ParticipantActivity list for this module.
            include_milestone_activities: If True, also return lists of GROUP and COACH activities.

        Returns:
            - If include_milestones=False:
                List of ParticipantSectionProgress (each with `.activities` as a list).
            - If include_milestones=True:
                Tuple[
                  list of ParticipantSectionProgress,
                  list of GROUP activities,
                  list of COACH activities
                  ]
        """
        section_pydantic_list = [
            ProgramModuleSectionOutput.model_validate(section)
            for section in module.program_module.sections
        ]

        if not activities:
            logger.info("No activities found")
            sections = [
                ParticipantSectionProgress(section=section, activities=[])
                for section in section_pydantic_list
            ]
            if include_milestone_activities:
                return sections, [], []

            return sections

        sections = []

        for section in section_pydantic_list:
            if section.activity_type in [
                ParticipantActivityEnum.WEIGHT,
                ParticipantActivityEnum.ACTIVITY,
            ]:
                matching_activities = [
                    act
                    for act in activities
                    if act.activity_type == section.activity_type
                ]
            else:
                matching_activities = [
                    act for act in activities if act.section_id == section.id
                ]

            section_progress = ParticipantSectionProgress(
                section=section, activities=matching_activities
            )

            sections.append(section_progress)

        class_activities = [
            activity
            for activity in activities
            if activity.activity_type == ParticipantActivityEnum.GROUP
        ]
        chat_activities = [
            activity
            for activity in activities
            if activity.activity_type == ParticipantActivityEnum.COACH
        ]

        if include_milestone_activities:
            return sections, class_activities, chat_activities

        return sections

    @staticmethod
    async def create_participant_activity(
        participant_activity: ParticipantActivityInput,
    ) -> ParticipantActivityOutput:
        activity = ParticipantActivity(
            participant_id=participant_activity.participant_id,
            value=participant_activity.value,
            unit=participant_activity.unit,
            activity_device=participant_activity.activity_device,
            activity_category=participant_activity.activity_category,
            activity_type=participant_activity.activity_type,
            is_content_related=participant_activity.is_content_related,
        )

        if participant_activity.created_at:
            activity.created_at = participant_activity.created_at
        if participant_activity.section_id:
            activity.section_id = participant_activity.section_id
        if participant_activity.live_session_id:
            activity.live_session_id = participant_activity.live_session_id

        await activity.save()

        return await ParticipantActivityOutput.from_orm(activity)

    @staticmethod
    async def get_participant_activity(
        activity_id: UUID,
    ) -> Optional[ParticipantActivityOutput]:
        activity = await ParticipantActivity.get_or_none(id=activity_id)
        if activity:
            return await ParticipantActivityOutput.from_orm(activity)
        return None

    @staticmethod
    async def update_participant_activity(
        activity_id: UUID, participant_activity: ParticipantActivityInput
    ) -> Optional[ParticipantActivityOutput]:
        await ParticipantActivity.filter(id=activity_id).update(
            **participant_activity.model_dump(exclude={"id"}, exclude_none=True)
        )
        activity = await ParticipantActivity.get(id=activity_id)
        return await ParticipantActivityOutput.from_orm(activity)

    @staticmethod
    async def delete_participant_activity(activity_id: UUID) -> None:
        await ParticipantActivity.filter(id=activity_id).delete()

    @staticmethod
    async def get_activities_by_participant_id(
        participant_id: UUID,
        activity_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> List[ParticipantActivity]:
        query = ParticipantActivity.filter(participant_id=participant_id)

        if activity_type:
            query = query.filter(activity_type=activity_type)
        if start_date:
            query = query.filter(created_at__gte=start_date)
        if end_date:
            query = query.filter(created_at__lt=end_date)

        data = await query.order_by("-created_at").all()

        return data
