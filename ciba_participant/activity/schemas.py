from datetime import datetime

from pydantic import BaseModel
from uuid import UUID
from ciba_participant.activity.models import (
    ActivityUnit,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ParticipantActivityEnum,
)


class ActivityTypeInput(BaseModel):
    title: str


class ParticipantActivityInput(BaseModel):
    created_at: datetime | None = None
    participant_id: UUID
    value: str | int
    unit: ActivityUnit
    activity_device: ParticipantActivityDevice
    activity_category: ParticipantActivityCategory
    activity_type: ParticipantActivityEnum
    section_id: UUID | None = None
    live_session_id: UUID | None = None
    is_content_related: bool = False
