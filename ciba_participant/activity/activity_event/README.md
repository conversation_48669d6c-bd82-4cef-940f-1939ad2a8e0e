## Using as ASGIMiddleware(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, ...)

```py
from ciba_participant.activity import ActivityEvent
from ciba_participant.common import ConnectionManager, ConnInfo

conn_info = ConnInfo(...)

app = FastAPI()

app.add_middleware(ConnectionManager(conn_info).asgi_middleware())

@app.post("/events")
async def register_event():
    ...
```

## Using Standalone (for testing)

```py
from ciba_participant.activity import ActivityEvent
from ciba_participant.common import ConnectionManager, ConnInfo

conn_info = ConnInfo(...)

async def test_something():
    async with ConnectionManager(conn_info) as cm:
        async with cm.use_connection() as conn:
            ...

```

## Creating events

You can create events using the generic `create_activity_event` or use submodules own event factory functions.

```py
from ciba_participant.activity import create_activity_event

generic_event = create_activity_event(...)
```

```py
from ciba_participant.activity import weight

weight_event = weight.create_manual_weight_registration_event(
    ...
)
```

## Saving events
After creating an event you can save it using `ActivityEvent.save` static method. Remember that you need to be in a ConnectionManager context, you can setup it as an ASGIMiddleware or standlone for testing of one-off scripting.

```py
from ciba_participant.activity import ActivityEvent
from ciba_participant.common import ConnectionManager, ConnInfo

conn_info = ConnInfo(...)

app = FastAPI()

app.add_middleware(ConnectionManager(conn_info).asgi_middleware())

@app.post("/events")
async def register_event():
    event = create_activity_event(...)
    await ActivityEvent.save(event)
```

## Grouping events

You can group events by its activity_id. It is just an uuid4 that you need to create and track to group many events as part of a single activity:

```py
from uuid import uuid4

activity_id = uuid4()

event_1 = create_activity_event(..., activity_id=activity_id)
event_2 = create_activity_event(..., activity_id=activity_id)
await ActivityEvent.save(event_1, event_2)

events = await ActivityEvent.filter(activity_id=activity_id)
```

You can also query by `id`, `name`, `timestamp`, `activity_type` and also using a custom query passing a PyPika Criterion object as `custom`.

![activity event schema](docs/activity_event_schema.png)
