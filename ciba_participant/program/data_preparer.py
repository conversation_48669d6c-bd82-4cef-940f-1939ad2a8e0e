from datetime import datetime, timedelta
from tortoise.functions import Count
from ciba_participant.cohort.models import Cohort


class DataPreparer:
    async def prepare_data(self, program_type: str) -> list:
        program = await self._query_with_type(program_type)
        return program

    async def _query_with_type(self, program_type: str) -> list:
        """Get data from Cohort model with program type."""
        #TODO: this should be used for scheduling only
        tomorrow = datetime.now().date() + timedelta(days=1)

        cohort_query = (
            Cohort.annotate(
                count_person=Count("participants__id")
            )
            .filter(
                started_at__gte=tomorrow,
                program__title=program_type
            )
            .all()
            .group_by(
                "id",
                "limit",
                "program__title",
                "started_at"
            )
            .order_by("started_at")
        )

        result = await cohort_query

        # Filter the result to get the first row where pr_limit > count_person
        filtered_result = [cohort for cohort in result if cohort.limit > cohort.count_person]

        return filtered_result[:1]
