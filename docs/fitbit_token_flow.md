# Fitbit Token Management Flow

This document provides a detailed explanation of the Fitbit token management flow, including sequence diagrams for different scenarios.

## Token Lifecycle

Fitbit tokens have the following lifecycle:

1. **Initial Authentication**: User authenticates with Fitbit and grants access to our application
1. **Token Usage**: Access token is used to make API calls
1. **Token Refresh**: When the access token expires, it is refreshed using the refresh token
1. **Old Token Storage**: Old refresh tokens are stored as backups but not actively managed

## Sequence Diagrams

### Normal API Call Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Fitbit API

    App->>DB: Check if access token is valid
    DB-->>App: Return access token status

    alt Access token is valid
        App->>API: Make API call with access token
        API-->>App: Return data
    else Access token is expired
        App->>App: Initiate token refresh flow
    end
```

### Token Refresh Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Fitbit API

    App->>DB: Check if refresh token is expired
    DB-->>App: Return refresh token status

    alt Refresh token is expired
        App->>DB: Mark connection as unhealthy
        App->>App: Require re-authentication
    else Refresh token is valid
        App->>API: Request new tokens with refresh token
        API-->>App: Return new access and refresh tokens

        App->>DB: Store new tokens
        App->>DB: Store old refresh token as backup
        App->>DB: Mark connection as healthy
    end
```

### Error Handling Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Fitbit API

    App->>API: Request new tokens with refresh token

    alt Invalid grant error
        API-->>App: Return invalid grant error
        App->>DB: Mark connection as unhealthy
        App->>App: Require re-authentication
        App->>App: Log token error
        App->>App: Track token metrics
    else Other error
        API-->>App: Return other error
        App->>DB: Mark connection as unhealthy
        App->>App: Log error
        App->>App: Track error metrics
    end
```

### Data Fetch Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Fitbit API

    App->>API: Fetch data with access token

    alt Successful data fetch
        API-->>App: Return data
        App->>DB: Mark connection as healthy
        App->>App: Log success
        App->>App: Track success metrics
    else Data fetch failure
        API-->>App: Return error
        App->>DB: Mark connection as unhealthy
        App->>App: Log failure
        App->>App: Track failure metrics
    end
```

## Implementation Details

### Token Storage

Tokens are stored in the `Fitbit` model with the following fields:

- `access_token`: The current access token
- `refresh_token`: The current refresh token
- `old_refresh_token`: The previous refresh token (for backup)
- `access_token_expires_at`: When the access token expires
- `refresh_token_expires_at`: When the refresh token expires (optional, as Fitbit doesn't specify)

### Token Expiration Checks

The model includes methods to check token expiration:

- `is_access_token_expired()`: Checks if the access token is expired
- `is_refresh_token_expired()`: Checks if the refresh token is expired

### Old Refresh Token Handling

Unlike Withings, Fitbit doesn't have the concept of an old refresh token that expires when a new access token is used. The old refresh token is stored as a backup but not actively managed.

## Key Differences from Withings

1. **Access Token Expiration**: Fitbit access tokens expire after 8 hours, compared to 3 hours for Withings
1. **Refresh Token Expiration**: Fitbit doesn't specify an explicit expiration for refresh tokens
1. **Old Refresh Token Handling**: Fitbit doesn't require expiring old refresh tokens when new access tokens are used
1. **Token Refresh Flow**: Fitbit has a simpler token refresh flow without old token expiration management

## Rate Limiting Considerations

Fitbit has rate limits that vary by API endpoint and subscription level. When rate limits are exceeded:

1. The API returns HTTP 429 (Too Many Requests)
1. The system logs the rate limit error
1. The system tracks rate limit metrics
1. The connection is NOT marked as unhealthy (rate limit errors are temporary and expected)

The system includes exponential backoff retry logic to handle rate limits gracefully.
