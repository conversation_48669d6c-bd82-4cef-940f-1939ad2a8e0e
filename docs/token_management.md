# Token Management for IoT Devices

This document explains how token management works for Withings and Fitbit devices in the CIBA IoT ETL system. It covers token expiration rules, refresh flows, and implementation details.

## Table of Contents

- [Overview](#overview)
- [Withings Token Management](#withings-token-management)
  - [Token Expiration Rules](#withings-token-expiration-rules)
  - [Token Refresh Flow](#withings-token-refresh-flow)
  - [Old Refresh Token Handling](#withings-old-refresh-token-handling)
  - [Implementation Details](#withings-implementation-details)
  - [Flow Diagram](#withings-flow-diagram)
  - [Detailed Diagrams](withings_token_diagram.md)
- [Fitbit Token Management](#fitbit-token-management)
  - [Token Expiration Rules](#fitbit-token-expiration-rules)
  - [Token Refresh Flow](#fitbit-token-refresh-flow)
  - [Implementation Details](#fitbit-implementation-details)
  - [Flow Diagram](#fitbit-flow-diagram)
  - [Detailed Diagrams](fitbit_token_diagram.md)
- [Comparison](#comparison)
- [Monitoring and Metrics](#monitoring-and-metrics)

## Overview

Both Withings and Fitbit use OAuth 2.0 for authentication, but they have different token expiration rules and refresh flows. This document explains these differences and how they are handled in our system.

## Withings Token Management

### Withings Token Expiration Rules

Withings has the following token expiration rules:

- **Access Token**: Expires after 3 hours
- **Refresh Token**: Expires after 1 year
- **Old Refresh Token**: Expires either:
  - 8 hours after a new refresh token is issued, OR
  - Immediately once the new access token is used

### Withings Token Refresh Flow

1. When an access token expires, we attempt to refresh it using the refresh token
1. If the refresh token is still valid, we get a new access token and refresh token
1. The old refresh token is stored in the database and marked to expire in 8 hours
1. When the new access token is used, the old refresh token is immediately expired

### Withings Old Refresh Token Handling

The old refresh token is stored to provide a fallback in case there are issues with the new refresh token. This is particularly useful in distributed systems where token refresh might happen concurrently.

When we use a new access token, we immediately expire the old refresh token by:

1. Setting its expiration timestamp to the current time
1. Logging the expiration
1. Tracking the expiration with metrics

### Withings Implementation Details

The implementation includes:

1. **Token Storage**: Tokens are stored in the `Withings` model with expiration timestamps
1. **Token Refresh**: The `refresh_withings_token` function handles token refresh
1. **Old Token Expiration**: The `expire_old_refresh_token` method marks old tokens as expired
1. **Expiration Checks**: Methods like `is_access_token_expired` and `is_refresh_token_expired` check token validity

### Withings Flow Diagram

```ascii
┌─────────────────┐     Access Token      ┌─────────────────┐
│                 │      Expires          │                 │
│  Valid Tokens   ├────────────────────► │  Refresh Tokens  │
│                 │                       │                 │
└─────────────────┘                       └────────┬────────┘
                                                   │
                                                   │ Get New Tokens
                                                   ▼
┌─────────────────┐                       ┌─────────────────┐
│                 │                       │                 │
│  Use New Tokens ◄───────────────────────┤  Store Tokens   │
│                 │                       │                 │
└────────┬────────┘                       └─────────────────┘
         │                                        │
         │ Use New Access Token                   │ Store Old Refresh Token
         │                                        │ (Expires in 8 hours)
         ▼                                        ▼
┌─────────────────┐                       ┌─────────────────┐
│                 │                       │                 │
│  Expire Old     │                       │  Old Refresh    │
│  Refresh Token  │                       │  Token Storage  │
│                 │                       │                 │
└─────────────────┘                       └─────────────────┘
```

## Fitbit Token Management

### Fitbit Token Expiration Rules

Fitbit has the following token expiration rules:

- **Access Token**: Expires after 8 hours (28800 seconds)
- **Refresh Token**: No explicit expiration, but should be refreshed regularly
- **Old Refresh Token**: Not actively managed by Fitbit (stored as backup only)

### Fitbit Token Refresh Flow

1. When an access token expires, we attempt to refresh it using the refresh token
1. If the refresh token is valid, we get a new access token and refresh token
1. The old refresh token is stored in the database as a backup, but not actively used
1. Unlike Withings, there's no concept of expiring the old refresh token when using the new access token

### Fitbit Implementation Details

The implementation includes:

1. **Token Storage**: Tokens are stored in the `Fitbit` model with expiration timestamps
1. **Token Refresh**: The `refresh_fitbit_token` function handles token refresh
1. **Expiration Checks**: Methods like `is_access_token_expired` and `is_refresh_token_expired` check token validity
1. **Backup Storage**: Old refresh tokens are stored but not actively managed

### Fitbit Flow Diagram

```ascii
┌─────────────────┐     Access Token      ┌─────────────────┐
│                 │      Expires          │                 │
│  Valid Tokens   ├────────────────────► │  Refresh Tokens  │
│                 │                       │                 │
└─────────────────┘                       └────────┬────────┘
                                                   │
                                                   │ Get New Tokens
                                                   ▼
┌─────────────────┐                       ┌─────────────────┐
│                 │                       │                 │
│  Use New Tokens ◄───────────────────────┤  Store Tokens   │
│                 │                       │                 │
└─────────────────┘                       └─────────────────┘
                                                   │
                                                   │ Store Old Refresh Token
                                                   │ (As Backup Only)
                                                   ▼
                                          ┌─────────────────┐
                                          │                 │
                                          │  Old Refresh    │
                                          │  Token Storage  │
                                          │                 │
                                          └─────────────────┘
```

## Comparison

| Feature | Withings | Fitbit |
|---------|----------|--------|
| Access Token Expiration | 3 hours | 8 hours |
| Refresh Token Expiration | 1 year | No explicit expiration |
| Old Refresh Token Handling | Expires after 8 hours or when new access token is used | Stored as backup only |
| Token Refresh Flow | More complex with old token expiration | Simpler without old token expiration |
| API Documentation | Clear about token expiration rules | Less explicit about refresh token expiration |

## Rate Limiting Considerations

Both Withings and Fitbit APIs have rate limits:

- **Withings**: 120 requests per minute per API project
- **Fitbit**: Varies by API endpoint and subscription level

When rate limits are exceeded:

1. The system logs the rate limit error
1. The system tracks rate limit metrics
1. **Important**: The connection is NOT marked as unhealthy for rate limit errors, as they are temporary and expected

The system includes exponential backoff retry logic to handle rate limits gracefully.

## Monitoring and Metrics

We track the following metrics for token management:

### Withings Metrics

- `withings.tokens.expired`: Counts expired token events
- `withings.tokens.old_refresh_expired`: Tracks when old refresh tokens are expired
- `withings.api.success`: Counts successful API calls
- `withings.api.error`: Counts API errors with status codes
- `withings.api.rate_limit`: Specifically tracks rate limit errors

### Fitbit Metrics

- `fitbit.tokens.expired`: Counts expired token events
- `fitbit.api.call`: Tracks API calls
- `fitbit.data.fetch_success`: Counts successful data fetches
- `fitbit.data.fetch_failure`: Counts failed data fetches

These metrics help monitor the health of the token refresh process and identify issues with token expiration or API rate limits.
