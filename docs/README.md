# CIBA IoT ETL Documentation

This directory contains documentation for the CIBA IoT ETL system.

## Token Management

The system integrates with various IoT devices that use OAuth 2.0 for authentication. Each device has its own token management rules and flows.

### Overview Documents

- [Token Management Overview](token_management.md) - Comprehensive overview of token management for all supported devices

### Device-Specific Documentation

- [Withings Token Flow](withings_token_flow.md) - Detailed explanation of Withings token management with sequence diagrams
- [Withings Token Diagrams](withings_token_diagram.md) - Visual diagrams of Withings token lifecycle and refresh flow
- [Fitbit Token Flow](fitbit_token_flow.md) - Detailed explanation of Fitbit token management with sequence diagrams
- [Fitbit Token Diagrams](fitbit_token_diagram.md) - Visual diagrams of Fitbit token lifecycle and refresh flow

## Viewing Diagrams

The sequence diagrams in these documents use Mermaid syntax. To view them properly:

1. Use a Markdown viewer that supports Mermaid (like GitHub's built-in viewer)
1. Use a Mermaid live editor: [https://mermaid.live/](https://mermaid.live/)
1. Install a Mermaid plugin for your IDE or Markdown viewer

## Implementation

The token management implementation can be found in:

- Withings: `src/ciba_iot_etl/extract/withings_api/processor.py` and `src/ciba_iot_etl/models/db/withings.py`
- Fitbit: `src/ciba_iot_etl/extract/fitbit_api/processor.py` and `src/ciba_iot_etl/models/db/fitbit.py`

## Metrics

Token-related metrics are logged with the prefix `DATADOG_METRIC:` for easy parsing by Datadog. These metrics can be used to monitor:

- Token expiration events
- API success/failure rates
- Rate limit occurrences
- Error distributions

See the [Token Management Overview](token_management.md#monitoring-and-metrics) for more details on available metrics.
