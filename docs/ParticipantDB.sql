
CREATE TABLE "program" (
	"id" uuid NOT NULL,
	"tile" varchar(255),
	"created_at" timestamp,
	"updated_at" timestamp,
	PRIMARY KEY("id")
);

CREATE TABLE "program_module" (
	"id" uuid NOT NULL,
	"title" varchar(255),
	"program_id" uuid,
	"created_at" timestamp,
	"updated_at" timestamp,
	"short_title" varchar(255),
	"started_at" timestamp,
	"ended_at" timestamp,
	"description" text(65535),
	PRIMARY KEY("id")
);

CREATE TABLE "program_module_section" (
	"id" serial NOT NULL,
	"title" varchar(255),
	"program_module_id" uuid,
	"activity_type" uuid,
	"description" text(65535),
	"metadata" json,
	PRIMARY KEY("id")
);

CREATE TABLE "cohort" (
	"id" uuid NOT NULL,
	"program_id" uuid,
	"name" varchar(255),
	"started_at" timestamp,
	"limit" int,
	"created_at" timestamp,
	"updated_at" timestamp,
	PRIMARY KEY("id")
);

CREATE TYPE "status_t" AS ENUM ('active', 'pending', 'inactive');

CREATE TABLE "participant" (
	"id" uuid NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	"email" varchar(255),
	"first_name" varchar(255),
	"last_name" varchar(255),
	"status" status_t,
	"cognito_sub" uuid,
	"medical_record" varchar(255),
	"is_test" boolean,
	"last_reset" timestamp,
	"metadata" json,
	PRIMARY KEY("id")
);

CREATE TABLE "cohort_members" (
	"cohort_id" uuid NOT NULL,
	"particiapnt_id" uuid,
	"created_at" timestamp,
	"updated_at" timestamp,
	PRIMARY KEY("cohort_id")
);

CREATE TABLE "live_calls" (
	"id" uuid NOT NULL,
	"cohort_id" uuid,
	PRIMARY KEY("id")
);

CREATE TABLE "solera_participant" (
	"id" uuid NOT NULL,
	"participant_id" uuid,
	"solera_id" uuid,
	"solera_key" varchar(255),
	"solera_program_id" uuid,
	PRIMARY KEY("id")
);

CREATE TYPE "unit_t" AS ENUM ('kg', 'lb');

,CREATE TYPE "activity_device_t" AS ENUM ('withings', 'manual');

,CREATE TYPE "activity_category_t" AS ENUM ();

CREATE TABLE "participant_activity" (
	"id" uuid NOT NULL,
	"participant_id" uuid,
	"value" varchar(255),
	"unit" unit_t,
	"activity_device" activity_device_t,
	"created_at" timestamp,
	"activity_category" activity_category_t,
	"activity_id" uuid,
	PRIMARY KEY("id", "created_at")
);

CREATE TABLE "solera_activity" (
	"id" uuid NOT NULL,
	"activity_type_id" uuid,
	PRIMARY KEY("id")
);

CREATE TABLE "activity_types" (
	"id" uuid NOT NULL,
	"title" varchar(255),
	PRIMARY KEY("id")
);

CREATE TABLE "heads_up_participant" (
	"id" uuid NOT NULL,
	"participant_id" uuid,
	"heads_up_token" varchar(255),
	PRIMARY KEY("id")
);

CREATE TABLE "participant_meta" (
	"id" uuid NOT NULL,
	"metadata" json,
	"paticipant_id" uuid,
	PRIMARY KEY("id")
);

ALTER TABLE "program_module"
ADD FOREIGN KEY("program_id") REFERENCES "program"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "program_module_section"
ADD FOREIGN KEY("program_module_id") REFERENCES "program_module"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "cohort"
ADD FOREIGN KEY("program_id") REFERENCES "program"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "cohort_members"
ADD FOREIGN KEY("cohort_id") REFERENCES "cohort"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "participant"
ADD FOREIGN KEY("id") REFERENCES "cohort_members"("particiapnt_id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "cohort"
ADD FOREIGN KEY("id") REFERENCES "live_calls"("cohort_id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "solera_participant"
ADD FOREIGN KEY("participant_id") REFERENCES "participant"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "participant_activity"
ADD FOREIGN KEY("participant_id") REFERENCES "participant"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "activity_types"
ADD FOREIGN KEY("id") REFERENCES "solera_activity"("activity_type_id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "program_module_section"
ADD FOREIGN KEY("activity_type") REFERENCES "activity_types"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "heads_up_participant"
ADD FOREIGN KEY("participant_id") REFERENCES "participant"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "participant_activity"
ADD FOREIGN KEY("activity_id") REFERENCES "activity_types"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
ALTER TABLE "participant_meta"
ADD FOREIGN KEY("paticipant_id") REFERENCES "participant"("id")
ON UPDATE NO ACTION ON DELETE NO ACTION;
