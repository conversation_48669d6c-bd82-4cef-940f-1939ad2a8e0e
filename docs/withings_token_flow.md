# Withings Token Management Flow

This document provides a detailed explanation of the Withings token management flow, including sequence diagrams for different scenarios.

## Token Lifecycle

Withings tokens have the following lifecycle:

1. **Initial Authentication**: User authenticates with Withings and grants access to our application
1. **Token Usage**: Access token is used to make API calls
1. **Token Refresh**: When the access token expires, it is refreshed using the refresh token
1. **Old Token Expiration**: Old refresh tokens expire either after 8 hours or when the new access token is used

## Sequence Diagrams

### Normal API Call Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Withings API

    App->>DB: Check if access token is valid
    DB-->>App: Return access token status

    alt Access token is valid
        App->>API: Make API call with access token
        API-->>App: Return data
    else Access token is expired
        App->>App: Initiate token refresh flow
    end
```

### Token Refresh Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Withings API

    App->>DB: Check if refresh token is expired
    DB-->>App: Return refresh token status

    alt Refresh token is expired
        App->>DB: Mark connection as unhealthy
        App->>App: Require re-authentication
    else Refresh token is valid
        App->>DB: Check if old refresh token is valid
        DB-->>App: Return old refresh token status

        alt Use old refresh token (if valid)
            App->>API: Request new tokens with old refresh token
        else Use current refresh token
            App->>API: Request new tokens with current refresh token
        end

        API-->>App: Return new access and refresh tokens

        App->>DB: Store new tokens
        App->>DB: Store old refresh token with 8-hour expiration
        App->>DB: Mark connection as healthy
    end
```

### Old Refresh Token Expiration Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Withings API

    App->>API: Make API call with new access token
    API-->>App: Return data

    Note over App,DB: When new access token is used

    App->>DB: Expire old refresh token immediately
    App->>App: Log token expiration
    App->>App: Track expiration metrics
```

### Error Handling Flow

```mermaid
sequenceDiagram
    participant App as CIBA IoT ETL
    participant DB as Database
    participant API as Withings API

    App->>API: Request new tokens with refresh token

    alt Rate limit error
        API-->>App: Return rate limit error (601)
        App->>DB: Mark connection as unhealthy
        App->>App: Log rate limit error
        App->>App: Track rate limit metrics
    else Invalid refresh token
        API-->>App: Return invalid grant error
        App->>DB: Mark connection as unhealthy
        App->>App: Require re-authentication
        App->>App: Log token error
        App->>App: Track token metrics
    else Other error
        API-->>App: Return other error
        App->>DB: Mark connection as unhealthy
        App->>App: Log error
        App->>App: Track error metrics
    end
```

## Implementation Details

### Token Storage

Tokens are stored in the `Withings` model with the following fields:

- `access_token`: The current access token
- `refresh_token`: The current refresh token
- `old_refresh_token`: The previous refresh token (for backup)
- `access_token_expires_at`: When the access token expires
- `refresh_token_expires_at`: When the refresh token expires
- `old_refresh_token_expires_at`: When the old refresh token expires

### Token Expiration Checks

The model includes methods to check token expiration:

- `is_access_token_expired()`: Checks if the access token is expired
- `is_refresh_token_expired()`: Checks if the refresh token is expired
- `is_old_refresh_token_expired()`: Checks if the old refresh token is expired

### Old Refresh Token Expiration

When a new access token is used, the old refresh token is expired by:

1. Setting `old_refresh_token_expires_at` to the current time
1. Logging the expiration
1. Tracking the expiration with metrics

This ensures that old refresh tokens are properly expired according to Withings' requirements.

## Rate Limiting Considerations

Withings has a rate limit of 120 requests per minute per API project. When the rate limit is exceeded:

1. The API returns status code 601 in the response body (while maintaining HTTP 200 OK status)
1. The system logs the rate limit error
1. The system tracks rate limit metrics
1. The connection is NOT marked as unhealthy (rate limit errors are temporary and expected)

The system includes exponential backoff retry logic to handle rate limits gracefully.
