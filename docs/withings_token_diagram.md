# Withings Token Flow Diagram

This diagram illustrates the Withings token lifecycle and refresh flow.

## Token Lifecycle Diagram

```mermaid
graph TD
    A[Initial Authentication] -->|Get Initial Tokens| B[Store Access & Refresh Tokens]
    B --> C[Use Access Token for API Calls]

    C -->|Access Token Expires| D{Refresh Token Valid?}

    D -->|Yes| E[Refresh Tokens]
    D -->|No| F[Require Re-authentication]

    E -->|Get New Tokens| G[Store New Tokens]
    G -->|Store Old Refresh Token| H[Old Refresh Token]
    G --> I[Use New Access Token]

    I -->|Triggers| J[Expire Old Refresh Token]

    H -->|Expires after 8 hours| K[Old Token Expired]
    J --> K

    K --> L[Log & Track Metrics]
```

## Token Expiration Timeline

```mermaid
gantt
    title Withings Token Expiration Timeline
    dateFormat  YYYY-MM-DD

    section Access Token
    Valid                      :a1, 2023-01-01, 3h
    Expired                    :a2, after a1, 1y

    section Refresh Token
    Valid                      :r1, 2023-01-01, 365d
    Expired                    :r2, after r1, 1y

    section Old Refresh Token
    Valid                      :o1, 2023-01-01, 8h
    Expired                    :o2, after o1, 1y

    section Events
    Initial Auth               :milestone, m1, 2023-01-01, 0d
    Token Refresh              :milestone, m2, 2023-01-01, 0d
    Use New Access Token       :milestone, m3, 2023-01-01, 0d
```

## Key Points

1. **Access Token**: Expires after 3 hours
1. **Refresh Token**: Expires after 1 year
1. **Old Refresh Token**: Expires either:
   - 8 hours after a new refresh token is issued, OR
   - Immediately once the new access token is used

## Implementation Flow

```mermaid
flowchart TD
    A[API Call Needed] --> B{Access Token Valid?}

    B -->|Yes| C[Make API Call]
    B -->|No| D[Refresh Token Flow]

    D --> E{Refresh Token Valid?}
    E -->|Yes| F{Old Refresh Token Valid?}
    E -->|No| G[Mark Unhealthy & Require Re-auth]

    F -->|Yes| H[Use Old Refresh Token]
    F -->|No| I[Use Current Refresh Token]

    H --> J[Get New Tokens]
    I --> J

    J --> K[Store New Tokens]
    K --> L[Store Old Token with 8h Expiry]
    K --> M[Make API Call with New Token]

    M --> N[Expire Old Refresh Token]
    N --> O[Log & Track Metrics]
```
