{"author": "Unnamed", "title": "ParticipantDB", "date": "2024-06-23T19:20:43.383Z", "tables": [{"id": 0, "name": "program", "x": -135.96909353905585, "y": -646.4826191027022, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "tile", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": 255}, {"name": "created_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}, {"name": "updated_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718553078123}, {"id": 1, "name": "program_module", "x": 143.33777527616485, "y": -523.7336431357085, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "title", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": 255}, {"name": "program_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}, {"name": "created_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": "", "values": []}, {"name": "updated_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 4, "size": "", "values": []}, {"name": "short_title", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 5, "size": 255}, {"name": "started_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 6, "size": "", "values": []}, {"name": "ended_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 7, "size": "", "values": []}, {"name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 8, "size": 65535}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718553096681}, {"id": 2, "name": "program_module_section", "x": 390.32132013697446, "y": -363.23924553238953, "fields": [{"name": "id", "type": "INT", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": true, "comment": "", "id": 0}, {"name": "title", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": 255}, {"name": "program_module_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}, {"name": "activity_type", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": "", "values": []}, {"name": "description", "type": "TEXT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 4, "size": 65535}, {"name": "metadata", "type": "JSON", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 5, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718553104767}, {"id": 3, "name": "cohort", "x": 712.7544652733511, "y": -1351.6017131911035, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "program_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": 255}, {"name": "started_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": "", "values": []}, {"name": "limit", "type": "INT", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 4}, {"name": "created_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 5, "size": "", "values": []}, {"name": "updated_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 6, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718609813669}, {"id": 4, "name": "participant", "x": 876.2705556264139, "y": -833.4187685012607, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "created_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "updated_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}, {"name": "email", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": 255}, {"name": "first_name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 4, "size": 255}, {"name": "last_name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 5, "size": 255}, {"name": "status", "type": "ENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 6, "values": ["active", "pending", "inactive"]}, {"name": "cognito_sub", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 7, "size": "", "values": []}, {"name": "medical_record", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 8, "size": 255}, {"name": "is_test", "type": "BOOLEAN", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 9, "size": "", "values": []}, {"name": "last_reset", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 10, "size": "", "values": []}, {"name": "metadata", "type": "JSON", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 11, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718609877715}, {"id": 5, "name": "cohort_members", "x": 1234.************, "y": -1187.2881723530068, "fields": [{"name": "cohort_id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "particiapnt_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "created_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}, {"name": "updated_at", "type": "DATETIME", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718609893180}, {"id": 6, "name": "live_calls", "x": 1227.7348179856597, "y": -1383.3539701426275, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "cohort_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718610026037}, {"id": 7, "name": "solera_participant", "x": 1279.8952463916798, "y": -736.0521775171903, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "participant_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "solera_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}, {"name": "solera_key", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "size": 255}, {"name": "solera_program_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 4, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718610115082}, {"id": 8, "name": "participant_activity", "x": 2029.0040976789512, "y": 33.2376359390895, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "participant_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "value", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": 255}, {"name": "unit", "type": "ENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 3, "values": ["kg", "lb"]}, {"name": "activity_device", "type": "ENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 4, "values": ["withings", "manual"]}, {"name": "created_at", "type": "DATETIME", "default": "", "check": "", "primary": true, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 5, "size": "", "values": []}, {"name": "activity_category", "type": "ENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 6, "values": []}, {"name": "activity_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 7, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718610279140}, {"id": 9, "name": "solera_activity", "x": 2172.6815886585373, "y": -220.0605071880488, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "activity_type_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718610395597}, {"id": 10, "name": "activity_types", "x": 1703.************, "y": -184.37651408034924, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "title", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": 255}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718610446604}, {"id": 11, "name": "heads_up_participant", "x": 1209.9408932271876, "y": -477.4892965221412, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "participant_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "heads_up_token", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": 255}], "comment": "", "indices": [], "color": "#175e7a", "key": 1718629881382}, {"id": 12, "name": "participant_meta", "x": 1762.************, "y": -904.5205438312178, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": 0, "size": "", "values": []}, {"name": "metadata", "type": "JSON", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 1, "size": "", "values": []}, {"name": "paticipant_id", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "id": 2, "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a", "key": 1719162937828}], "relationships": [{"startTableId": 1, "startFieldId": 2, "endTableId": 0, "endFieldId": 0, "cardinality": "Many to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "program_module_program_id_fk", "id": 0}, {"startTableId": 2, "startFieldId": 2, "endTableId": 1, "endFieldId": 0, "cardinality": "Many to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "program_module_section_program_module_id_fk", "id": 1}, {"startTableId": 3, "startFieldId": 1, "endTableId": 0, "endFieldId": 0, "cardinality": "Many to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "cohort_program_id_fk", "id": 2}, {"startTableId": 5, "startFieldId": 0, "endTableId": 3, "endFieldId": 0, "cardinality": "One to many", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "cohort_members_cohort_id_fk", "id": 3}, {"startTableId": 4, "startFieldId": 0, "endTableId": 5, "endFieldId": 1, "cardinality": "Many to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "participant_id_fk", "id": 4}, {"startTableId": 3, "startFieldId": 0, "endTableId": 6, "endFieldId": 1, "cardinality": "One to many", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "cohort_id_fk", "id": 5}, {"startTableId": 7, "startFieldId": 1, "endTableId": 4, "endFieldId": 0, "cardinality": "One to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "participant_id_fk", "id": 6}, {"startTableId": 8, "startFieldId": 1, "endTableId": 4, "endFieldId": 0, "cardinality": "One to many", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "participant_id_fk", "id": 7}, {"startTableId": 10, "startFieldId": 0, "endTableId": 9, "endFieldId": 1, "cardinality": "One to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "activity_types_id_fk", "id": 8}, {"startTableId": 2, "startFieldId": 3, "endTableId": 10, "endFieldId": 0, "cardinality": "One to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "program_module_section_activity_type_fk", "id": 9}, {"startTableId": 11, "startFieldId": 1, "endTableId": 4, "endFieldId": 0, "cardinality": "One to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "heads_up_participant_participant_id_fk", "id": 10}, {"startTableId": 8, "startFieldId": 7, "endTableId": 10, "endFieldId": 0, "cardinality": "One to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "participant_activity_activity_id_fk", "id": 11}, {"startTableId": 12, "startFieldId": 2, "endTableId": 4, "endFieldId": 0, "cardinality": "One to one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "participant_meta_paticipant_id_fk", "id": 12}], "notes": [], "subjectAreas": [{"id": 0, "name": "Program ", "x": -202.**************, "y": -735.2903009660652, "width": 902.6350410132945, "height": 660.*************, "color": "#175e7a"}, {"id": 1, "name": "Cohort", "x": 688.0062839621705, "y": -1458.*************, "width": 842.6496917167597, "height": 527.2630873629494, "color": "#175e7a"}, {"id": 2, "name": "Participant", "x": 836.6914513730159, "y": -908.5815627520235, "width": 1220.6490381458316, "height": 657.2051428518, "color": "#175e7a"}, {"id": 3, "name": "Activity", "x": 1507.6241529455142, "y": -244.60977295685973, "width": 961.8481928239728, "height": 656.9952011464648, "color": "#175e7a"}], "types": []}