# Fitbit Token Flow Diagram

This diagram illustrates the Fitbit token lifecycle and refresh flow.

## Token Lifecycle Diagram

```mermaid
graph TD
    A[Initial Authentication] -->|Get Initial Tokens| B[Store Access & Refresh Tokens]
    B --> C[Use Access Token for API Calls]

    C -->|Access Token Expires| D{Refresh Token Valid?}

    D -->|Yes| E[Refresh Tokens]
    D -->|No| F[Require Re-authentication]

    E -->|Get New Tokens| G[Store New Tokens]
    G -->|Store Old Refresh Token as Backup| H[Old Refresh Token]
    G --> I[Use New Access Token]

    I --> J[Make API Calls]

    J -->|Success| K[Log & Track Success Metrics]
    J -->|Failure| L[Log & Track Error Metrics]
```

## Token Expiration Timeline

```mermaid
gantt
    title Fitbit Token Expiration Timeline
    dateFormat  YYYY-MM-DD

    section Access Token
    Valid                      :a1, 2023-01-01, 8h
    Expired                    :a2, after a1, 1y

    section Refresh Token
    Valid                      :r1, 2023-01-01, 1y

    section Events
    Initial Auth               :milestone, m1, 2023-01-01, 0d
    Token Refresh              :milestone, m2, 2023-01-01, 0d
```

## Key Points

1. **Access Token**: Expires after 8 hours (28800 seconds)
1. **Refresh Token**: No explicit expiration, but should be refreshed regularly
1. **Old Refresh Token**: Stored as backup but not actively managed

## Implementation Flow

```mermaid
flowchart TD
    A[API Call Needed] --> B{Access Token Valid?}

    B -->|Yes| C[Make API Call]
    B -->|No| D[Refresh Token Flow]

    D --> E{Refresh Token Valid?}
    E -->|Yes| F[Use Refresh Token]
    E -->|No| G[Mark Unhealthy & Require Re-auth]

    F --> H[Get New Tokens]

    H --> I[Store New Tokens]
    I --> J[Store Old Token as Backup]
    I --> K[Make API Call with New Token]

    K -->|Success| L[Log & Track Success Metrics]
    K -->|Failure| M[Log & Track Error Metrics]
```

## Comparison with Withings

```mermaid
graph TB
    subgraph Fitbit
    F1[Access Token: 8 hours]
    F2[Refresh Token: No explicit expiration]
    F3[Old Token: Stored as backup]
    F4[Simple token refresh flow]
    end

    subgraph Withings
    W1[Access Token: 3 hours]
    W2[Refresh Token: 1 year]
    W3[Old Token: Expires after 8h or when new token used]
    W4[Complex token refresh flow]
    end

    F1 --- W1
    F2 --- W2
    F3 --- W3
    F4 --- W4
```
