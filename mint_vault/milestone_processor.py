import pendulum

# Assume SoleraParticipantActivityEnum and SoleraActivityPayload are defined elsewhere
# For example:
# from solera_enums import SoleraParticipantActivityEnum, SoleraActivityPayload
from ciba_participant.activity.models import SoleraParticipantActivityEnum


class MilestoneProcessor:
    # Allowed ME types as per your specification.
    ALLOWED_ME_TYPES = [
        SoleraParticipantActivityEnum.QUIZ,
        SoleraParticipantActivityEnum.ARTICLE,
        SoleraParticipantActivityEnum.RECIPES,
        SoleraParticipantActivityEnum.MEALS,
        SoleraParticipantActivityEnum.ACTIVITY,
        SoleraParticipantActivityEnum.PLAY,
        SoleraParticipantActivityEnum.WEIGHT,
        SoleraParticipantActivityEnum.COACH,
    ]

    def __init__(
        self, enrollment_date: pendulum.DateTime, program_start: pendulum.DateTime
    ):
        self.enrollment_date = enrollment_date
        self.program_start = program_start
        # Stubs for weight loss processing
        self.first_non_self_reported_weight_date = None
        self.first_non_self_reported_weight_value = None
        self.weight_activities = []

    def parse_dt(self, dt_str: str) -> pendulum.DateTime:
        return pendulum.parse(dt_str)

    def extract_activity_types(self, activity: dict) -> set:
        data = activity.get("data", {})
        processed_keys = {k.lower() for k in data.keys()}
        recognized = set()
        for enum_item in SoleraParticipantActivityEnum:
            if enum_item.value.lower() in processed_keys:
                recognized.add(enum_item)
        return recognized

    def is_meaningful_engagement(self, activity: dict) -> bool:
        types = self.extract_activity_types(activity)
        return bool(types.intersection(set(self.ALLOWED_ME_TYPES)))

    def filter_corrections(self, activities: list) -> list:
        return [act for act in activities if not act.get("correction", False)]

    def link_activities_to_days(self, activities: list) -> dict:
        days = {}
        for act in activities:
            day = self.parse_dt(act["timestamp"]).isoformat()
            days.setdefault(day, []).append(act)
        return days

    def expand_to_weeks(
        self, activities_to_days: dict, start_date: pendulum.DateTime
    ) -> dict:
        weeks = {}
        for day_str, acts in activities_to_days.items():
            dt = pendulum.parse(day_str)
            week_index = (dt - start_date).days // 7 + 1
            weeks.setdefault(week_index, []).extend(acts)
        return weeks

    # New method for HWM: compute rolling (sliding) ME sessions within a given period.
    def compute_hwm_sessions_sliding(
        self,
        activities: list,
        period_start: pendulum.DateTime,
        period_end: pendulum.DateTime,
    ) -> int:
        # Filter activities to those that qualify as a meaningful engagement.
        filtered = [
            act
            for act in activities
            if self.is_meaningful_engagement(act)
            and period_start <= self.parse_dt(act["timestamp"]) < period_end
        ]
        # Sort by timestamp.
        sorted_acts = sorted(filtered, key=lambda x: self.parse_dt(x["timestamp"]))
        sessions = 0
        current_date = period_start
        # Use a rolling window: if any ME occurs within a 7-day window starting at current_date,
        # count one session and jump ahead by 7 days; otherwise, shift the window by 1 day.
        while current_date <= period_end:
            window_end = current_date.add(days=7)
            window = [
                act
                for act in sorted_acts
                if current_date <= self.parse_dt(act["timestamp"]) < window_end
            ]
            if window:
                sessions += 1
                current_date = current_date.add(days=7)
            else:
                current_date = current_date.add(days=1)
        return sessions

    # Count Meaningful Engagements in the specified range starting exactly at start_date.
    def completed_MEs_in_range(
        self,
        activities_to_weeks_map: dict,
        start_date: pendulum.DateTime,
        end_date: pendulum.DateTime,
    ) -> int:
        me_count = 0
        # Instead of sessions, we count all activities that qualify as ME.
        for week_idx, acts in activities_to_weeks_map.items():
            # Determine the week’s start date based on program start.
            week_start = self.program_start.add(days=(week_idx - 1) * 7)
            # Only count weeks fully or partially in the given range.
            if start_date <= week_start <= end_date:
                for act in acts:
                    if self.is_meaningful_engagement(act):
                        me_count += 1
        return me_count

    def has_5_percent_weight_loss_since(
        self, start_date: pendulum.DateTime, end_date: pendulum.DateTime
    ) -> bool:
        baseline_date = self.first_non_self_reported_weight_date
        baseline_weight = self.first_non_self_reported_weight_value
        min_weight = None
        for activity in self.weight_activities:
            activity_date = self.parse_dt(activity.get("timestamp"))
            if not start_date <= activity_date <= end_date:
                continue
            if activity_date >= baseline_date:
                try:
                    weight = float(activity["data"].get("weight"))
                except (TypeError, ValueError):
                    continue
                if min_weight is None or weight < min_weight:
                    min_weight = weight
        if baseline_weight is None or min_weight is None:
            return False
        weight_loss_percentage = (
            (baseline_weight - min_weight) / baseline_weight
        ) * 100
        return weight_loss_percentage >= 5

    @property
    def date_achieved_5_percent_loss(self):
        return None  # Stub: implement as needed

    @property
    def M4_date_achieved(self):
        return None  # Stub: implement as needed

    def maintains_5_percent_loss(self, start_date, end_date):
        return False  # Stub: implement as needed

    @property
    def milestone_6_achieved_date(self):
        return None  # Stub

    @property
    def milestone_7b_achieved_date(self):
        return None  # Stub

    @property
    def milestone_8b_achieved_date(self):
        return None  # Stub

    def total_MEs_by_date(self, cutoff_date: pendulum.DateTime) -> int:
        return 0  # Stub

    @property
    def is_enrolled_covered_individual(self) -> bool:
        return True

    @property
    def enrolled_in_program(self) -> bool:
        return True

    @property
    def attended_first_session(self) -> bool:
        return False

    @property
    def downloaded_app(self) -> bool:
        return True

    @property
    def completed_registration(self) -> bool:
        return True

    # Helper functions for missing requirements
    def missing_MEs(self, target: int, actual: int) -> dict:
        missing_count = target - actual
        if missing_count > 0:
            return {
                "missing_count": missing_count,
                "allowed_types": [str(t) for t in self.ALLOWED_ME_TYPES],
            }
        return {}

    # ------------------
    # NDPP Milestones
    def ndpp_milestones(self, activities_to_weeks_map: dict) -> dict:
        ps = self.program_start

        milestone_dates = {
            "M1": {"start_date": ps.to_date_string(), "end_date": ps.to_date_string()},
            "M2": {
                "start_date": ps.to_date_string(),
                "end_date": ps.add(weeks=4).to_date_string(),
            },
            "M3": {
                "start_date": ps.add(weeks=4).to_date_string(),
                "end_date": ps.add(weeks=9).to_date_string(),
            },
            "M4": {
                "start_date": ps.add(weeks=9).to_date_string(),
                "end_date": ps.add(weeks=52).to_date_string(),
            },
        }

        # M1: Enrollment as a covered individual.
        M1_achieved = self.is_enrolled_covered_individual
        M1_remaining = [] if M1_achieved else ["enrollment"]

        # M2: Require >=2 Meaningful Engagements within first 4 weeks.
        MEs_4w = self.completed_MEs_in_range(
            activities_to_weeks_map, ps, ps.add(weeks=4)
        )
        M2_achieved = MEs_4w >= 2
        M2_remaining = [] if M2_achieved else [self.missing_MEs(2, MEs_4w)]

        # M3: Require >=2 additional MEs between weeks 5–9.
        MEs_5_9 = self.completed_MEs_in_range(
            activities_to_weeks_map, ps.add(weeks=4), ps.add(weeks=9)
        )
        M3_achieved = MEs_5_9 >= 2
        M3_remaining = [] if M3_achieved else [self.missing_MEs(2, MEs_5_9)]

        # M4: Achieve a 5% weight loss from baseline within weeks 9–52.
        weight_loss = self.has_5_percent_weight_loss_since(
            start_date=ps.add(weeks=9), end_date=ps.add(weeks=52)
        )
        M4_achieved = M1_achieved and weight_loss
        M4_remaining = [] if M4_achieved else ["weight_loss"]

        return {
            "M1": {
                **milestone_dates["M1"],
                "achieved": M1_achieved,
                "remaining": M1_remaining,
            },
            "M2": {
                **milestone_dates["M2"],
                "achieved": M2_achieved,
                "remaining": M2_remaining,
            },
            "M3": {
                **milestone_dates["M3"],
                "achieved": M3_achieved,
                "remaining": M3_remaining,
            },
            "M4": {
                **milestone_dates["M4"],
                "achieved": M4_achieved,
                "remaining": M4_remaining,
            },
        }

    # ------------------
    # IBC/WM Milestones
    def ibc_milestones(self, activities_to_weeks_map: dict) -> dict:
        ps = self.program_start

        milestone_dates = {
            "M1": {"start_date": ps.to_date_string(), "end_date": ps.to_date_string()},
            "M2": {
                "start_date": ps.to_date_string(),
                "end_date": ps.add(weeks=4).to_date_string(),
            },
            "M3": {
                "start_date": ps.to_date_string(),
                "end_date": ps.add(weeks=9).to_date_string(),
            },
            "M4": {
                "start_date": ps.add(weeks=5).to_date_string(),
                "end_date": ps.add(weeks=104).to_date_string(),
            },
        }

        # M1: Enrollment / attendance at first session or registration.
        M1_achieved = self.is_enrolled_covered_individual and (
            (self.enrolled_in_program and self.attended_first_session)
            or (self.downloaded_app and self.completed_registration)
        )
        M1_remaining = [] if M1_achieved else ["enrollment/first_session"]

        # M2: Require M1 & >=2 Meaningful Engagements within first 4 weeks.
        MEs_4w = self.completed_MEs_in_range(
            activities_to_weeks_map, ps, ps.add(weeks=4)
        )
        M2_achieved = M1_achieved and (MEs_4w >= 2)
        M2_remaining = [] if M2_achieved else [self.missing_MEs(2, MEs_4w)]

        # M3: Require M1 & a total of >=4 Meaningful Engagements by week 9.
        MEs_9w = self.completed_MEs_in_range(
            activities_to_weeks_map, ps, ps.add(weeks=9)
        )
        M3_achieved = M1_achieved and (MEs_9w >= 4)
        M3_remaining = [] if M3_achieved else [self.missing_MEs(4, MEs_9w)]

        # M4: Require (M2 or M3 achieved) and 5% weight loss between weeks 5–104.
        weight_loss = self.has_5_percent_weight_loss_since(
            start_date=ps.add(weeks=5), end_date=ps.add(weeks=104)
        )
        M4_achieved = M1_achieved and (M2_achieved or M3_achieved) and weight_loss
        M4_remaining = [] if M4_achieved else ["weight_loss"]

        return {
            "M1": {
                **milestone_dates["M1"],
                "achieved": M1_achieved,
                "remaining": M1_remaining,
            },
            "M2": {
                **milestone_dates["M2"],
                "achieved": M2_achieved,
                "remaining": M2_remaining,
            },
            "M3": {
                **milestone_dates["M3"],
                "achieved": M3_achieved,
                "remaining": M3_remaining,
            },
            "M4": {
                **milestone_dates["M4"],
                "achieved": M4_achieved,
                "remaining": M4_remaining,
            },
        }

    # ------------------
    # HWM Milestones using sliding window for ME weeks.
    def hwm_milestones(
        self, activities_to_weeks_map: dict, all_activities: list
    ) -> dict:
        ps = self.program_start

        milestone_dates = {
            "M1": {"start_date": ps.to_date_string(), "end_date": ps.to_date_string()},
            "M2": {
                "start_date": ps.to_date_string(),
                "end_date": ps.add(weeks=4).to_date_string(),
            },
            "M3": {
                "start_date": ps.add(weeks=4).to_date_string(),
                "end_date": ps.add(weeks=9).to_date_string(),
            },
            "M4": {
                "start_date": ps.to_date_string(),
                "end_date": ps.add(days=365).to_date_string(),
            },
        }

        # M1: Complete enrollment process including app download.
        M1_achieved = self.is_enrolled_covered_individual and (
            self.downloaded_app and self.completed_registration
        )
        M1_remaining = [] if M1_achieved else ["enrollment/app download"]

        # For HWM, use sliding window rule on all activities (filtering by ME).
        # M2: Require >=2 ME sessions within first 4 weeks.
        sessions_4w = self.compute_hwm_sessions_sliding(
            all_activities, ps, ps.add(weeks=4)
        )
        M2_achieved = sessions_4w >= 2
        M2_remaining = (
            [] if M2_achieved else [f"Need {2 - sessions_4w} additional ME session(s)"]
        )

        # M3: Require >=2 additional ME sessions between weeks 5 and 9.
        sessions_5_9 = self.compute_hwm_sessions_sliding(
            all_activities, ps.add(weeks=4), ps.add(weeks=9)
        )
        M3_achieved = sessions_5_9 >= 2
        M3_remaining = (
            [] if M3_achieved else [f"Need {2 - sessions_5_9} additional ME session(s)"]
        )

        # M4: Achieve 5% weight loss on days 1 to 365.
        weight_loss = self.has_5_percent_weight_loss_since(
            start_date=ps, end_date=ps.add(days=365)
        )
        M4_achieved = M1_achieved and weight_loss
        M4_remaining = [] if M4_achieved else ["weight_loss"]

        return {
            "M1": {
                **milestone_dates["M1"],
                "achieved": M1_achieved,
                "remaining": M1_remaining,
            },
            "M2": {
                **milestone_dates["M2"],
                "achieved": M2_achieved,
                "remaining": M2_remaining,
            },
            "M3": {
                **milestone_dates["M3"],
                "achieved": M3_achieved,
                "remaining": M3_remaining,
            },
            "M4": {
                **milestone_dates["M4"],
                "achieved": M4_achieved,
                "remaining": M4_remaining,
            },
        }

    def calculate_participant_milestone(
        self,
        participant_activities: list,
        participant_cohort_start_date: pendulum.DateTime,
        participant_enrollment_date: pendulum.DateTime,
        participant_program: str,
    ):
        # Determine program start date based on the first valid (non-enrollment) activity.
        # If the first valid activity (weight tracking or other non-enrollment) occurs on the same day as enrollment,
        # then program start = enrollment date; otherwise, use the date of the first valid activity.
        non_enrollment_activities = [
            el
            for el in participant_activities
            if el.get("data") and "enrollment" not in el.get("data")
        ]
        if non_enrollment_activities:
            first_valid_activity = sorted(
                non_enrollment_activities, key=lambda x: self.parse_dt(x["timestamp"])
            )[0]
            first_valid_date = self.parse_dt(first_valid_activity["timestamp"])
            if first_valid_date != participant_enrollment_date:
                self.program_start = pendulum.instance(first_valid_date)
            else:
                self.program_start = pendulum.instance(participant_enrollment_date)
        else:
            self.program_start = pendulum.instance(participant_enrollment_date)

        # Process weight activities.
        weight_activities = []
        for el in participant_activities:
            activity_data = el.get("data")
            if not activity_data:
                continue
            if "enrollment" in activity_data:
                # Enrollment activity may update enrollment date if needed.
                pass
            if "weight" in activity_data:
                weight_activities.append(el)

        if weight_activities:
            first_weight = sorted(weight_activities, key=lambda x: x["timestamp"])[0]
            self.first_non_self_reported_weight_date = self.parse_dt(
                first_weight["timestamp"]
            )
            try:
                self.first_non_self_reported_weight_value = float(
                    first_weight["data"]["weight"]
                )
            except (TypeError, ValueError):
                self.first_non_self_reported_weight_value = None
        self.weight_activities = weight_activities

        cleaned_activities = self.filter_corrections(participant_activities)
        activities_to_days_map = self.link_activities_to_days(cleaned_activities)
        activities_to_weeks_map = self.expand_to_weeks(
            activities_to_days_map, self.program_start
        )

        if participant_program == "NDPP":
            milestone_status = self.ndpp_milestones(activities_to_weeks_map)
        elif participant_program == "IBC":
            milestone_status = self.ibc_milestones(activities_to_weeks_map)
        elif participant_program == "HWM":
            milestone_status = self.hwm_milestones(
                activities_to_weeks_map, cleaned_activities
            )
        else:
            raise ValueError(f"Unknown participant program: {participant_program}")

        print(f"{participant_program} Milestone Status:", milestone_status)
        return milestone_status


# # Example usage:
# if __name__ == "__main__":
#     # TODO: Retrieve participant activities from the database and process them
#     path_to_json = (
#         "/Users/<USER>/dev/ciba/ciba-MintVault/mint_vault/mint_vault_resp.json"
#     )
#     program_start = "2025-03-05T00:00:00+00"

#     with open(path_to_json, "r") as f:
#         data = json.load(f)

#     solera_activities = data["body"]["solera_activities"]
#     participant_program = data["body"]["user_data"]["programId"]
#     enrollment_date = program_start
#     mp = MilestoneProcessor(
#         pendulum.parse(enrollment_date), pendulum.parse(program_start)
#     )
#     result = mp.calculate_participant_milestone(
#         solera_activities,
#         pendulum.parse(program_start),
#         pendulum.parse(enrollment_date),
#         participant_program,
#     )
