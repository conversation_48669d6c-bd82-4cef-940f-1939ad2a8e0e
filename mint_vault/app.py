"""Function that calculates solera milestones and returns actual solera status

This lambda handler represents the main entry point for handling events received
from Participant ecosystem, processing them and forwarding to Solera API.



Example:
    Running locally
    if __name__ == "__main__":
        def mock_lambda_context():
        ""Mock Lambda Context for local testing""
            context = Mock()
            context.function_name = "test_function"
            context.memory_limit_in_mb = 128
            context.invoked_function_arn = (
                "arn:aws:lambda:us-east-1:123456789012:function:test_function"
            )
            context.aws_request_id = "test-request-id"
            return context

        test_event = {}  # Add a test event here
        asyncio.run(lambda_handler(test_event, mock_lambda_context()))
"""

from io import StringIO

import asyncio
import json
import os
from pathlib import Path
from typing import Any, Dict, List, Union
import pandas as pd
import boto3
import pendulum
from aws_lambda_powertools import Logger
from aws_lambda_powertools.event_handler import (
    APIGatewayRestResolver,
    Response,
    content_types,
)
from aws_lambda_powertools.event_handler.exceptions import (
    BadRequestError,
    InternalServerError,
    NotFoundError,
    ServiceError,
    UnauthorizedError,
)
from aws_lambda_powertools.logging import correlation_paths
from aws_lambda_powertools.utilities.parser import ValidationError
from aws_lambda_powertools.utilities.typing import LambdaContext
from ciba_participant.common.aws_handler import (
    get_dynamodb_table,
    publish_to_sns,
    put_csv_to_s3,
    get_s3_client,
)
from ciba_participant.common.db import close_db, init_db
from ciba_participant.notifications.models import SlackNotification
from ciba_participant.participant.models import Participant, ParticipantStatus
from ciba_participant.settings import get_settings, ENV
from ciba_participant.solera.api import SoleraActivitiesList, SoleraAPIAsyncClient
from data_models import (
    SoleraActivityRequest,
    CibaActivityRequest,
    ActivityStatusRequest,
    CatchupRequest,
    EnrolledRequest,
    LambdaResponse,
    MilestonesRequest,
    ParticipantRequest,
)
from solera import (
    SoleraParticipant,
    get_activities_for_date_range,
    get_participant_status,
)
from ciba_participant.participant.service import ParticipantService

SERVICE_NAME = "MintVault"
logger = Logger(service=SERVICE_NAME)

SETTINGS = None
DB_INITIALIZED = False
REGION = os.environ.get("AWS_REGION") or "us-east-2"
sns_client = boto3.client("sns", region_name=REGION)

state_table_name = f"{os.environ.get('DYNAMO_DB_TABLE')}"
AWS_BUCKET_NAME = f"{os.environ.get('AWS_BUCKET_NAME')}"
state_table = get_dynamodb_table(
    region_name=REGION,
    table_name=state_table_name,
)


def send_slack_notification(source: str, details: str):
    """
    Send slack notification in case if lambda fails
    """
    slc = SlackNotification(
        environment=SETTINGS.ENV,
        is_test=bool(SETTINGS.DEBUG),
        source=source,
    )

    slc.title = "Error in MintVault"
    slc.details = f"{details}"
    slc.type = "REST API"

    publish_to_sns(
        sns_client=sns_client,
        sns_topic_arn=SETTINGS.SLACK_SNS_TOPIC_ARN,
        message=slc.model_dump_json(),
    )


async def ensure_db_initialized():
    """
    Ensure the database is initialized
    to correctly handle aws db connection
    :return:
    """
    global DB_INITIALIZED  # pylint: disable=global-statement
    if not DB_INITIALIZED:
        await init_db()
        DB_INITIALIZED = True


app = APIGatewayRestResolver(enable_validation=True)
app.enable_swagger(
    title="MintVault API",
    description="""
    API used to retrieve participant activity data both from Solera and Ciba.
    Also it is able to count how user follows milestones and how much of them are completed.
    This might help to understand how user is engaged in the program and how money this participant already earned.
    """,
)


@app.get(rule="/bad-request-error")
def bad_request_error():
    """
    Bad request error
    :return:
    """
    error_message = "Missing required parameter"
    send_slack_notification("bad_request_error", error_message)
    raise BadRequestError(error_message)  # HTTP  400


@app.get(rule="/unauthorized-error")
def unauthorized_error():
    """
    Unauthorized error
    :return:
    """
    error_message = "Unauthorized"
    send_slack_notification("unauthorized_error", error_message)
    raise UnauthorizedError(error_message)  # HTTP 401


@app.get(rule="/not-found-error")
def not_found_error():
    """
    Not found error
    :return:
    """
    send_slack_notification("not_found_error", "Not Found")
    raise NotFoundError  # HTTP 404


@app.get(rule="/internal-server-error")
def internal_server_error():
    """
    Internal server error
    :return:
    """
    raise InternalServerError("Internal server error")  # HTTP 500


@app.get(rule="/service-error", cors=True)
def service_error():
    """
    Service error
    :return:
    """
    raise ServiceError(502, "Something went wrong!")


@app.not_found
def handle_not_found_errors() -> Response:
    """
    Handle not found errors
    :param exc:
    :return:
    """
    route = app.current_event.path
    logger.info(f"Not found route: {route}")
    send_slack_notification("not_found_error", f"Not Found: {route}")

    return Response(
        status_code=418, content_type=content_types.TEXT_PLAIN, body="I'm a teapot!"
    )


@app.post("/participant")
def participant(participant_request: ParticipantRequest) -> LambdaResponse:
    """Participant endpoint

    We retrieve participant activity status both from solera and ciba

    :return:
    """
    try:
        # Parse the incoming event body into the ParticipantRequest model

        # Initialize the SoleraAPIAsyncClient
        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )

        # Run async tasks in a blocking manner using asyncio.run or loop.run_until_complete
        loop = asyncio.get_event_loop()
        loop.run_until_complete(client.authenticate())
        loop.run_until_complete(init_db())

        result = loop.run_until_complete(
            get_participant_status(client=client, **participant_request.dict())
        )
        loop.run_until_complete(close_db())
        status = "warning" if result.error else "success"

        return LambdaResponse(status=status, body=result)
    except ValidationError as e:
        send_slack_notification("participant", f"validation_error: {str(e)}")
        return LambdaResponse(status="validation_error", body={"message": str(e)})
    except Exception as e:
        send_slack_notification("participant", f"unexpected_error: {str(e)}")
        return LambdaResponse(status="unexpected_error", body={"message": str(e)})


@app.post("/participant/activity")
def create_participant_activity(participant_activity: CibaActivityRequest):
    """Create participant activity

    Endpoint used to create participant activity in solera manually

    :return:
    """
    loop = asyncio.get_event_loop()
    try:
        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )
        loop.run_until_complete(client.authenticate())
        loop.run_until_complete(init_db())
        sp = SoleraParticipant(participant_id=participant_activity.participant_id)
        loop.run_until_complete(sp.get_ciba_participant())
        saved_activity = loop.run_until_complete(
            sp.save_activity(ciba_activity=participant_activity)
        )
        solera_activity = loop.run_until_complete(
            sp.convert_ciba_to_solera_activity(ciba_activity=saved_activity)
        )

        response = loop.run_until_complete(
            SoleraParticipant.submit_activities(
                solera_client=client,
                activities=SoleraActivitiesList(activities=[solera_activity]),
            )
        )

        return {"status": "success", "message": "Activity processed", "body": response}
    except ValidationError as e:
        send_slack_notification(
            "create_participant_activity", f"validation_error: {str(e)}"
        )
        return {"status": "error", "message": str(e)}, 400


@app.post("/participant/activity/catchup")
def catchup_participant_activity(catchup_request: CatchupRequest):
    """Create participant activity

    Endpoint used to create participant activity in solera manually

    :return:
    """
    resp = {"status": "warning", "message": ""}
    loop = asyncio.get_event_loop()
    try:
        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )
        loop.run_until_complete(client.authenticate())
        loop.run_until_complete(init_db())

        result = loop.run_until_complete(
            get_activities_for_date_range(
                client=client,
                start_date=catchup_request.start_date,
                end_date=catchup_request.end_date,
            )
        )
        if not result["processed_data"].activities:
            logger.info(
                f"No activities found for period"
                f" {catchup_request.start_date} - {catchup_request.end_date}"
            )
            resp["message"] = (
                f"No activities found for period"
                f" {catchup_request.start_date} - {catchup_request.end_date}"
            )
            return resp

        data = result["processed_data"].model_dump()
        s3_file_path = loop.run_until_complete(save_data_to_s3(data=data["activities"]))
        logger.info(
            f"""Activities for
            {catchup_request.start_date} - {catchup_request.end_date}
            submitted to solera
            """
        )
        solera_resp = loop.run_until_complete(
            SoleraParticipant.submit_activities(
                solera_client=client, activities=result["processed_data"]
            )
        )
        logger.info(f"Solera response: {solera_resp}")
        timestamp = pendulum.instance(catchup_request.end_date).int_timestamp

        state_table.put_item(
            Item={
                "RequestId": solera_resp.requestId,
                "CreatedAt": timestamp,
                "filePath": s3_file_path,
            }
        )
        resp["status"] = "success"
        resp["message"] = f"""
            {len(data["activities"])} Activities for
            {catchup_request.start_date} - {catchup_request.end_date}
            submitted to solera
            """
        resp.update(solera_resp.model_dump())
        resp.update(catchup_request.model_dump())
        return resp
    except ValidationError as e:
        send_slack_notification(
            "catchup_participant_activity", f"validation_error: {str(e)}"
        )
        return {"status": "error", "message": str(e)}, 400
    finally:
        loop.run_until_complete(close_db())


@app.post("/participant/activity/correct")
def correct_participant_activity(participant_activity: SoleraActivityRequest):
    """Create participant activity

    Endpoint used to create participant activity in solera manually

    To make corrections to activity data already accepted follow this procedure.
    Submit the correct data under a new referenceId
    AND submit a Correction activity to mark the invalid data as entered-in-error.
    The Correction activity must have the referenceId set equal to the referenceId
    of the item that needs to be corrected.

    :return:
    """
    # try:
    #     client = SoleraAPIAsyncClient(
    #         client_id=SETTINGS.SOLERA_CLIENT_ID,
    #         client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
    #         environment=SETTINGS.ENV,
    #     )
    #     loop = asyncio.get_event_loop()
    #     loop.run_until_complete(client.authenticate())
    #     loop.run_until_complete(init_db())
    #     return {"status": "success", "message": "Activity processed"}
    # except ValidationError as e:
    #     send_slack_notification(
    #         "correct_participant_activity", f"validation_error: {str(e)}"
    #     )
    #     return {"status": "error", "message": str(e)}, 400
    return {"status": "empty", "message": "Not implemented"}, 501


@app.post("/activity/status")
def get_activity_status(activity_request: ActivityStatusRequest) -> LambdaResponse:
    """Participant enrolled endpoint

    Endpoint used to get the enrolled participants


    :return:
    """
    loop = asyncio.get_event_loop()
    try:
        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )
        loop.run_until_complete(client.authenticate())
        loop.run_until_complete(init_db())

        result = loop.run_until_complete(
            client.get_activity_status(request_id=activity_request.request_id)
        )

        return LambdaResponse(status="success", body=result)
    except ValidationError as e:
        send_slack_notification("get_activity_status", f"validation_error: {str(e)}")
        return LambdaResponse(status="validation_error", body={"message": str(e)})
    except Exception as e:
        send_slack_notification("get_activity_status", f"unexpected_error: {str(e)}")
        return LambdaResponse(status="unexpected_error", body={"message": str(e)})
    finally:
        loop.run_until_complete(close_db())


@app.post("/enrolled")
def get_enrolled(enrolled_request: EnrolledRequest) -> LambdaResponse:
    """Participant enrolled endpoint

    Endpoint used to get the enrolled participants


    :return:
    """
    loop = asyncio.get_event_loop()
    try:
        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )
        loop.run_until_complete(client.authenticate())
        loop.run_until_complete(init_db())
        result = loop.run_until_complete(
            get_activities_for_date_range(client=client, **enrolled_request.dict())
        )

        enrolled_count = (
            len(result["processed_data"].activities)
            if "processed_data" in result
            else 0
        )
        return LambdaResponse(
            status="success", body={"enrolled_count": enrolled_count, "data": result}
        )
    except ValidationError as e:
        send_slack_notification("get_enrolled", f"validation_error: {str(e)}")
        return LambdaResponse(status="validation_error", body={"message": str(e)})
    except Exception as e:
        send_slack_notification("get_enrolled", f"unexpected_error: {str(e)}")
        return LambdaResponse(status="unexpected_error", body={"message": str(e)})
    finally:
        loop.run_until_complete(close_db())


@app.post("/milestones")
def get_milestones(milestone_request: MilestonesRequest):
    """Milestones endpoint

    Endpoint used to get the milestones progress for a given programId


    :return:
    """
    loop = asyncio.get_event_loop()
    try:
        # body = parse(MilestonesRequest, app.current_event.json_body)

        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )
        loop.run_until_complete(client.authenticate())
        loop.run_until_complete(init_db())
        # Implement your logic to fetch milestones
        # This is a placeholder response
        return {"status": "success", "milestones": []}
    except ValidationError as e:
        send_slack_notification("get_milestones", f"unexpected_error: {str(e)}")
        return {"status": "error", "message": str(e)}, 400
    finally:
        loop.run_until_complete(close_db())


async def process_participant(participant, client):
    semaphore = asyncio.Semaphore(500)
    async with semaphore:
        solera_participant = await participant.solera_participant
        if len(solera_participant) > 1:
            logger.warning(
                f"Participant {participant.id} has more than one solera participant record"
            )

        solera_participant_handler = SoleraParticipant(
            participant=participant, solera_client=client
        )
        solera_participant_handler.solera_participant = solera_participant[-1]
        await solera_participant_handler.get_ciba_participant()
        status = await solera_participant_handler.get_enrollment_status()

        if not status.enrolled and participant.status != ParticipantStatus.DELETED:
            await ParticipantService(
                participant_id=participant.id, email=participant.email
            ).delete(
                email=participant.email,
                disenrolledReason=status.disenrolledReason,
                disenrollmentDate=status.disenrollmentDate,
            )
            logger.info(
                f"Participant {participant.id} has been deleted due to solera enrollment status {status}"
            )
            return {participant: solera_participant_handler}
        return None


async def sync_solera_enrollment_status(client: SoleraAPIAsyncClient) -> List[Dict]:
    try:
        logger.info("Syncing solera enrollment status")
        participants = (
            await Participant.filter(is_test=False)
            .prefetch_related("solera_participant", "participant_meta")
            .all()
        )

        deleted_users = []

        logger.info(f"Participants found: {len(participants)}")
        tasks = [process_participant(p, client) for p in participants]
        results = await asyncio.gather(*tasks)
        deleted_users = [res for res in results if res is not None]

        logger.info(f"Deleted participants: {len(deleted_users)}")
        await save_data_to_s3(deleted_users)
        logger.info("Deleted participants data saved to S3")
        return deleted_users
    except Exception as e:
        send_slack_notification(
            "sync_solera_enrollment_status", f"unexpected_error: {str(e)}"
        )
        return []


async def send_daily_activities_to_solera(
    client: SoleraAPIAsyncClient,
    output_location: str = None,
) -> (List[dict], dict):
    """
    Send daily activities to solera
    Is in progress of evolving to use output_location to make less calls to DB

    :param client: SoleraAPIAsyncClient
    :param output_location: str
    :return: tuple of list of dict and dict
    """
    logger.info("Sending daily activities to solera")
    current_datetime = pendulum.now("UTC")
    result = await get_activities_for_date_range(client=client)
    # data = read_csv_from_s3(output_location=output_location)

    if not result["processed_data"].activities:
        logger.info("No activities found for last 24h")
        return

    data = result["processed_data"].model_dump()
    s3_file_path = await save_data_to_s3(data=data["activities"])
    logger.info("Activities for last 24h submitted to solera")
    solera_resp = await SoleraParticipant.submit_activities(
        solera_client=client, activities=result["processed_data"]
    )
    logger.info(f"Solera response: {solera_resp}")

    dynamob_db_payload = {
        "RequestId": solera_resp.requestId,
        "CreatedAt": current_datetime.int_timestamp,
        "filePath": s3_file_path,
    }
    state_table.put_item(Item=dynamob_db_payload)

    return result, dynamob_db_payload


async def save_data_to_s3(data: List[Dict]) -> str:
    current_datetime = pendulum.now("UTC")
    filepath = Path(
        f"/tmp/solera/activities/{current_datetime.year}/{current_datetime.month}/"
    )
    filepath.mkdir(parents=True, exist_ok=True)
    filename = Path(f"{filepath}/{current_datetime.strftime('%Y-%m-%d_%H-%M')}.json")
    with open(filename, mode="w", newline="", encoding="utf-8") as file:
        file.write(json.dumps(data, indent=4))

    s3_file_path = f"{str(filename).strip('/tmp')}"
    put_csv_to_s3(
        file_path=str(filename),
        bucket_name=AWS_BUCKET_NAME,
        s3_key=s3_file_path,
    )
    return s3_file_path


def read_csv_from_s3(output_location: str, region_name="us-east-2") -> pd.DataFrame:
    """
    Read a CSV file from S3 based on the given OutputLocation.

    Args:
        output_location (str): S3 URI of the file (e.g., 's3://bucket-name/path/to/file.csv').
        region_name (str): AWS region where the S3 bucket is located.

    Returns:
        pd.DataFrame: DataFrame containing the CSV data.
    """
    # Parse bucket and key from the S3 URI
    if not output_location.startswith("s3://"):
        raise ValueError(f"Invalid S3 URI: {output_location}")

    s3_parts = output_location.replace("s3://", "").split("/", 1)
    bucket_name = s3_parts[0]
    object_key = s3_parts[1]

    # Initialize S3 client
    s3_client = get_s3_client(region_name=region_name)

    # Retrieve the object content
    try:
        response = s3_client.get_object(Bucket=bucket_name, Key=object_key)
        csv_content = response["Body"].read().decode("utf-8")
    except Exception as e:
        raise RuntimeError(f"Failed to read S3 object: {e}")

    # Convert the CSV content to a pandas DataFrame
    try:
        csv_data = pd.read_csv(StringIO(csv_content))
    except Exception as e:
        raise RuntimeError(f"Failed to parse CSV content: {e}")

    return csv_data


async def async_lambda_handler(
    event: Dict[str, Any],
    context: LambdaContext,
) -> Dict[str, Any]:
    """Lambda handler function

    This function is the main entry point for handling events received
    Event types we handle:
    - Scheduled events (daily at 16:00 UTC)
    - SQS events
    - API Gateway events


    :param event:
    :param context:
    :return:
    """
    logger.info(f"Received event: {event}")
    resp = LambdaResponse()

    try:
        client = SoleraAPIAsyncClient(
            client_id=SETTINGS.SOLERA_CLIENT_ID,
            client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
            environment=SETTINGS.ENV,
        )

        await ensure_db_initialized()
        await client.authenticate()
        logger.info("Authenticated with Solera API")

        if "source" in event and event["source"] == "aws.events":
            logger.info(f"Scheduled event received: {event}")
            resources = event.get("resources", None)
            rule_name = resources[0].split("/")[-1] if resources else None
            logger.info(f"Rule name: {rule_name}")
            if rule_name and "Weekly" in rule_name:
                await sync_solera_enrollment_status(client=client)
            elif rule_name and "Daily" in rule_name:
                result, dynamob_db_payload = await send_daily_activities_to_solera(
                    client=client
                )
                resp.body = dynamob_db_payload
                resp.body["extra_data"] = result
            else:
                raise ValueError("Unsupported rule name")
        elif "Records" in event and event["Records"][0]["eventSource"] == "aws:sqs":
            for record in event["Records"]:
                logger.info(f"SQS event received: {record}")
                message_body = record["body"]
                if isinstance(message_body, bytes):
                    message_body = message_body.decode("utf-8")
                logger.info(f"SQS event received: {message_body}")
                # event_data = SQSNotification(**json.loads(message_body))
                # Placeholder for SQS event handling
            result = {
                "message": "SQS event received"
            }  # Placeholder for SQS event handling
            resp.body = result
        else:
            raise ValueError("Unsupported event type")

    except Exception as e:
        logger.exception(f"Error processing event: {e}")
        resp.status = "error"
        raise
    finally:
        await close_db()
        global DB_INITIALIZED
        DB_INITIALIZED = False
    return resp.model_dump()


@logger.inject_lambda_context(correlation_id_path=correlation_paths.API_GATEWAY_REST)
def lambda_handler(event, context: LambdaContext):
    # Use asyncio.run to execute the async handler
    global SETTINGS
    slc = SlackNotification(
        environment="",
        is_test=False,
        source=context.function_name,
    )
    try:
        SETTINGS = get_settings() if not SETTINGS else SETTINGS
        slc.environment = SETTINGS.ENV
        slc.is_test = True if SETTINGS.ENV == ENV.DEV else False
        slc.title = "MintVault invocation"
        logger.info("Settings initiated")

        logger.info(f"Event received: {event}")

        if "httpMethod" in event:
            slc.type = "API Gateway"
            # API Gateway event
            logger.info("API Gateway event received")
            result = app.resolve(event, context)
        else:
            slc.type = "scheduled event"
            result = asyncio.run(
                async_lambda_handler(event, context)
            )
    except Exception as e:
        logger.exception(f"Error processing event: {e}")
        slc.details = f"An error occurred: {e}"
        slc.title = "Error"
        logger.error(e)
        publish_to_sns(
            sns_client=sns_client,
            sns_topic_arn=SETTINGS.SLACK_SNS_TOPIC_ARN,
            message=slc.model_dump_json(),
        )
        raise e

    return result


# async def main():
#     SETTINGS = get_settings()
#     await ensure_db_initialized()
#     client = SoleraAPIAsyncClient(
#         client_id=SETTINGS.SOLERA_CLIENT_ID,
#         client_secret=SETTINGS.SOLERA_CLIENT_SECRET,
#         environment=SETTINGS.ENV,
#     )

#     participant_request = ParticipantRequest(
#         participant_id="a13bd580-70e1-70ac-ebdf-b62f0692a34f"
#     )

#     result = await get_participant_status(client=client, **participant_request.model_dump())
#     logger.info(result)
#     await close_db()


# if __name__ == "__main__":
#     asyncio.run(main())
