from typing import Dict, Any, Optional, List
from pydantic import BaseModel, field_validator, model_validator
from datetime import datetime
from ciba_participant.activity.models import ParticipantActivityEnum
from uuid import UUID
from ciba_participant.solera.data_preparer import SoleraActivityPayload


# Pydantic models for request validation
class ParticipantRequest(BaseModel):  # pylint: disable=too-few-public-methods
    """Pydantic model for validating participant request

    Should be use exactly one of three attributes.

    Attributes:
        enrollment_id: Optional[str] = None
        participant_id: Optional[str] = None
        participant_email: Optional[str] = None
    """

    enrollment_id: Optional[str] = None
    participant_id: Optional[str] = None
    participant_email: Optional[str] = None


class CatchupRequest(BaseModel):  # pylint: disable=too-few-public-methods
    """Pydantic model for validating catchup request"""

    start_date: Optional[datetime]
    end_date: Optional[datetime]


class SoleraActivityRequest(BaseModel):  # pylint: disable=too-few-public-methods
    """Pydantic model for validating solera activity request

    this is a model which then will be compiled and sent to solera

    Attributes:
        activity_payload: Dict[str, Any]
        enrollment_id: Optional[str] = None
        participant_id: Optional[str] = None
        email: Optional[str] = None
    """

    activity_payload: SoleraActivityPayload
    enrollment_id: Optional[str] = None
    participant_id: Optional[str] = None
    email: Optional[str] = None


class CibaActivityRequest(BaseModel):  # pylint: disable=too-few-public-methods
    """Pydantic model for validating ciba activity request

    this is a model which then will be compiled and sent to solera

    Attributes:
        activity_type: Enum
        activity_date: Optional[datetime]
        activity_value: Optional[str]
        participant_id: Optional[str] = None
    """

    activity_type: ParticipantActivityEnum
    activity_date: Optional[datetime]
    activity_value: Optional[str]
    participant_id: Optional[str] = None
    is_device: Optional[bool] = False


class EnrolledRequest(BaseModel):  # pylint: disable=too-few-public-methods
    """Pydantic model for validating enrolled request

    We are setting the start and end date for the enrolled request
    Attributes:
        start_date: datetime
        end_date: datetime
    """

    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    activity_type: Optional[ParticipantActivityEnum] = ParticipantActivityEnum.ENROLL

    @field_validator("start_date", "end_date", mode="before")
    def parse_dates(cls, value):  # pylint: disable=no-self-argument
        """
        Parse the date strings into datetime objects
        :param value:
        :return:
        """
        if value is None:
            return value
        try:
            # Attempt to parse the date string into a datetime object
            return datetime.fromisoformat(value)
        except ValueError as e:
            raise ValueError(
                f"Invalid date format for {value}. Expected ISO 8601 format."
            ) from e


class MilestonesRequest(BaseModel):  # pylint: disable=too-few-public-methods
    """Pydantic model for validating milestones request

    All what we care here is the programId for which
    we are going to calculate the milestones

    Attributes:
        programId: str

    """

    programId: str


class LambdaResponse(BaseModel):
    """Pydantic model for lambda response

    Attributes:
        status: str
        message: Optional[str] = None
    """

    status: Optional[str] = "success"
    body: Optional[Any] = None


class ParticipantStats(BaseModel):
    """Participant stats model to hold all participant data from Solera and Ciba."""

    id: UUID
    email: str
    enrollment_id: Optional[str] = None
    user_data: Optional[Dict[str, Any]] = None
    enrollment_status: Optional[Dict[str, Any]] = None
    solera_milestone_status: Optional[Dict[str, Any]] = None
    ciba_milestone_status: Optional[Dict[str, Any]] = None
    solera_activities: Optional[List[Dict[str, Any]]] = []
    ciba_activities: Optional[List[Dict[str, Any]]] = []
    error: Optional[str] = None
    cohort_start_date: Optional[datetime] = None
    ciba_enrollment_date: Optional[datetime] = None
    cohort_name: Optional[str] = None
    cohort_id: Optional[str] = None

    @property
    def solera_activities_num(self) -> int:
        """Get the number of Solera activities.

        :return:
        """
        return len(self.solera_activities or [])

    @property
    def ciba_activities_num(self) -> int:
        """Get the number of CIBA activities.

        :return:
        """
        return len(self.ciba_activities or [])

    @model_validator(mode="before")
    def set_default_activities(cls, values):  # pylint: disable=no-self-argument
        """Set default values for activities lists.

        :param values:
        :return:
        """
        values.setdefault("solera_activities", [])
        values.setdefault("ciba_activities", [])
        return values


class ActivityStatusRequest(BaseModel):
    request_id: str
