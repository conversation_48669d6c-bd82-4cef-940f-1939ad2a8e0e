"""Mix of functions to interact with Solera API and CIBA participant data.

Main idea is to keep all solera milestone calculation dependent logic in one place and
solera API interaction in another place.

This script is used to check the status of the participant in Solera and compare it with
CIBA one.

"""

import asyncio
from uuid import UUID
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import httpx
import pandas as pd
import pendulum
import yaml
from tortoise.expressions import Q
from tortoise.queryset import QuerySet

from ciba_participant.solera.data_preparer import DataPreparer, SoleraActivityPayload
from ciba_participant.participant.models import (
    Participant,
    SoleraParticipant as SoleraParticipantModel,
)
from ciba_participant.cohort.models import CohortProgramModules, CohortMembers
from ciba_participant.solera.api import (
    SoleraAPIAsyncClient,
    UserInfo,
    EnrollmentStatus,
    MilestoneStatus,
    SoleraActivitiesList,
    ActivityStatusResp,
    SubmitActivity400Resp,
    SubmitActivityResp,
    ActivityStatus,
    ActivityStatusData,
)
from ciba_participant.settings import get_settings
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ParticipantActivityCategory,
    ActivityUnit,
    SoleraParticipantActivityEnum,
)
from ciba_participant.activity.pydantic_models import ParticipantActivityOutput
from ciba_participant.activity.maps import PARTICIPANT_TO_SOLERA_MAP
from data_models import ParticipantStats, CibaActivityRequest
from milestone_processor import MilestoneProcessor
from aws_lambda_powertools import Logger
from datetime import datetime, timedelta

settings = get_settings()
cwd = Path(__file__).parent

service_name = "MintVault"
logger = Logger(service=service_name)


class Program:
    """Program class to hold program data and check milestones.

    In solera currently we have 3 programs:
    - HWM
    - IBC
    - NDPP

    All program related rules are in milestones_rules.yaml file
    """

    def __init__(self, name: str, milestones: List[Dict[str, Any]]):
        self.name = name
        self.milestones = milestones

    def check_milestone(
        self, milestone_id: int, participant_data: Dict[str, Any]
    ) -> bool:
        """Check if the milestone is met based on the participant activity.

        :param milestone_id:
        :param participant_data:
        :return:
        """
        # Find the milestone by ID
        milestone = next((m for m in self.milestones if m["id"] == milestone_id), None)
        if not milestone:
            logger.error(f"Milestone {milestone_id} not found in program {self.name}")
            return False

        requirements = milestone.get("requirements", [{}])

        # activities_by_milestones = participant_data.get("activities_by_milestones", {})
        # milestone_activities = activities_by_milestones.get(str(milestone_id), [])

        ors = []
        ands = []
        conditions = []

        for req in requirements:
            or_conditions = req.get("or", False)
            and_conditions = req.get("and", False)
            or_met = (
                any(self._check_activity(r, participant_data) for r in or_conditions)
                if or_conditions
                else False
            )
            ors.append(or_met)
            and_met = (
                all(self._check_activity(r, participant_data) for r in and_conditions)
                if and_conditions
                else False
            )
            ands.append(and_met)
            condition_met = (
                self._check_activity(req, participant_data)
                if not or_conditions and not and_conditions
                else False
            )
            conditions.append(condition_met)

        if not any(ors) and not all(ands):
            return all(conditions)
        if any(ors) and all(ands):
            return True
        if all(ands) and not any(ors):
            return True
        if any(ors):
            return True
        return False

    def _check_activity(
        self, req: Dict[str, Any], participant_data: Dict[str, Any]
    ) -> bool:
        """Helper method to check if an activity requirement is met.
        This considers the minimum value or condition and actual values from participant_data.
        """
        activity = req["activity"]
        condition = req.get("condition")
        min_value = req.get("min", 1)  # Default expected min is 1

        # Check condition-based requirements (like weight loss)
        if condition == "min_5_percent_loss_from_start":
            start_weight = float(participant_data.get("start_weight", 0))
            current_weight = float(participant_data.get("current_weight", 0))
            if current_weight <= 0.95 * start_weight:
                return True
            return False
        if condition == "enrolled":
            return participant_data.get("enrollment", 0) > 0

        # For activities, check if the participant data meets the minimum requirement
        actual_value = participant_data.get(activity, 0)
        return actual_value >= min_value

    def split_activities_by_milestones(self, activities):
        """Split activities by milestones.
        Provides better visibility of activities distrivution between milestones

        :param activities:
        :return:
        """
        activities_by_milestones = {}
        for milestone in self.milestones:
            milestone_id = milestone["id"]
            activities_by_milestones[milestone_id] = []
            milestone_week = milestone.get("weeks", "0")
            start_week, end_week = (
                milestone_week.split("-")
                if "-" in milestone_week
                else (milestone_week, milestone_week)
            )
            start_week_int = int(start_week) + 1
            end_week_int = int(end_week) + 1

            actvts = []
            if start_week_int == end_week_int:
                actvts = activities[str(start_week_int)]["activities"]
            else:
                for week in range(start_week_int, end_week_int):
                    try:
                        actvts = activities[str(week)]["activities"]
                    except KeyError:
                        actvts = []
            if actvts:
                activities_by_milestones[milestone_id].append(actvts)
        return activities_by_milestones

    def get_hit_milestones(self, participant_data: Dict[str, Any]) -> List[int]:
        """Mid function to check all milestones statuses for the participant.

        :param participant_data:
        :return:
        """
        hit_milestones = []
        for milestone in self.milestones:
            if self.check_milestone(milestone["id"], participant_data):
                hit_milestones.append(milestone["id"])
        return hit_milestones

    @classmethod
    def load_from_yaml(cls, yaml_content: str) -> List["Program"]:
        """Load program milestones rules from yaml.

        :param yaml_content:
        :return:
        """
        data = yaml.safe_load(yaml_content)
        return [
            cls(program_data["name"], program_data["milestones"])
            for program_data in data["programs"]
        ]


class SoleraParticipant:
    """Get Solera related data from particpant

    and use some methods to process this data.
    Main class to handle transformation participant->solera participant

    """

    def __init__(
        self,
        participant: Participant = None,
        participant_id: UUID = None,
        solera_client: SoleraAPIAsyncClient = None,
    ):
        self.participant = participant if participant else None
        self.participant_id = participant.id if participant else participant_id
        self.data_preparer: DataPreparer = None
        self.solera_client: SoleraAPIAsyncClient = solera_client
        self.solera_participant: SoleraParticipantModel = None

    async def init_solera_data_handler(self):
        """Initialize data preparer for Solera API.

        :return:
        """
        solera_participants = await self.participant.solera_participant
        self.solera_participant: SoleraParticipantModel = (
            solera_participants[0]
            if isinstance(solera_participants, list)
            else solera_participants
        )
        return DataPreparer(self.solera_participant)

    async def get_user_info(self):
        """Retrieve user info from Solera API.

        :return: User info

        response schema: {
                            “https://soleranetwork.com/solera_uuid":"37c172cfbbfd4ad899869dfb",
                            "enrollmentId":"37c172cfbbfd4ad899869dfb",
                            “patientId”:”bf6092b6-4a5b-4890-b175-0013c647a33c”,
                            "programId":"NDPP",
                            "programType":"DiabetesPrevention",
                            "milestoneStructureCode":"M7A3",
                            "given_name":"Aleen",
                            "family_name":"Witting",
                             "ethnicity":["asian"],
                             "gender":"female",
                             "birthdate":"02/22/1988",
                             "heightFeet":"5",
                             "heightInches":"4",
                             "physicalActivityLevel":"slightlyActive",
                             "weight":160,
                             "phone_number":"**********",
                             "email":"<EMAIL>",
                             "address":{
                                "street1":"111 w monroe St",
                                 "street2":"",
                                 "city":"Phoenix",
                                 "state":"AZ",
                                 "zipCode":"85003"
                             },
                             “doNotText”: true,
                             “payorId”: “BCBSSC”,
                             “insuranceType”: “Medicaid”,
                             "languagePreference": "English"
                        }

        """
        user_info = await self.solera_client.get_user_info(
            self.solera_participant.solera_key
        )

        return UserInfo.model_validate(user_info)

    async def get_enrollment_status(self):
        """Retrieve enrollment status from Solera API.

        :return: {
                    "enrolled":true, "disenrolledReason":"", "disenrollmentDate": ""
                 }
        """
        enrollment_status = await self.solera_client.get_enrollment_status(
            self.solera_participant.solera_program_id,
            self.solera_participant.solera_enrollment_id,
        )

        return EnrollmentStatus.model_validate(enrollment_status)

    async def get_milestone_status(self):
        """Retrieve milestone status from Solera API.

        :return: {
                    "userId": "6f553d1d16b3415dbd846223",
                    "programId": "NDPP",
                    "programStart": "2018-06-01T00:00:00.000Z",
                    "milestones": [
                        {
                            "name": "Milestone 1",
                            "date": "2018-06-24T00:00:00.000Z"
                        }
                    ]
                 }
        """

        milestone_status_resp = await self.solera_client.get_milestone_status(
            self.solera_participant.solera_program_id,
            self.solera_participant.solera_enrollment_id,
        )
        if isinstance(milestone_status_resp, dict):
            return MilestoneStatus.model_validate(milestone_status_resp)
        return milestone_status_resp

    async def get_activity_status(self, request_id):
        """Retrieve activity status from Solera API.

        :param request_id:
        :return:
        """
        activity_status_resp = await self.solera_client.get_activity_status(request_id)
        if isinstance(activity_status_resp, dict):
            status_data = activity_status_resp.get("data", {})
            activity_status_data = ActivityStatusData.model_validate(status_data)
            if activity_status_data.successes:
                sucesses = [
                    ActivityStatus.model_validate(data)
                    for data in activity_status_data.successes
                ]
                activity_status_data.successes = sucesses
            if activity_status_data.errors:
                errors = [
                    ActivityStatus.model_validate(data)
                    for data in activity_status_data.errors
                ]
                activity_status_data.errors = errors

            return ActivityStatusResp(
                data=activity_status_data,
            )
        else:
            logger.error(f"Error getting activity status: {activity_status_resp}")

    async def save_activity(self, ciba_activity: CibaActivityRequest):
        try:
            pa = ParticipantActivity(
                created_at=ciba_activity.activity_date,
                updated_at=ciba_activity.activity_date,
                participant=self.participant,
                activity_type=ciba_activity.activity_type,
                value=ciba_activity.activity_value,
                section_id=None,
                live_session_id=None,
            )
            pa.activity_device = (
                ParticipantActivityDevice.WITHINGS
                if ciba_activity.is_device
                else ParticipantActivityDevice.MANUAL_INPUT
            )
            pa.activity_category = (
                ParticipantActivityCategory.WEIGHT
                if ciba_activity.activity_type == ParticipantActivityEnum.WEIGHT
                else ParticipantActivityCategory.ACTIVITY
            )
            pa.unit = (
                ActivityUnit.LB
                if ciba_activity.activity_type == ParticipantActivityEnum.WEIGHT
                else ActivityUnit.ACTION
            )
            logger.info(f"Saving activity: {pa.__dict__}")
            await pa.save()
        except Exception as e:
            logger.error(f"Error saving activity: {e}")
            raise e
        return pa

    @staticmethod
    async def submit_activities(
        activities: SoleraActivitiesList, solera_client: SoleraAPIAsyncClient = None
    ) -> Union[SubmitActivityResp, SubmitActivity400Resp]:
        """Send activity to Solera API.

        :param activities: is an array of prepared activities by data_preparer:
                                {'activities': [activity1, activity2]}
               solera_client: is a solera api client

        :return:
        if 200 response:
                {
                    "requestId":"49d86bda-3df6-48ce-9224-7c06bbfe127e"
                }
        if 400 response:
                {
                    "validationError":{
                        "fields":{
                            "activityData.activities":{
                                "message":"'activities' is required"
                            }
                        },
                        "message":"",
                        "status":400,
                        "name":"ValidateError"
                    }
                }
        """

        activities = activities.model_dump()
        submit_activity_resp = await solera_client.submit_activity(
            activities=activities
        )
        if isinstance(submit_activity_resp, dict):
            return SubmitActivityResp.model_validate(submit_activity_resp)

        if isinstance(submit_activity_resp, httpx.Response):
            if submit_activity_resp.status_code == 400:
                return SubmitActivity400Resp.model_validate(submit_activity_resp.json())

    async def get_user_activities(self) -> SoleraActivitiesList:
        """Retrieve all recorded user activities from Solera API.

        :return: {
                    "activities":[
                        {
                            "userId":"ENROLLMENT_ID_FROM_HANDOFF",
                            "enrollmentId":"ENROLLMENT_ID_FROM_HANDOFF",
                            "referenceId":"YOUR_REFERENCE_ID_HERE",
                            "programId":"PROGRAM_ID_FROM_USERINFO_CALL_HERE",
                            "timestamp":"2021-01-01T01:02:03.000Z",
                            "data":{
                                "Enrollment":true
                            }
                        },
                        {
                            "userId":"ENROLLMENT_ID_FROM_HANDOFF",
                            "enrollmentId":"ENROLLMENT_ID_FROM_HANDOFF",
                            "referenceId":"YOUR_REFERENCE_ID_HERE",
                            "programId":"PROGRAM_ID_FROM_USERINFO_CALL_HERE",
                            "timestamp":"2021-01-01T01:05:03.000Z",
                            "data":{
                                "CoachInteraction":1,
                                "CoachInteractionMinutes":10
                            }
                        }
                    ]
                }
        """
        activities_resp = await self.solera_client.get_user_activity_data(
            self.solera_participant.solera_enrollment_id
        )
        if isinstance(activities_resp, dict):
            return SoleraActivitiesList.model_validate(activities_resp)

        logger.error(f"Error getting user activities: {activities_resp}")
        return activities_resp

    async def enroll(
        self, participant: Participant, activity_id: str = None
    ) -> Union[SubmitActivityResp, SubmitActivity400Resp]:
        """Notifies Solera about participant enrollment.

        :param participant:
        :param activity_id:
        :return: {
                    "data":{
                        "enrolled":true,
                        "disenrolledReason":"",
                        "disenrollmentDate": ""
                    }
                 }
        """
        if not activity_id:
            participant_activities = await participant.activities
            enrolled_activity = [
                activity
                for activity in participant_activities
                if activity.activity_type == ParticipantActivityEnum.ENROLL
            ]
            activity_id = enrolled_activity[0].id if enrolled_activity else None

        activity = await self.data_preparer.prepare_enrollemt_payload(
            activity_id=activity_id, user_created_at=participant.created_at, full=True
        )
        submit_activity_resp = await self.submit_activities(
            activities=activity, solera_client=self.solera_client
        )
        if isinstance(submit_activity_resp, dict):
            return SubmitActivityResp.model_validate(submit_activity_resp)
        return submit_activity_resp

    async def get_full_info(self, format: str = None) -> ParticipantStats:
        """Get full participant info from Solera API and Ciba.

        :return:
        """
        stats = ParticipantStats(
            id=self.participant.id,
            email=self.participant.email,
            enrollment_id=self.solera_participant.solera_enrollment_id,
        )
        try:
            ciba_activities = await ParticipantActivity.filter(
                participant=self.participant
            ).all()
            if ciba_activities:
                ciba_activities = [
                    await ParticipantActivityOutput.from_orm(act)
                    for act in ciba_activities
                ]
                enrollment_date = [
                    act.created_at
                    for act in ciba_activities
                    if act.activity_type == ParticipantActivityEnum.ENROLL
                ]
                if enrollment_date:
                    stats.ciba_enrollment_date = enrollment_date[0]

                if format and format == "json":
                    ciba_activities = [
                        ParticipantActivityOutput.validate(act).model_dump_json()
                        for act in ciba_activities
                    ]
                stats.ciba_activities = ciba_activities

            cohort_members = (
                await CohortMembers.filter(participant_id=self.participant.id)
                .prefetch_related("cohort")
                .all()
            )

            stats.cohort_start_date = cohort_members[0].cohort.started_at
            stats.cohort_name = cohort_members[0].cohort.name
            stats.cohort_id = cohort_members[0].cohort.id
            try:
                stats.user_data = await self.get_user_info()
            except Exception:
                logger.error("got err when get_user_info from solera: {e}")
            try:
                stats.enrollment_status = await self.get_enrollment_status()
            except Exception:
                logger.error("got err when get_enrollment_status from solera: {e}")
            try:
                stats.solera_milestone_status = await self.get_milestone_status()
            except Exception:
                logger.error("got err when get_enrollment_status from solera: {e}")

            try:
                stats.solera_activities = await self.get_user_activities()
            except Exception:
                logger.error("got err when get_enrollment_status from solera: {e}")

            # diff = await self.check_activities_diffs(
            #     stats.solera_activities, ciba_activities
            # )
            # logger.info(diff)
        except Exception as e:  # pylint: disable=broad-except
            stats.error = str(e)

        return stats

    async def check_activities_diffs(
        self,
        solera_activities: SoleraActivitiesList,
        ciba_activities: list[ParticipantActivityOutput],
    ):
        """Check if there are any differences between Solera and Ciba activities."""
        solera_activities = {
            act.referenceId: act.model_dump() for act in solera_activities.activities
        }
        ciba_activities = {act.id: act.model_dump() for act in ciba_activities}

        # compare activities by key in solera and ciba dictt
        diff = {
            k: solera_activities[k]
            for k in set(solera_activities) - set(ciba_activities)
        }

        return diff

    async def convert_ciba_to_solera_activity(
        self, activity: ParticipantActivity
    ) -> Optional[SoleraActivityPayload]:
        """
        Convert a CIBA participant activity to a Solera activity payload.

        :param activity: A CIBA participant activity instance.
        :return: The corresponding Solera activity payload or None if not mappable.
        """
        try:
            # Get the mapping for the activity type
            solera_activity = PARTICIPANT_TO_SOLERA_MAP.get(activity.activity_type)
            if not solera_activity:
                logger.warning(
                    f"No mapping found for activity type: {activity.activity_type}"
                )
                return None

            # For enrollment activities, prepare the enrollment payload.
            if activity.activity_type == ParticipantActivityEnum.ENROLL:
                enrollment_payload = await self.data_preparer.prepare_enrollemt_payload(
                    activity_id=activity.id, user_created_at=self.participant.created_at
                )
                return enrollment_payload

            # For other activity types, build the payload data and prepare it.
            activity_data = {solera_activity: activity.value}
            activity_payload = await self.data_preparer.prepare_activity_payload(
                activity_id=activity.id,
                activity=activity_data,
                activity_type=solera_activity,
                timestamp=activity.created_at,
            )
            return activity_payload

        except Exception as e:
            logger.exception(f"Error processing activity ID {activity.id}: {e}")
            return None

    async def convert_multiple_activities(
        self, activities: list[ParticipantActivity]
    ) -> list:
        semaphore = asyncio.Semaphore(1000)
        counter = {"completed": 0, "total": len(activities)}
        lock = asyncio.Lock()

        async def _process(
            activity: ParticipantActivity,
        ) -> Optional[SoleraActivityPayload]:
            async with semaphore:
                payload = await self.convert_ciba_to_solera_activity(activity)
                async with lock:
                    counter["completed"] += 1
                    if (
                        counter["completed"] % 1000 == 0
                        or counter["completed"] == counter["total"]
                    ):
                        logger.debug(
                            f"Progress: {counter['completed']}/{counter['total']} tasks completed"
                        )
                return payload

        tasks = [_process(activity) for activity in activities]
        payloads = await asyncio.gather(*tasks, return_exceptions=False)
        # Filter out None results
        return [p for p in payloads if p]

    async def check_milestones(
        self,
        ciba_activities: list[ParticipantActivity],
        enrollment_date: pendulum.DateTime,
        program_start: pendulum.DateTime,
        program: str,
    ):
        """Check if participant hit any milestone."""

        solera_activities = await self.convert_multiple_activities(ciba_activities)
        solera_activities_dict = [el.model_dump() for el in solera_activities]

        mp = MilestoneProcessor(
            enrollment_date=enrollment_date,
            program_start=program_start,
        )
        milestones_progress = mp.calculate_participant_milestone(
            participant_activities=solera_activities_dict,
            participant_cohort_start_date=program_start,
            participant_enrollment_date=enrollment_date,
            participant_program=program,
        )

        return milestones_progress

    def _annotate_activities_with_dates(self, ciba_activities, all_cohort_modules):
        for _, dates in all_cohort_modules.items():
            module_activities = [
                a
                for a in ciba_activities
                if a.created_at >= dates["started_at"]
                and a.created_at <= dates["ended_at"]
            ]
            dates["activities"] = module_activities

    def _calculate_activity_counts(self, ciba_activities):
        return {
            "enrollment": 1 if any(ciba_activities) else 0,
            "videoWatched": len(
                filter_activities(ciba_activities, ParticipantActivityEnum.PLAY)
            ),
            "coachInteraction": len(
                filter_activities(ciba_activities, ParticipantActivityEnum.COACH)
            ),
            "weight": len(
                filter_activities(ciba_activities, ParticipantActivityEnum.WEIGHT)
            ),
            "mealsLogged": len(
                filter_activities(ciba_activities, ParticipantActivityEnum.RECIPES)
            ),
            "physicalActivity": len(
                filter_activities(ciba_activities, ParticipantActivityEnum.ACTIVITY)
            ),
            "groupInteraction": len(
                filter_activities(ciba_activities, ParticipantActivityEnum.GROUP)
            ),
        }

    def _calculate_weights(self, participant_meta, ciba_activities):
        start_weight = (
            participant_meta[-1].metadata.get("weight", 0) if participant_meta else 0
        )
        weight_activities = filter_activities(
            ciba_activities, ParticipantActivityEnum.WEIGHT
        )
        current_weight = (
            max(weight_activities, key=lambda x: x.created_at).value
            if weight_activities
            else 0
        )
        return start_weight, current_weight

    def _load_program(self, current_program):
        with open("milestones_rules.yaml", "r", encoding="utf-8") as milestones_file:
            programs = Program.load_from_yaml(milestones_file.read())
        program = next((p for p in programs if p.name == current_program), None)
        if not program:
            raise ValueError(f"Program {current_program} not found in milestones rules")
        return program

    async def get_ciba_participant(self):
        """Retrieve participant data from CIBA.

        :param participant_id:
        :return:
        """
        if not self.participant:
            self.participant = (
                await Participant.filter(id=self.participant_id)
                .prefetch_related("activities")
                .prefetch_related("participant_meta")
                .first()
            )

    async def correct(
        self, reference_id: str, activity_type: SoleraParticipantActivityEnum
    ):
        """Cancels existing solera activity"""
        correction_payload = await self.data_preparer.prepare_correction_payload(
            reference_id=reference_id, activity_type=activity_type
        )
        resp = await self.solera_client.submit_activity(
            activities=correction_payload.model_dump()
        )
        return resp


async def sort_activities_by_participant(participant_activities_list):
    """Sort activities by participant."""
    data = {}

    for activity in participant_activities_list:
        participant_id = str(activity.participant_id)
        if not data.get(participant_id):
            data[participant_id] = []
        data[participant_id].append(activity)
    return data


def is_in_last_24_hours(cohort_start_date: datetime) -> bool:
    """
    Returns True if cohort_start_date occurred within the last 24 hours from now.
    """
    # Convert both to Pendulum instances if needed (if cohort_start_date isn't already)
    now = pendulum.now()
    start_dt = (
        pendulum.instance(cohort_start_date)
        if not isinstance(cohort_start_date, pendulum.DateTime)
        else cohort_start_date
    )

    return (now - start_dt) <= pendulum.duration(hours=24)


async def get_milestone_participants(merged_cohort_date_participant_activities):
    """Get milestone participants."""

    other_m_activities = {}
    m1_activities_by_participant = {}

    for (
        participant_data,
        activities,
    ) in merged_cohort_date_participant_activities.items():
        participant_id, cohort_start_date = participant_data

        if pendulum.instance(cohort_start_date) > pendulum.now():
            # here we ensure that we do not send any activity with future cohort date except ENROLL
            activities = [
                el
                for el in activities
                if el.activity_type == ParticipantActivityEnum.ENROLL
            ]

        if not is_in_last_24_hours(cohort_start_date):
            # If older than 24h, send to "Solera" or some other pipeline
            other_m_activities[participant_id] = activities
            continue

        if not activities:
            continue

        sorted_activities = sorted(activities, key=lambda x: x.created_at)

        first_activity = sorted_activities[0]
        if first_activity.activity_type == ParticipantActivityEnum.WEIGHT:
            m1_activities_by_participant[participant_id] = sorted_activities
        else:
            earliest_weight = next(
                (
                    act
                    for act in sorted_activities
                    if act.activity_type == ParticipantActivityEnum.WEIGHT
                ),
                None,
            )
            if earliest_weight is None:
                logger.warning(f"No WEIGHT found for participant {participant_id}")
                m1_activities_by_participant[participant_id] = sorted_activities
                continue
            earliest_weight.created_at = first_activity.created_at - timedelta(
                minutes=5
            )
            updated_activities = sorted(sorted_activities, key=lambda x: x.created_at)
            m1_activities_by_participant[participant_id] = updated_activities

    return m1_activities_by_participant, other_m_activities


async def get_activities_for_date_range(
    client: SoleraAPIAsyncClient,
    start_date: Optional[pendulum.DateTime] = None,
    end_date: Optional[pendulum.DateTime] = None,
    activity_type: Optional[ParticipantActivityEnum] = None,
    participant_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Retrieve all activities of all participants within a specified date range.
    Uses the SoleraParticipant.convert_multiple_activities method to convert
    CIBA activities to Solera activities with embedded concurrency controls.

    :param client: Solera API client instance.
    :param start_date: Start of the date range.
    :param end_date: End of the date range.
    :param activity_type: Specific activity type to filter.
    :return: Serialized payload of activities and errors.
    """
    errors = {}

    if start_date is None and end_date is None:
        end_date = pendulum.now()
        start_date = end_date.subtract(days=1)

    logger.info(
        f"Getting activities from {start_date.isoformat()} to {end_date.isoformat()}"
    )

    # Build the filter dictionary.
    filters = {
        "created_at__gte": start_date,
        "created_at__lte": end_date,
    }
    if activity_type:
        filters["activity_type"] = activity_type
    if participant_id:
        filters["participant_id"] = participant_id

    logger.info(f"Filters: {filters}")

    # Optimize the query by selecting related participant and filtering by activity_type if provided.
    participant_activities: QuerySet = ParticipantActivity.filter(
        **filters
    ).select_related("participant")

    participant_activities_list = await participant_activities.all()
    logger.info(f"Found {len(participant_activities_list)} activities")

    # Group activities by participant.
    activities_by_participant = await sort_activities_by_participant(
        participant_activities_list
    )
    participant_ids = activities_by_participant.keys()

    participant_filter = {"id__in": participant_ids, "status": "active"}
    active_participant_ids = await Participant.filter(**participant_filter).values_list(
        "id", flat=True
    )
    active_participant_ids_str = [str(pid) for pid in active_participant_ids]

    cohort_members = (
        await CohortMembers.filter(participant_id__in=active_participant_ids)
        .prefetch_related("cohort")
        .all()
    )
    cohort_members_start_date = {
        str(m.participant_id): m.cohort.started_at for m in cohort_members
    }
    merged_cohort_date_participant_activities = {}
    for participant_id in active_participant_ids_str:
        participant_data = (participant_id, cohort_members_start_date[participant_id])
        merged_cohort_date_participant_activities[participant_data] = (
            activities_by_participant[participant_id]
        )

    (
        m1_activities_by_participant,
        other_m_activities_by_participant,
    ) = await get_milestone_participants(merged_cohort_date_participant_activities)

    # Merge activities from both milestone groups.
    merged_activities = []
    for el in other_m_activities_by_participant.values():
        merged_activities.extend(el)
    for el in m1_activities_by_participant.values():
        merged_activities.extend(el)

    # Group merged activities by participant id.
    from collections import defaultdict

    activities_grouped = defaultdict(list)
    for activity in merged_activities:
        activities_grouped[activity.participant.id].append(activity)

    # For each participant, create a SoleraParticipant instance, initialize its data preparer,
    # and convert its list of activities using the concurrency-controlled method.
    async def process_participant(participant_id, activities):
        participant = activities[
            0
        ].participant  # All activities share the same participant.
        solera_participant = SoleraParticipant(
            participant=participant, solera_client=client
        )
        await solera_participant.get_ciba_participant()
        solera_participant.data_preparer = (
            await solera_participant.init_solera_data_handler()
        )
        payloads = await solera_participant.convert_multiple_activities(activities)
        return payloads

    tasks = [process_participant(pid, acts) for pid, acts in activities_grouped.items()]
    results = await asyncio.gather(*tasks, return_exceptions=False)
    # Flatten list of lists into a single list.
    all_payloads = [payload for sublist in results for payload in sublist]

    full_activity_payload = SoleraActivitiesList()
    full_activity_payload.activities = all_payloads
    return {"processed_data": full_activity_payload, "errors": errors}


async def get_participant_status(
    participant_id: UUID = None,
    participant_email: str = None,
    enrollment_id: str = None,
    client: SoleraAPIAsyncClient = None,
) -> ParticipantStats:
    """Get full participant status both from Solera API and Ciba.

    :param participant_id:
    :param participant_email:
    :param enrollment_id:
    :param client:
    :return:
    """
    if not client:
        raise ValueError("SoleraAPIAsyncClient instance must be provided")

    if participant_id:
        participant = (
            await Participant.all_participants.filter(id=participant_id)
            .first()
            .prefetch_related("activities")
        )
    elif participant_email:
        participant = (
            await Participant.all_participants.filter(email=participant_email)
            .first()
            .prefetch_related("activities")
        )
    elif enrollment_id:
        participant = (
            await Participant.all_participants.filter(
                solera_participant__solera_enrollment_id=enrollment_id
            )
            .first()
            .prefetch_related("activities")
        )
    else:
        raise ValueError("Provide participant_id, participant_email, or enrollment_id")

    solera_participant = SoleraParticipant(
        participant=participant, solera_client=client
    )
    await solera_participant.get_ciba_participant()
    solera_participant.data_preparer = (
        await solera_participant.init_solera_data_handler()
    )
    full_info = await solera_participant.get_full_info()
    cohort_start_date = pendulum.instance(full_info.cohort_start_date)
    try:
        program = full_info.user_data.programId if full_info.user_data else "NDPP"
        full_info.ciba_milestone_status = await solera_participant.check_milestones(
            ciba_activities=full_info.ciba_activities,
            enrollment_date=full_info.ciba_enrollment_date,
            program_start=cohort_start_date,
            program=program,
        )
    except Exception as e:
        logger.error(f"Error checking milestones for participant {participant.id}: {e}")

    return full_info


def filter_activities(
    activities,
    activity_type: ParticipantActivityEnum,
) -> list[ParticipantActivity]:
    """Filters activities by type, device, and date range."""
    if not activities:
        logger.error(f"No activities found for: {activity_type}")
        return []
    return [
        activity for activity in activities if activity.activity_type == activity_type
    ]


async def get_current_cohort_program_module(cohort_id) -> (CohortProgramModules, dict):
    """Calculate current module and all modules for the cohort.

    :param cohort_id:
    :return:
    """
    now = pendulum.now()

    current_module = await CohortProgramModules.filter(
        Q(started_at__lte=now) & Q(ended_at__gte=now) & Q(cohort_id=cohort_id)
    ).first()

    all_modules = (
        await CohortProgramModules.filter(cohort_id=cohort_id)
        .prefetch_related("program_module")
        .all()
    )
    ready_modules = {}
    for m in all_modules:
        program_module = await m.program_module
        ready_modules[program_module.short_title.split("/")[0].replace("Week ", "")] = {
            "started_at": m.started_at,
            "ended_at": m.ended_at,
        }

    return current_module, ready_modules


async def geather_all_participants_data(csv_participant_ids_list):
    from ciba_participant.common.db import init_db, close_db

    await init_db()

    if csv_participant_ids_list:
        participants_list = await Participant.filter(
            id__not_in=csv_participant_ids_list
        ).all()
    else:
        participants_list = await Participant.all()

    # Create an instance of SoleraAPIAsyncClient
    client = SoleraAPIAsyncClient(
        client_id=settings.SOLERA_CLIENT_ID,
        client_secret=settings.SOLERA_CLIENT_SECRET,
        environment=settings.ENV,
    )
    await client.authenticate()

    all_participants_stats = []
    participants_len = len(participants_list)
    for participant in participants_list:
        activity_stats = {
            "ciba_activities_num": "",
            "solera_activities_num": "",
            "enrollment_status": "",
            "enrollment_id": "",
            "email": participant.email,
            "participant_id": str(participant.id),
            "ciba_milestone_status": "",
            "solera_milestone_status": "",
            "error": "",
        }
        try:
            logger.info(
                f"Processing participant ({participants_list.index(participant)}/{participants_len})"
            )
            participant_id = participant.id

            # Create an instance of SoleraParticipant
            solera_participant = SoleraParticipant(
                participant_id=participant_id, solera_client=client
            )

            # Initialize the Solera data handler
            await solera_participant.get_ciba_participant()
            await solera_participant.init_solera_data_handler()

            # Get milestone progress
            participant_metrics = await solera_participant.get_full_info()
            activity_stats["ciba_activities_num"] = len(
                participant_metrics.ciba_activities
            )
            activity_stats["solera_activities_num"] = len(
                participant_metrics.solera_activities
            )
            activity_stats["enrollment_status"] = participant_metrics.enrollment_status
            activity_stats["enrollment_id"] = participant_metrics.enrollment_id
            activity_stats["solera_milestone_status"] = (
                participant_metrics.solera_milestone_status
            )

            ciba_milestone_status = await solera_participant.check_milestones(
                ciba_activities=participant_metrics.ciba_activities,
                enrollment_date=participant_metrics.ciba_enrollment_date,
                program_start=participant_metrics.cohort_start_date,
                program=participant_metrics.user_data.programId,
            )
            ciba_milestone_status = (
                ciba_milestone_status if ciba_milestone_status else ""
            )
            activity_stats["ciba_milestone_status"] = ciba_milestone_status
        except Exception as e:
            logger.exception(f"Error processing participant: {e}")
            activity_stats["error"] = str(e)
        finally:
            all_participants_stats.append(activity_stats)
    all_participants_stats_df = pd.DataFrame(all_participants_stats)
    all_participants_stats_df.to_csv(
        "all_participants_stats_leftovers.csv", index=False
    )
    logger.info("All participants processed")

    await close_db()


async def main():
    from ciba_participant.common.db import init_db, close_db

    client = SoleraAPIAsyncClient(
        client_id=settings.SOLERA_CLIENT_ID,
        client_secret=settings.SOLERA_CLIENT_SECRET,
        environment=settings.ENV,
    )
    await init_db()
    await get_activities_for_date_range(client)
    await close_db()


if __name__ == "__main__":
    asyncio.run(main())
