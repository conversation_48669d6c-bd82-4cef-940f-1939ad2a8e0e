# Makefile for building the Python package using Poetry

.PHONY: help install build clean test lint-all ruff bandit mypy precommit

help:  ## Display this help
	@echo "Available commands:"
	@echo "  make install  - Install dependencies"
	@echo "  make build    - Build the package"
	@echo "  make clean    - Clean build artifacts"
	@echo "  make test     - Run tests"

install:  ## Install dependencies using Poetry
	uv sync

build:  ## Build the package
	uv build

clean:  ## Clean build artifacts
	rm -rf dist/
	rm -rf build/
	rm -rf *.egg-info
	rm -f ruff.json bandit.json mypy.json precommit.txt

test:  ## Run tests using pytest
	uv run pytest

lint-all: ruff bandit mypy precommit

ruff:
	uv run ruff check . --output-format json > ruff.json

bandit:
	uv run bandit -r -c pyproject.toml . -f json -o bandit.json

mypy:
	uv run mypy . --strict --ignore-missing-imports | poetry run mypy-json-report parse --output-file mypy.json

precommit:
	uv run pre-commit run --all-files --show-diff-on-failure --all > precommit.txt
