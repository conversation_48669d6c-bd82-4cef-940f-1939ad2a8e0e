SHELL := /bin/bash
export IMAGE_PATH := rpm-registration-api
lint:
	uv run black .
	uv run isort .
	uv run flake8 .
	# poetry run mypy .
	uv run pylint --fail-under=9.5 app tests

recreate:
	docker compose stop
	docker compose rm -f app
	docker rmi -f rpm-registration-service_app
	docker compose up -d

recreate-with-db: recreate
	docker volume rm -f rpm_registration_db
	docker compose up -d

up:
	docker compose up -d

down:
	docker compose down


migrate:
	docker compose -it app aerich upgrade

clear:
	docker compose down
	docker rmi -f rpm-registration-service_app
	docker volume rm -f rpm_registration_db
