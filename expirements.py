import pendulum

from ciba_participant.activity.models import ParticipantActivity
from ciba_participant.cohort.models import CohortMembers, CohortMembershipStatus
from ciba_participant.common.db import init_db, close_db
from ciba_participant.notifications.email.send_grid_email import (
    get_cohorts_ending_tomorrow_csv,
)
from ciba_participant.participant.models import Participant


async def main():
    await init_db()

    # cohorts = await CohortRepository.get_paginated_cohorts(
    #     page=1, per_page=10, include={Include.program_modules, Include.participants, Include.program, Include.created_by}, filters=FilterInput(
    #         cohort_status=CohortStatusFilter.ending,
    #     )
    # )
    #
    # total_pages = cohorts.total_pages
    #
    # print("=" * 80)
    # print(f"COHORTS DEBUG OUTPUT")
    # print("=" * 80)
    # print(f"Total Pages: {total_pages}")
    # print()
    #
    # for i, cohort in enumerate(cohorts.cohorts, 1):
    #     print(f"[{i}] COHORT: {cohort.name}")
    #     print("-" * 60)
    #     print(f"  ID: {cohort.id}")
    #     print(f"  Status: {cohort.status.value}")
    #     print(f"  Created: {cohort.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Updated: {cohort.updated_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Start Date: {cohort.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  End Date: {cohort.end_date.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Participant Limit: {cohort.limit}")
    #     print(f"  Current Participants: {len(cohort.participants)}")
    #     print()
    #
    #     print("=" * 80)
    #     print()

    await Participant.filter(
        id=participant_id,
        status__in=[ParticipantStatus.ACTIVE, ParticipantStatus.COMPLETED],
    ).get_or_none()

    await close_db()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
