import pytest
from unittest.mock import AsyncMock, patch

from ciba_participant.cohort.models import (
    CohortM<PERSON>bers,
    Cohort,
    CohortProgramModules,
)
from ciba_participant.test_utils import (
    DateWhen,
    add_participant_to_cohort,
    create_authorized_user,
    create_cohort,
    create_full_program,
    create_participant,
    clear_db,
)

from src.cohort.mutations import delete_cohort, CohortIDInput


@pytest.fixture(scope="function")
async def happy_path():
    await clear_db()

    program, _, _ = await create_full_program()
    authorized_user = await create_authorized_user()
    (
        participant,
        solera_participant,
        participant_meta,
    ) = await create_participant()

    cohort = await create_cohort(
        program_id=program.id,
        created_by=authorized_user.id,
        cohort_date=DateWhen.NOW,
    )

    await add_participant_to_cohort(
        cohort_id=cohort.id, participant_id=participant.id
    )

    return cohort, participant


@pytest.mark.asyncio
async def test_delete_cohort(happy_path):
    with (
        patch(
            "ciba_participant.cohort.crud.delete_conversation",
            new_callable=AsyncMock,
        ) as mock_delete_conversation,
    ):
        cohort, participant = await happy_path

        # Test delete cohort with participant
        with pytest.raises(ValueError) as exc_info:
            await delete_cohort(CohortIDInput(id=cohort.id))
        assert (
            exc_info.value.args[0]
            == "A cohort with at least one participant, cannot be deleted."
        )

        # Test delete cohort without participant
        await CohortMembers.filter(participant_id=participant.id).delete()

        assert (
            await CohortMembers.filter(cohort_id=cohort.id).exists() is False
        )

        await delete_cohort(CohortIDInput(id=cohort.id))

        assert await Cohort.filter(id=cohort.id).first() is None
        assert (
            await CohortProgramModules.filter(cohort_id=cohort.id).exists()
            is False
        )
        mock_delete_conversation.assert_called()
