from typing import Any
from uuid import UUID, uuid4

import jwt
import pytest
import strawberry
from ciba_participant.participant.models import (
    Authorized,
    AutorizedRole,
    ParticipantStatus,
)
from ciba_participant.test_utils import clear_db
from cryptography.hazmat.primitives.asymmetric import rsa
from fastapi import Depends, FastAPI
from fastapi.testclient import TestClient
from strawberry.fastapi import GraphQLRouter

from src.auth import (
    WithAuthorizedUser,
    WithBearerToken,
    WithUnverifiedCognitoAuth,
)
from src.auth.decorators import is_authorized
from tests.integration.auth.init_test import generate_token_data

TEST_PREFIX = "/graphql"
TEST_QUERY = {"query": "{ test }"}
TEST_ERROR = "custom error"


async def create_authorized(authorized_id=uuid4()) -> Authorized:
    return await Authorized.create(
        id=authorized_id,
        email="<EMAIL>",
        first_name="user",
        last_name="name",
        status=ParticipantStatus.ACTIVE,
        cognito_sub=authorized_id,
        is_test=True,
        role=AutorizedRole.HEALTH_COACH,
        alternative_host=False,
        ciba_api_id=None,
        support_in_chat=False,
    )


def encode_data(data: dict) -> str:
    return jwt.encode(
        payload=data,
        key=rsa.generate_private_key(public_exponent=65537, key_size=2048),
        algorithm="RS256",
    )


@strawberry.type
class Query(strawberry.schema.BaseSchema):
    @strawberry.field
    @is_authorized()
    async def test(self, info: strawberry.Info) -> bool:
        return True


@strawberry.type
class QueryWithCustomErrorMsg(strawberry.schema.BaseSchema):
    @strawberry.field
    @is_authorized(error_msg=TEST_ERROR)
    async def test(self, info: strawberry.Info) -> bool:
        return True


class Context(
    WithBearerToken,
    WithAuthorizedUser,
    WithUnverifiedCognitoAuth,
):
    pass


def provide_context() -> Context:
    return Context()


def get_context(context: Context = Depends(provide_context)) -> Context:
    return context


@pytest.fixture
def mock_token_data():
    data = generate_token_data()
    token = encode_data(data)

    return data, token


@pytest.fixture
def mock_test_client():
    app = FastAPI()
    schema = strawberry.Schema(query=Query)
    graphql_app = GraphQLRouter[Any, None](
        schema=schema, context_getter=get_context
    )
    app.include_router(graphql_app, prefix=TEST_PREFIX)
    test_client = TestClient(app)

    return test_client


@pytest.mark.asyncio
async def test_is_authorized(mock_token_data, mock_test_client):
    data, token = mock_token_data
    test_client = mock_test_client

    await clear_db()
    await create_authorized(UUID(data["sub"]))

    response = test_client.post(
        TEST_PREFIX,
        json=TEST_QUERY,
        headers={"Authorization": f"Bearer {token}"},
    )

    response_data = response.json()

    assert response_data == {"data": {"test": True}}


@pytest.mark.asyncio
async def test_is_not_authorized(mock_token_data, mock_test_client):
    _, token = mock_token_data
    test_client = mock_test_client

    await clear_db()
    await create_authorized(uuid4())

    response = test_client.post(
        TEST_PREFIX,
        json=TEST_QUERY,
        headers={"Authorization": f"Bearer {token}"},
    )

    response_data = response.json()

    assert response_data.get("data") is None
    assert response_data.get("errors") == [
        {
            "message": "Unauthorized",
            "locations": [{"line": 1, "column": 3}],
            "path": ["test"],
        }
    ]


@pytest.mark.asyncio
async def test_is_not_authorized_with_custom_error_message(mock_token_data):
    _, token = mock_token_data

    await clear_db()
    await create_authorized(uuid4())

    app = FastAPI()
    schema = strawberry.Schema(query=QueryWithCustomErrorMsg)
    graphql_app = GraphQLRouter[Any, None](
        schema=schema, context_getter=get_context
    )
    app.include_router(graphql_app, prefix=TEST_PREFIX)
    test_client = TestClient(app)

    response = test_client.post(
        TEST_PREFIX,
        json=TEST_QUERY,
        headers={"Authorization": f"Bearer {token}"},
    )

    response_data = response.json()

    assert response_data.get("data") is None
    assert response_data.get("errors") == [
        {
            "message": TEST_ERROR,
            "locations": [{"line": 1, "column": 3}],
            "path": ["test"],
        }
    ]
