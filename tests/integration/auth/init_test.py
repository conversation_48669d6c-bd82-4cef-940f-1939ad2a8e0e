import time
from datetime import <PERSON><PERSON><PERSON>
from functools import cached_property
from uuid import UUID, uuid4

import httpx
import jwt
import jwt.algorithms
import pytest
from ciba_participant.participant.models import (
    Authorized,
    AutorizedRole,
    ParticipantStatus,
)
from ciba_participant.test_utils import clear_db
from cryptography.hazmat.primitives.asymmetric import rsa
from starlette.datastructures import Headers
from starlette.requests import Request

from src.auth import (
    WithAuthorizedUser,
    WithBearerToken,
    WithCognitoUser,
    WithUnverifiedCognitoAuth,
    WithVerifiedCognitoAuth,
    get_cognito_info,
    get_cognito_issuer,
)
from src.auth.errors import MissingAuthorizationHeaderError, MissingTokenError
from src.auth.jwks import JWKSCache
from src.settings import get_settings


def test_get_aws_jwks_url():
    expected = "https://cognito-idp.some-region.amazonaws.com/some-pool-id"
    result = get_cognito_issuer("some-region", "some-pool-id")

    assert result == expected


@pytest.mark.parametrize(
    "test_request, expected_error",
    [
        (
            Request({"type": "http", "headers": []}),
            MissingAuthorizationHeaderError,
        ),
        (
            Request(
                {
                    "type": "http",
                    "headers": Headers({"Authorization": "NoBearer"}).raw,
                }
            ),
            MissingTokenError,
        ),
    ],
)
def test_with_bearer_token_raising_error(test_request, expected_error):
    ctx = WithBearerToken()
    ctx.request = test_request

    with pytest.raises(expected_error):
        ctx.token


def test_with_bearer_token():
    request = Request(
        {
            "type": "http",
            "headers": Headers({"Authorization": "Bearer the-token"}).raw,
        }
    )

    ctx = WithBearerToken()
    ctx.request = request

    assert ctx.token == "the-token"


def generate_token_data():
    return {
        "sub": str(uuid4()),
        "email_verified": True,
        "custom:isParticipantAdmin": "1",
        "iss": "some_issuer",
        "cognito:username": str(uuid4()),
        "origin_jti": str(uuid4()),
        "aud": "abcdefghijklmnopqrstuvwx",
        "event_id": str(uuid4()),
        "token_use": "id",
        "auth_time": int(time.time()),
        "exp": int(time.time() + timedelta(days=1).total_seconds()),
        "iat": int(time.time()),
        "jti": str(uuid4()),
        "email": "<EMAIL>",
        "custom:isAdmin": "1",
    }


def verify_auth_assertions(context, data, token):
    assert context.auth
    assert context.auth.cognito.email == data["email"]
    assert context.auth.cognito.email_verified == data["email_verified"]
    assert context.auth.cognito.origin_jti == data["origin_jti"]
    assert context.auth.cognito.event_id == data["event_id"]
    assert context.auth.cognito.token_use == data["token_use"]
    assert context.auth.ciba.is_admin == data["custom:isAdmin"]
    assert (
        context.auth.ciba.is_participant_admin
        == data["custom:isParticipantAdmin"]
    )
    assert context.auth.ciba.username == data["cognito:username"]
    assert context.auth.jwt.sub == data["sub"]
    assert context.auth.jwt.aud == data["aud"]
    assert context.auth.jwt.auth_time == data["auth_time"]
    assert context.auth.jwt.exp == data["exp"]
    assert context.auth.jwt.iat == data["iat"]
    assert context.auth.jwt.jti == data["jti"]
    assert context.token == token


def test_with_verified_cognito_auth():
    region = "some-region"
    pool_id = "some-pool-id"
    client_id = "abcdefghijklmnopqrstuvwx"
    cognito = get_cognito_info(region, pool_id)

    data = generate_token_data()
    data["aud"] = client_id
    data["iss"] = cognito.issuer

    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )

    public_key = private_key.public_key()

    kid = "9Wm9Gtoz_rGlfDkDfpvE0WeealEulrS7X6nzBr9CLgI"

    public_jwks = jwt.algorithms.RSAAlgorithm.to_jwk(public_key, as_dict=True)
    public_jwks["kid"] = kid
    public_jwks["alg"] = "RS256"
    public_jwks["use"] = "sig"

    def handler(request):
        return httpx.Response(200, json={"keys": [public_jwks]})

    transport = httpx.MockTransport(handler=handler)
    client = httpx.Client(transport=transport)

    JWKSCache._client = client

    token = jwt.encode(
        payload=data, key=private_key, algorithm="RS256", headers={"kid": kid}
    )

    settings = get_settings()
    original_region = settings.AWS_REGION
    original_pool_id = settings.PROVIDERS_COGNITO_USER_POOL_ID
    original_client_id = settings.PROVIDERS_COGNITO_APP_CLIENT_ID

    settings.COGNITO_AWS_REGION = region
    settings.PROVIDERS_COGNITO_USER_POOL_ID = pool_id
    settings.PROVIDERS_COGNITO_APP_CLIENT_ID = client_id

    try:

        class Context(WithVerifiedCognitoAuth):
            @cached_property
            def token(self) -> str:
                return token

        ctx = Context()

        verify_auth_assertions(ctx, data, token)
    finally:
        settings.COGNITO_AWS_REGION = original_region
        settings.PROVIDERS_COGNITO_USER_POOL_ID = original_pool_id
        settings.PROVIDERS_COGNITO_APP_CLIENT_ID = original_client_id


def test_with_unverified_cognito_auth():
    data = generate_token_data()

    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )

    token = jwt.encode(
        payload=data,
        key=private_key,
        algorithm="RS256",
    )

    class Context(WithUnverifiedCognitoAuth):
        @cached_property
        def token(self) -> str:
            return token

    ctx = Context()

    verify_auth_assertions(ctx, data, token)


@pytest.mark.asyncio
async def test_with_cognito_user():
    data = generate_token_data()

    class Context(WithCognitoUser, WithUnverifiedCognitoAuth):
        @cached_property
        def token(self) -> str:
            return jwt.encode(
                payload=data,
                key=rsa.generate_private_key(
                    public_exponent=65537, key_size=2048
                ),
                algorithm="RS256",
            )

    ctx = Context()

    user = ctx.user
    assert user
    assert str(user.id) == data["sub"]
    assert user.email == data["email"]
    assert user.email_verified == data["email_verified"]


@pytest.mark.asyncio
async def test_context_including_authorized_data():
    """
    context.authorized should include authorized user information
    with admin permissions flags.
    """

    data = generate_token_data()
    authorized_id = UUID(data["sub"])

    await clear_db()
    authorized = Authorized(
        id=authorized_id,
        email="<EMAIL>",
        first_name="user",
        last_name="name",
        status=ParticipantStatus.ACTIVE,
        cognito_sub=authorized_id,
        is_test=True,
        role=AutorizedRole.HEALTH_COACH,
        alternative_host=False,
        ciba_api_id=None,
        support_in_chat=False,
        content_admin=True,
    )

    await authorized.save()

    class Context(WithAuthorizedUser, WithUnverifiedCognitoAuth):
        @cached_property
        def token(self) -> str:
            return jwt.encode(
                payload=data,
                key=rsa.generate_private_key(
                    public_exponent=65537, key_size=2048
                ),
                algorithm="RS256",
            )

    ctx = Context()
    authorized = await ctx.authorized

    assert authorized
    assert authorized.id == authorized_id
    assert authorized.email == data["email"]
    assert authorized.first_name == "user"
    assert authorized.last_name == "name"
    assert authorized.chat_identity
    assert authorized.role == AutorizedRole.HEALTH_COACH
    assert authorized.status == ParticipantStatus.ACTIVE
    assert authorized.content_admin is True
    assert authorized.classes_admin is False
