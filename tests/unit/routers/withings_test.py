from unittest.mock import AsyncMock, MagicMock, patch

import pendulum
import pytest
from starlette.responses import RedirectResponse

from app.routers.withings import subscribe_to_account
from tests.unit.common import TEST_URL, TEST_MEMBER_STATE, TEST_EMAIL

get_member_by_state_mock = (
    "ciba_iot_etl.models.db.member_state.MemberState.get_member_by_state"
)
get_redirect_url_mock = (
    "ciba_iot_etl.models.db.member_state.MemberState.get_redirect_uri"
)
filter_mock = "ciba_iot_etl.models.db.member_state.MemberState.filter"
process_member_state_mock = "app.services.common.process_member_state"


class DummyMemberState:
    def __init__(self, sync_start_date):
        self.sync_start_date = sync_start_date
        self.redirect_uri = TEST_URL


TEST_MEMBER_STATE_MODEL = DummyMemberState(
    sync_start_date=pendulum.now().subtract(days=30)
)


class DummyQuery:
    def __init__(self, result):
        self.result = result

    async def get_or_none(self):
        # Return the dummy result asynchronously
        return self.result


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_error():
    """
    subscribe_to_account should return a RedirectResponse with error
    when the error parameter is provided.
    """
    test_member = AsyncMock()
    dummy_query = DummyQuery(TEST_MEMBER_STATE_MODEL)

    with (
        patch(get_member_by_state_mock) as mocked_member_search,
        patch(get_redirect_url_mock) as mocked_redirect_search,
        patch(filter_mock) as mocked_state,
        # Make sure process_member_state returns a site to trigger the redirect
        patch("app.services.common.process_member_state") as mocked_process,
    ):
        mocked_state.return_value = dummy_query
        mocked_member_search.return_value = test_member
        mocked_redirect_search.return_value = TEST_URL
        mocked_process.return_value = (
            f"{TEST_URL}?connectionError=Invalid user",
            0,
        )

        actual_value = await subscribe_to_account(
            error="Invalid user", state=TEST_MEMBER_STATE
        )

    # Check that we got a RedirectResponse with the expected URL
    assert isinstance(actual_value, RedirectResponse)
    assert actual_value.status_code == 307  # Temporary redirect
    # URL encoding changes spaces to %20, so we need to check for the encoded version
    assert "Invalid%20user" in actual_value.headers["location"]


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_valid():
    """
    subscribe_to_account should return a RedirectResponse when valid email and code are provided.
    """
    test_member = AsyncMock()
    dummy_query = DummyQuery(TEST_MEMBER_STATE_MODEL)

    with (
        patch(get_member_by_state_mock) as mocked_member_search,
        patch(get_redirect_url_mock) as mocked_redirect_search,
        patch(filter_mock) as mocked_state,
        # Make sure process_member_state returns a site to trigger the redirect
        patch(process_member_state_mock) as mocked_process,
    ):
        mocked_state.return_value = dummy_query
        mocked_member_search.return_value = test_member
        mocked_redirect_search.return_value = TEST_URL
        mocked_process.return_value = (TEST_URL, 0)

        # Mock the handle_subscribe_to_account method
        with patch(
            "app.services.withings.MemberWithings.handle_subscribe_to_account"
        ) as mock_handle:
            mock_handle.return_value = {"token": "test-token", "healthy": True}

            actual_value = await subscribe_to_account(
                email=TEST_EMAIL,
                code="valid_code",
                state=TEST_MEMBER_STATE,
                error="",
            )

    # Check that we got a RedirectResponse
    assert isinstance(actual_value, RedirectResponse)
    assert actual_value.status_code == 307  # Temporary redirect


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_no_code():
    """
    subscribe_to_account should return a RedirectResponse with error if no code is provided.
    """
    test_member = AsyncMock()
    dummy_query = DummyQuery(TEST_MEMBER_STATE_MODEL)
    process_member_state_mock = "app.services.common.process_member_state"

    with (
        patch(get_member_by_state_mock) as mocked_member_search,
        patch(get_redirect_url_mock) as mocked_redirect_search,
        patch(filter_mock) as mocked_state,
        # Make sure process_member_state returns a site to trigger the redirect
        patch(process_member_state_mock) as mocked_process,
    ):
        mocked_state.return_value = dummy_query
        mocked_member_search.return_value = test_member
        mocked_redirect_search.return_value = TEST_URL
        mocked_process.return_value = (
            f"{TEST_URL}?connectionError=No code provided",
            0,
        )

        actual_value = await subscribe_to_account(
            state=TEST_MEMBER_STATE,
            code="",
            email=TEST_EMAIL,
            error="",
        )

    # Check that we got a RedirectResponse with the expected URL
    assert isinstance(actual_value, RedirectResponse)
    assert actual_value.status_code == 307  # Temporary redirect
    # URL encoding changes spaces to %20, so we need to check for the encoded version
    assert "No%20code%20provided" in actual_value.headers["location"]


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_no_state_or_email():
    """
    subscribe_to_account should return a TokenResp with error if neither state nor email is provided.
    """
    # Create a mock response object with an error attribute
    mock_response = MagicMock()
    mock_response.error = "Neither email nor state provided"

    # Patch the subscribe_to_account_helper function to return our mock
    with patch(
        "app.routers.withings.subscribe_to_account_helper",
        return_value=mock_response,
    ):
        actual_value = await subscribe_to_account(
            state="", code="valid_code", email="", error=""
        )

    # Check that the response has the expected error attribute
    assert hasattr(actual_value, "error")
    assert actual_value.error == "Neither email nor state provided"


@pytest.mark.asyncio
async def test_withings_subscribe_to_account_member_not_found():
    """
    subscribe_to_account should return a RedirectResponse when member is not found.
    """
    # Create a mock response object with an error attribute
    mock_response = MagicMock()
    mock_response.error = "Member not found"

    # Mock the redirect URL
    redirect_url = f"{TEST_URL}?connectionError=Member%20not%20found"

    # Patch the subscribe_to_account_helper function to return a RedirectResponse
    with patch(
        "app.routers.withings.subscribe_to_account_helper"
    ) as mock_helper:
        # Make the helper return a RedirectResponse directly
        mock_helper.return_value = RedirectResponse(redirect_url)

        # Call the function
        actual_value = await subscribe_to_account(
            state=TEST_MEMBER_STATE,
            code="valid_code",
            email=TEST_EMAIL,
            error="",
        )

    # Check that we got a RedirectResponse
    assert isinstance(actual_value, RedirectResponse)
    assert actual_value.status_code == 307  # Temporary redirect
    # We don't check the exact URL content since it's mocked
