import pytest
from unittest.mock import MagicMock, patch, AsyncMock
import pendulum

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.classes.models import BookingStatusEnum, Booking
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.participant.models import ParticipantStatus, Participant

from ciba_participant.notifications.push.queries import (
    get_chat_query,
    get_start_module_participants,
    get_participants_no_weight_activity,
    get_participants,
    get_participants_with_class_tomorrow,
    get_participants_with_class_in_next_hour,
)


class TestGetChatQuery:
    def test_basic_substitution(self):
        """
        get_chat_query should return a query with the correct chat_identity and sid.
        """
        sql = get_chat_query("foo@bar", "XYZ123")
        assert "chat_identity = 'foo@bar'" in sql
        assert "sid = 'XYZ123'" in sql


class TestGetStartModuleParticipants:
    @pytest.fixture
    def mock_cohort_program_modules(self):
        return MagicMock()

    @pytest.fixture
    def mock_participant(self):
        participant = MagicMock()
        participant.email = "<EMAIL>"
        participant.first_name = "Test"
        participant.last_name = "User"
        participant.status = ParticipantStatus.ACTIVE
        meta = MagicMock()
        meta.metadata = {"is_weight": True}
        participant.participant_meta = [meta]
        return participant

    @pytest.mark.asyncio
    @patch.object(CohortProgramModules, "filter", autospec=True)
    async def test_get_start_module_participants_with_results(
        self, mock_filter, mock_cohort_program_modules, mock_participant
    ):
        """
        get_start_module_participants should return a list of participants with true is_weight flag
        when those participants have metadata linked.
        """

        today = pendulum.today("UTC")
        start_of_day = today.start_of("day")
        end_of_day = today.end_of("day")

        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[mock_cohort_program_modules])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        cohort = MagicMock()
        cohort.participants = [mock_participant]
        mock_cohort_program_modules.cohort = cohort

        result = await get_start_module_participants()

        mock_filter.assert_called_once_with(
            started_at__gte=start_of_day, started_at__lt=end_of_day
        )
        mock_filter.return_value.prefetch_related.assert_called_once_with(
            "cohort__participants__participant_meta"
        )

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == "<EMAIL>"
        assert result[0].first_name == "Test"
        assert result[0].last_name == "User"
        assert result[0].is_weight is True

    @pytest.mark.asyncio
    @patch.object(CohortProgramModules, "filter", autospec=True)
    async def test_get_start_module_participants_no_meta(
        self, mock_filter, mock_cohort_program_modules, mock_participant
    ):
        """
        get_start_module_participants should return a list of participants with false is_weight flag
        when those participants have no metadata linked.
        """
        mock_participant.participant_meta = []
        mock_participant.status = ParticipantStatus.ACTIVE

        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[mock_cohort_program_modules])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        cohort = MagicMock()
        cohort.participants = [mock_participant]
        mock_cohort_program_modules.cohort = cohort

        result = await get_start_module_participants()

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].is_weight is False

    @pytest.mark.asyncio
    @patch.object(CohortProgramModules, "filter", autospec=True)
    async def test_get_start_module_participants_no_results(self, mock_filter):
        """
        get_start_module_participants should return None if there are no participants found.
        """
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_start_module_participants()

        assert result is None

    @pytest.mark.asyncio
    @patch.object(CohortProgramModules, "filter", autospec=True)
    async def test_filters_out_inactive_participants(
        self, mock_filter, mock_cohort_program_modules, mock_participant
    ):
        """
        get_start_module_participants should filter out inactive participants
        """
        inactive = MagicMock()
        inactive.status = ParticipantStatus.DELETED
        inactive.email = "<EMAIL>"
        inactive.first_name = "No"
        inactive.last_name = "Pe"
        inactive.participant_meta = []

        active = mock_participant
        active.status = ParticipantStatus.ACTIVE
        active.participant_meta = []

        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[mock_cohort_program_modules])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        cohort = MagicMock()
        cohort.participants = [active, inactive]
        mock_cohort_program_modules.cohort = cohort

        result = await get_start_module_participants()
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == active.email

    @pytest.mark.asyncio
    @patch.object(CohortProgramModules, "filter", autospec=True)
    async def test_empty_cohort_returns_none(
        self, mock_filter, mock_cohort_program_modules
    ):
        """
        get_start_module_participants should return None if the cohort has no participants
        """
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[mock_cohort_program_modules])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        mock_cohort_program_modules.cohort = MagicMock(participants=[])

        result = await get_start_module_participants()
        assert result is None

    @pytest.mark.asyncio
    @patch.object(CohortProgramModules, "filter", autospec=True)
    async def test_meta_without_is_weight_defaults_false(
        self, mock_filter, mock_cohort_program_modules, mock_participant
    ):
        """
        get_start_module_participants should default is_weight to False
        """
        participant = mock_participant
        participant.status = ParticipantStatus.ACTIVE
        meta = MagicMock()
        meta.metadata = {}
        participant.participant_meta = [meta]

        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[mock_cohort_program_modules])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        cohort = MagicMock()
        cohort.participants = [participant]
        mock_cohort_program_modules.cohort = cohort

        result = await get_start_module_participants()
        assert result[0].is_weight is False


class TestGetParticipantsNoWeightActivity:
    @pytest.fixture
    def base_participant(self):
        p = MagicMock()
        p.email = "<EMAIL>"
        p.first_name = "Test"
        p.last_name = "User"
        return p

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_get_participants_no_weight_activity_with_results(
        self, mock_all, base_participant
    ):
        """
        get_participants_no_weight_activity should return a list of participants with is_weight flag
        pulled from metadata.
        """
        target_date = pendulum.now("UTC").subtract(days=7)
        start_of_day = target_date.start_of("day")

        p = base_participant
        meta = MagicMock()
        meta.metadata = {"is_weight": True}
        p.participant_meta = [meta]
        activity = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT,
            created_at=start_of_day.add(hours=12),
        )
        p.activities = [activity]

        mock_prefetch = AsyncMock(return_value=[p])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        mock_all.return_value.prefetch_related.assert_called_once_with(
            "participant_meta", "activities"
        )
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == p.email
        assert result[0].is_weight is True

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_get_participants_no_weight_activity_no_meta(
        self, mock_all, base_participant
    ):
        """
        get_participants_no_weight_activity should return a list of participants with false is_weight flag
        when those participants have no metadata linked.
        """
        target_date = pendulum.now("UTC").subtract(days=7)
        start_of_day = target_date.start_of("day")

        p = base_participant
        p.participant_meta = []
        activity = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT,
            created_at=start_of_day.add(hours=12),
        )
        p.activities = [activity]

        mock_prefetch = AsyncMock(return_value=[p])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].is_weight is False

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_get_participants_no_weight_activity_outside_range(
        self, mock_all, base_participant
    ):
        """
        get_participants_no_weight_activity should return None if the last weight activity
        was outside the 7-day range.
        """
        target_date = pendulum.now("UTC").subtract(days=7)
        start_of_day = target_date.start_of("day")

        p = base_participant
        activity = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT,
            created_at=start_of_day.subtract(days=1),
        )
        p.activities = [activity]

        mock_prefetch = AsyncMock(return_value=[p])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert result is None

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_get_participants_no_weight_activity_no_activities(
        self, mock_all, base_participant
    ):
        """
        get_participants_no_weight_activity should return None if the participant has no activities.
        """
        p = base_participant
        p.activities = []

        mock_prefetch = AsyncMock(return_value=[p])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert result is None

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_get_participants_no_weight_activity_no_participants(self, mock_all):
        """
        get_participants_no_weight_activity should return None if there are no participants found.
        """
        mock_prefetch = AsyncMock(return_value=[])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert result is None

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_mix_of_participants(self, mock_all):
        """
        get_participants_no_weight_activity should return only participants
        that have weight activity in 7 day range.
        """
        # one in-range, one out-of-range
        target_date = pendulum.now("UTC").subtract(days=7)
        sod = target_date.start_of("day")
        # in-range
        p1 = MagicMock()
        p1.email = "<EMAIL>"
        p1.first_name = "In"
        p1.last_name = "Range"
        m1 = MagicMock()
        m1.metadata = {"is_weight": True}
        p1.participant_meta = [m1]
        a1 = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT, created_at=sod.add(hours=1)
        )
        p1.activities = [a1]
        # out-of-range
        p2 = MagicMock()
        p2.email = "<EMAIL>"
        p2.first_name = "Out"
        p2.last_name = "Range"
        m2 = MagicMock()
        m2.metadata = {"is_weight": True}
        p2.participant_meta = [m2]
        a2 = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT,
            created_at=sod.subtract(days=1),
        )
        p2.activities = [a2]
        mock_prefetch = AsyncMock(return_value=[p1, p2])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == "<EMAIL>"

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_multiple_weight_activities(self, mock_all):
        """
        get_participants_no_weight_activity should return a list of participants
        with multiple weight activities, but only the most recent one is considered.
        """
        target_date = pendulum.now("UTC").subtract(days=7)
        sod = target_date.start_of("day")
        p = MagicMock()
        p.email = "<EMAIL>"
        p.first_name = "Multi"
        p.last_name = "Test"
        m = MagicMock()
        m.metadata = {"is_weight": True}
        p.participant_meta = [m]
        old = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT,
            created_at=sod.subtract(days=2),
        )
        recent = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT, created_at=sod.add(hours=2)
        )
        p.activities = [old, recent]
        mock_prefetch = AsyncMock(return_value=[p])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == "<EMAIL>"

    @pytest.mark.asyncio
    @patch.object(Participant, "all", autospec=True)
    async def test_meta_without_is_weight_defaults_false(self, mock_all):
        """
        get_participants_no_weight_activity should default is_weight to False
        when metadata is empty.
        """
        target_date = pendulum.now("UTC").subtract(days=7)
        sod = target_date.start_of("day")
        p = MagicMock()
        p.email = "<EMAIL>"
        p.first_name = "No"
        p.last_name = "Meta"
        m = MagicMock()
        m.metadata = {}
        p.participant_meta = [m]
        a = MagicMock(
            activity_type=ParticipantActivityEnum.WEIGHT, created_at=sod.add(hours=3)
        )
        p.activities = [a]
        mock_prefetch = AsyncMock(return_value=[p])
        mock_all.return_value.prefetch_related = mock_prefetch

        result = await get_participants_no_weight_activity()
        assert result[0].is_weight is False


class TestGetParticipants:
    @pytest.fixture
    def mock_participant(self):
        participant = MagicMock()
        participant.email = "<EMAIL>"
        participant.first_name = "Test"
        participant.last_name = "User"
        meta = MagicMock()
        meta.metadata = {"is_weight": True}
        participant.participant_meta = [meta]
        return participant

    @pytest.mark.asyncio
    @patch.object(Participant, "filter", autospec=True)
    async def test_get_participants_with_results(self, mock_filter, mock_participant):
        """
        get_participants should return a list of participants.
        """
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[mock_participant])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants(["test_id"])

        mock_filter.assert_called_once_with(id__in=["test_id"])
        mock_filter.return_value.prefetch_related.assert_called_once_with(
            "participant_meta"
        )

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == "<EMAIL>"
        assert result[0].is_weight is True

    @pytest.mark.asyncio
    @patch.object(Participant, "filter", autospec=True)
    async def test_get_participants_no_results(self, mock_filter):
        """
        get_participants should return None if there are no participants found.
        """
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants(["test_id"])
        assert result is None


class TestGetParticipantsWithClassTomorrow:
    @pytest.fixture
    def mock_booking(self):
        booking = MagicMock()

        participant = MagicMock()
        participant.email = "<EMAIL>"
        participant.first_name = "Test"
        participant.last_name = "User"

        booking.participant = participant

        return booking

    @pytest.mark.asyncio
    @patch.object(Booking, "filter", autospec=True)
    async def test_get_participants_with_class_tomorrow(
        self, mock_filter, mock_booking
    ):
        """
        get_participants_with_class_tomorrow should return a list of participants
        who have classes scheduled tomorrow.
        """
        timezone = "America/Los_Angeles"

        mock_prefetch = AsyncMock()
        mock_all = AsyncMock()
        mock_all.return_value = [mock_booking]
        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants_with_class_tomorrow(timezone=timezone)

        mock_filter.assert_called_once()
        mock_filter.return_value.prefetch_related.assert_called_once_with(
            "live_session", "participant"
        )

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == "<EMAIL>"
        assert result[0].first_name == "Test"
        assert result[0].last_name == "User"

    @pytest.mark.asyncio
    @patch.object(Booking, "filter", autospec=True)
    async def test_get_participants_with_class_tomorrow_multiple_bookings_same_participant(
        self, mock_filter, mock_booking
    ):
        """
        get_participants_with_class_tomorrow should return a list of unique participants
        when multiple bookings are made for the same participant.
        """
        mock_booking2 = MagicMock()
        mock_booking2.participant = mock_booking.participant

        mock_prefetch = AsyncMock()
        mock_all = AsyncMock()
        mock_all.return_value = [mock_booking, mock_booking2]
        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants_with_class_tomorrow()

        assert isinstance(result, list)
        assert len(result) == 1  # Should deduplicate participants
        assert result[0].email == "<EMAIL>"

    @pytest.mark.asyncio
    @patch.object(Booking, "filter", autospec=True)
    async def test_get_participants_with_class_tomorrow_no_bookings(self, mock_filter):
        """
        get_participants_with_class_tomorrow should return an empty list if there are no bookings.
        """
        mock_prefetch = AsyncMock()
        mock_all = AsyncMock()
        mock_all.return_value = []
        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants_with_class_tomorrow()

        assert result == []

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_filter_arguments(self, mock_filter, mock_now):
        """
        Test that the filter arguments are set correctly for the Booking filter.
        """
        timezone = "America/Los_Angeles"
        now = pendulum.datetime(2023, 1, 2, 8, 0, 0, tz=timezone)
        mock_now.return_value = now
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        await get_participants_with_class_tomorrow(timezone=timezone)

        tomorrow_start = now.add(days=1).start_of("day")
        tomorrow_end = tomorrow_start.end_of("day")
        tomorrow_start_utc = tomorrow_start.in_timezone("UTC")
        tomorrow_end_utc = tomorrow_end.in_timezone("UTC")

        mock_filter.assert_called_once_with(
            live_session__meeting_start_time__gte=tomorrow_start_utc,
            live_session__meeting_start_time__lt=tomorrow_end_utc,
            status=BookingStatusEnum.BOOKED,
        )

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_timezone_edge(self, mock_filter, mock_now):
        """
        Test that the filter arguments are set correctly for the Booking filter
        when the time is at the edge of a timezone change.
        """
        timezone = "America/Los_Angeles"
        now = pendulum.datetime(2023, 1, 1, 23, 30, 0, tz=timezone)
        mock_now.return_value = now
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        await get_participants_with_class_tomorrow(timezone=timezone)

        tomorrow_start = now.add(days=1).start_of("day")
        tomorrow_end = tomorrow_start.end_of("day")
        tomorrow_start_utc = tomorrow_start.in_timezone("UTC")
        tomorrow_end_utc = tomorrow_end.in_timezone("UTC")

        mock_filter.assert_called_once_with(
            live_session__meeting_start_time__gte=tomorrow_start_utc,
            live_session__meeting_start_time__lt=tomorrow_end_utc,
            status=BookingStatusEnum.BOOKED,
        )


class TestGetParticipantsWithClassInNextHour:
    @pytest.fixture
    def mock_booking(self):
        booking = MagicMock()

        participant = MagicMock()
        participant.email = "<EMAIL>"
        participant.first_name = "Test"
        participant.last_name = "User"

        booking.participant = participant

        return booking

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_get_participants_with_class_in_next_hour(
        self, mock_filter, mock_now, mock_booking
    ):
        """
        get_participants_with_class_in_next_hour should return a list of participants
        who have classes scheduled within the next hour.
        """
        now = pendulum.datetime(2023, 1, 1, 10, 12, 0)
        mock_now.return_value = now

        mock_prefetch = AsyncMock()
        mock_all = AsyncMock()
        mock_all.return_value = [mock_booking]
        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants_with_class_in_next_hour()

        args = mock_filter.call_args[1]
        assert args["status"] == BookingStatusEnum.BOOKED

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0].email == "<EMAIL>"
        assert result[0].first_name == "Test"
        assert result[0].last_name == "User"

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_get_participants_with_class_in_next_hour_multiple_bookings_same_participant(
        self, mock_filter, mock_now, mock_booking
    ):
        """
        get_participants_with_class_in_next_hour should return a list of unique participants
        when multiple bookings are made for the same participant.
        """
        now = pendulum.datetime(2023, 1, 1, 10, 12, 0)
        mock_now.return_value = now

        mock_booking2 = MagicMock()
        mock_booking2.participant = mock_booking.participant

        # Create AsyncMock for chained async calls
        mock_prefetch = AsyncMock()
        mock_all = AsyncMock()
        mock_all.return_value = [mock_booking, mock_booking2]
        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants_with_class_in_next_hour()

        assert isinstance(result, list)
        assert len(result) == 1  # Should deduplicate participants
        assert result[0].email == "<EMAIL>"

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_get_participants_with_class_in_next_hour_no_bookings(
        self, mock_filter, mock_now
    ):
        now = pendulum.datetime(2023, 1, 1, 10, 12, 0)
        mock_now.return_value = now

        mock_prefetch = AsyncMock()
        mock_all = AsyncMock()
        mock_all.return_value = []
        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        result = await get_participants_with_class_in_next_hour()

        assert result == []

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_exact_5_minute_boundary(self, mock_filter, mock_now):
        """
        Test that the filter arguments are set correctly for the Booking filter
        when the time is exactly on a 5-minute boundary.
        """
        now = pendulum.datetime(2023, 1, 1, 10, 15, 30)
        mock_now.return_value = now
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        await get_participants_with_class_in_next_hour()

        boundary = now.replace(minute=15, second=0, microsecond=0)
        start = boundary.add(minutes=60)
        end = boundary.add(minutes=65)
        mock_filter.assert_called_once_with(
            live_session__meeting_start_time__gte=start,
            live_session__meeting_start_time__lt=end,
            status=BookingStatusEnum.BOOKED,
        )

    @pytest.mark.asyncio
    @patch("ciba_participant.notifications.push.queries.pendulum.now")
    @patch.object(Booking, "filter", autospec=True)
    async def test_day_wrap_around(self, mock_filter, mock_now):
        """
        Test that the filter arguments are set correctly for the Booking filter
        when the time wraps around to the next day.
        """
        now = pendulum.datetime(2023, 1, 1, 23, 58, 0)
        mock_now.return_value = now
        mock_prefetch = AsyncMock()
        mock_prefetch.all = AsyncMock(return_value=[])
        mock_filter.return_value.prefetch_related = MagicMock(
            return_value=mock_prefetch
        )

        await get_participants_with_class_in_next_hour()

        boundary = now.replace(minute=(58 // 5) * 5, second=0, microsecond=0)
        start = boundary.add(minutes=60)
        end = boundary.add(minutes=65)
        mock_filter.assert_called_once_with(
            live_session__meeting_start_time__gte=start,
            live_session__meeting_start_time__lt=end,
            status=BookingStatusEnum.BOOKED,
        )
