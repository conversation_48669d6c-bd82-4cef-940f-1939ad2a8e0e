import uuid
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from contextlib import asynccontextmanager

import pendulum
import pytest
from freezegun import freeze_time

from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityCategory,
    ParticipantActivityDevice,
    ActivityUnit,
)
from ciba_participant.notifications.email.data_models import ScaleToParticpant
from ciba_participant.notifications.email.send_grid_email import (
    get_participants_for_last_24_hours_csv,
    process_participant_for_csv,
    get_latest_solera_participant,
    get_participant_metadata,
    extract_address_data,
    get_participants_in_cohorts_ending_in_28_days,
    EmailHandler,
    send_cohorts_ending_tomorrow,
    generate_cohort_ending_tomorrow_csv_file,
    get_cohorts_ending_tomorrow_csv,
)
from ciba_participant.participant.models import (
    ParticipantStatus,
)
from ciba_participant.cohort.models import (
    <PERSON><PERSON><PERSON>,
    CohortMembers,
    CohortMembershipStatus,
    CohortStatusEnum,
)


# Test data constants
TEST_EMAIL = "<EMAIL>"
TEST_FIRST_NAME = "Test"
TEST_LAST_NAME = "User"
TEST_PHONE = "************"
TEST_PROGRAM_ID = "NDPP"
TEST_WEIGHT = "180"
TEST_ADDRESS = {
    "street1": "123 Main St",
    "street2": "Apt 4B",
    "zipCode": "12345",
    "city": "Test City",
    "state": "TS",
}

# Cohort ending email test constants
TEST_COHORT_ID = uuid.uuid4()
TEST_PROGRAM_NAME = "AscendWell Program"
TEST_END_DATE = "2024-02-15"
COHORT_ENDING_TEMPLATE_ID = "d-35d3c87d59ad4ab0ac8e5eef28e966e1"
COHORT_ENDED_TEMPLATE_ID = "d-b5bf2df0cdb449359e50080529f345db"

# Cohorts ending tomorrow test constants
TEST_COHORT_NAME = "Test Cohort Tomorrow"
TEST_COHORT_START_DATE = "2024-01-15"
TEST_TOMORROW_DATE = "2024-01-16"
TEST_CSV_HEADERS = ["cohort_name", "program_name", "cohort_start_date", "cohort_end_date"]


@pytest.fixture
def mock_participant():
    """Create a mock participant for testing."""
    participant = MagicMock()
    participant.id = uuid.uuid4()
    participant.email = TEST_EMAIL
    participant.first_name = TEST_FIRST_NAME
    participant.last_name = TEST_LAST_NAME
    participant.status = ParticipantStatus.ACTIVE
    participant.is_test = False
    return participant


@pytest.fixture
def mock_solera_participant():
    """Create a mock solera participant for testing."""
    solera_participant = MagicMock()
    solera_participant.id = uuid.uuid4()
    solera_participant.solera_program_id = TEST_PROGRAM_ID
    solera_participant.created_at = pendulum.now().subtract(days=1)
    return solera_participant


@pytest.fixture
def mock_metadata():
    """Create mock metadata for testing."""
    return {
        "address": TEST_ADDRESS,
        "phone_number": TEST_PHONE,
        "weight": TEST_WEIGHT,
    }


@pytest.fixture
def mock_participant_meta(mock_metadata):
    """Create a mock participant meta for testing."""
    participant_meta = MagicMock()
    participant_meta.id = uuid.uuid4()
    participant_meta.metadata = mock_metadata
    participant_meta.created_at = pendulum.now().subtract(days=1)
    return participant_meta


@pytest.fixture
def mock_activity(mock_participant):
    """Create a mock participant activity for testing."""
    activity = MagicMock()
    activity.id = uuid.uuid4()
    activity.participant = mock_participant
    activity.activity_type = ParticipantActivityEnum.ENROLL
    activity.created_at = pendulum.now()
    activity.value = "enrolled"
    activity.unit = ActivityUnit.ACTION
    activity.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    activity.activity_category = ParticipantActivityCategory.ACTIVITY
    return activity


@pytest.fixture
def mock_scale_participant():
    """Create a mock ScaleToParticpant for testing."""
    return ScaleToParticpant(
        created_at=pendulum.now().strftime("%Y-%m-%d %H:%M:%S"),
        email=TEST_EMAIL,
        phone_number=TEST_PHONE,
        first_name=TEST_FIRST_NAME,
        last_name=TEST_LAST_NAME,
        street1=TEST_ADDRESS["street1"],
        street2=TEST_ADDRESS["street2"],
        zipCode=TEST_ADDRESS["zipCode"],
        city=TEST_ADDRESS["city"],
        state=TEST_ADDRESS["state"],
        solera_program_id=TEST_PROGRAM_ID,
        weight=TEST_WEIGHT,
        status="active",
        re_enrolled=True,
    )


@pytest.fixture
def mock_cohort():
    """Create a mock cohort for testing."""
    cohort = MagicMock()
    cohort.id = TEST_COHORT_ID
    cohort.name = "Test Cohort"
    cohort.status = CohortStatusEnum.ACTIVE

    # Mock program relationship
    program = MagicMock()
    program.title = TEST_PROGRAM_NAME
    cohort.program = program

    # Mock end_date property
    async def mock_end_date():
        return pendulum.parse(TEST_END_DATE)

    cohort.end_date = mock_end_date()

    return cohort


@pytest.fixture
def mock_cohort_member(mock_participant):
    """Create a mock cohort member for testing."""
    member = MagicMock()
    member.id = uuid.uuid4()
    member.cohort_id = TEST_COHORT_ID
    member.participant = mock_participant
    member.status = CohortMembershipStatus.ACTIVE
    return member


@pytest.fixture
def mock_email_handler():
    """Create a mock EmailHandler for testing."""
    handler = MagicMock(spec=EmailHandler)
    handler.generate_message = AsyncMock()
    handler.send_email = AsyncMock()
    return handler


@pytest.fixture
def mock_cohort_tomorrow():
    """Create a mock cohort ending tomorrow for testing."""
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = TEST_COHORT_NAME
    cohort.status = CohortStatusEnum.ACTIVE
    cohort.started_at = pendulum.parse(TEST_COHORT_START_DATE)

    # Mock program relationship
    program = MagicMock()
    program.title = TEST_PROGRAM_NAME
    cohort.program = program

    # Mock end_date property
    async def mock_end_date():
        return pendulum.parse(TEST_TOMORROW_DATE)

    cohort.end_date = mock_end_date()

    return cohort


@pytest.fixture
def mock_cohort_data():
    """Create mock cohort data for CSV generation."""
    return {
        "cohort_name": TEST_COHORT_NAME,
        "program_name": TEST_PROGRAM_NAME,
        "cohort_start_date": TEST_COHORT_START_DATE,
        "cohort_end_date": TEST_TOMORROW_DATE,
    }


# Context managers for common patch setups
@asynccontextmanager
async def setup_process_participant_patches(
    solera_participant=None,
    metadata=None,
    re_enrollment_status=True,
    address_data="__not_set__",
):
    """Set up common patches for process_participant_for_csv tests."""
    patches = [
        patch(
            "ciba_participant.notifications.email.send_grid_email.get_latest_solera_participant",
            return_value=solera_participant,
        ),
        patch(
            "ciba_participant.notifications.email.send_grid_email.get_participant_metadata",
            return_value=metadata,
        ),
        patch(
            "ciba_participant.notifications.email.send_grid_email.check_re_enrollment_status",
            return_value=re_enrollment_status,
        ),
    ]

    # Add address data patch if explicitly specified (including None)
    if address_data != "__not_set__":
        patches.append(
            patch(
                "ciba_participant.notifications.email.send_grid_email.extract_address_data",
                return_value=address_data,
            )
        )

    # Start all patches
    started_patches = [p.start() for p in patches]

    try:
        yield started_patches
    finally:
        # Stop all patches
        for p in patches:
            p.stop()


@asynccontextmanager
async def setup_cohort_filter_chain(cohorts=None, members=None):
    """Set up mock filter chains for cohort-related tests."""
    cohort_patches = []
    member_patches = []

    # Setup cohort filter chain
    if cohorts is not None:
        cohort_filter_patch = patch.object(Cohort, "filter")
        cohort_patches.append(cohort_filter_patch)
        mock_cohort_filter = cohort_filter_patch.start()

        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=cohorts)

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_cohort_filter.return_value.annotate.return_value = mock_annotate

    # Setup members filter chain
    if members is not None:
        member_filter_patch = patch.object(CohortMembers, "filter")
        member_patches.append(member_filter_patch)
        mock_member_filter = member_filter_patch.start()

        mock_members_prefetch = MagicMock()
        mock_members_all = AsyncMock(return_value=members)

        mock_members_prefetch.all = mock_members_all
        mock_member_filter.return_value.prefetch_related.return_value = (
            mock_members_prefetch
        )

    try:
        yield
    finally:
        # Stop all patches
        for p in cohort_patches + member_patches:
            p.stop()


@asynccontextmanager
async def setup_email_handler_mocks():
    """Set up EmailHandler method mocks."""
    generate_patch = patch.object(
        EmailHandler, "generate_message", new_callable=AsyncMock
    )
    send_patch = patch.object(EmailHandler, "send_email", new_callable=AsyncMock)

    mock_generate = generate_patch.start()
    mock_send = send_patch.start()

    try:
        yield {"generate": mock_generate, "send": mock_send}
    finally:
        generate_patch.stop()
        send_patch.stop()


# Helper functions for tests
def assert_address_data(address_data, expected_address=None):
    """Assert that address data matches expected values."""
    if expected_address is None:
        assert address_data is None
    else:
        assert address_data is not None
        for key, value in expected_address.items():
            assert address_data[key] == value


def create_scale_participant_data(**overrides):
    """Create ScaleToParticpant test data with optional overrides."""
    default_data = {
        "created_at": "2023-01-01 12:00:00",
        "email": TEST_EMAIL,
        "phone_number": TEST_PHONE,
        "first_name": TEST_FIRST_NAME,
        "last_name": TEST_LAST_NAME,
        "street1": TEST_ADDRESS["street1"],
        "street2": TEST_ADDRESS["street2"],
        "zipCode": TEST_ADDRESS["zipCode"],
        "city": TEST_ADDRESS["city"],
        "state": TEST_ADDRESS["state"],
        "solera_program_id": TEST_PROGRAM_ID,
        "weight": TEST_WEIGHT,
        "status": "active",
        "re_enrolled": True,
    }
    default_data.update(overrides)
    return ScaleToParticpant(**default_data)


def assert_email_generation_call(
    mock_generate,
    expected_email,
    expected_subject,
    expected_template_id,
    expected_data=None,
    call_index=0,
):
    """Assert that email generation was called with expected parameters."""
    if call_index == 0 and mock_generate.call_count == 1:
        mock_generate.assert_called_once()
        call_args = mock_generate.call_args
    else:
        assert mock_generate.call_count > call_index, (
            f"Expected at least {call_index + 1} calls, got {mock_generate.call_count}"
        )
        call_args = mock_generate.call_args_list[call_index]

    assert call_args[1]["to_emails"] == expected_email
    assert call_args[1]["subject"] == expected_subject
    assert call_args[1]["template_id"] == expected_template_id

    if expected_data:
        for key, value in expected_data.items():
            assert call_args[1]["dynamic_template_data"][key] == value


# Parameterized tests for extract_address_data
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "metadata,expected_result",
    [
        # Valid address
        ({"address": TEST_ADDRESS}, TEST_ADDRESS),
        # Missing required field
        (
            {
                "address": {
                    "street1": "123 Main St",
                    # Missing zipCode
                    "city": "Test City",
                    "state": "TS",
                }
            },
            None,
        ),
        # Missing address key
        ({"phone_number": TEST_PHONE}, None),
        # None metadata
        (None, None),
    ],
    ids=["valid", "missing_field", "missing_address", "none_metadata"],
)
async def test_extract_address_data(metadata, expected_result):
    """Test extract_address_data with various inputs."""
    # Act
    result = extract_address_data(metadata)

    # Assert
    assert_address_data(result, expected_result)


# Helper function for testing get_latest_x functions
async def _test_get_latest_record(participant, get_function, expected_result):
    """Generic test for get_latest_x functions."""
    # Act
    result = await get_function(participant)

    # Assert
    assert result == expected_result


# Parameterized tests for get_latest_solera_participant
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "solera_records,expected_result",
    [
        # Single record
        (lambda sp: [sp], lambda sp: sp),
        # Multiple records (newest should be returned)
        (
            lambda sp: [MagicMock(created_at=pendulum.now().subtract(days=10)), sp],
            lambda sp: sp,
        ),
        # Empty list
        (lambda _: [], lambda _: None),
    ],
    ids=["single", "multiple", "empty"],
)
async def test_get_latest_solera_participant(
    mock_participant, mock_solera_participant, solera_records, expected_result
):
    """Test get_latest_solera_participant with various inputs."""
    # Arrange
    mock_participant.solera_participant = solera_records(mock_solera_participant)
    expected = expected_result(mock_solera_participant)

    # Act & Assert
    await _test_get_latest_record(
        mock_participant, get_latest_solera_participant, expected
    )


# Parameterized tests for get_participant_metadata
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "meta_records,expected_result",
    [
        # Single record
        (lambda meta: [meta], lambda meta: meta.metadata),
        # Multiple records (newest should be returned)
        (
            lambda meta: [
                MagicMock(
                    created_at=pendulum.now().subtract(days=10),
                    metadata={"old": "data"},
                ),
                meta,
            ],
            lambda meta: meta.metadata,
        ),
        # Empty list
        (lambda _: [], lambda _: None),
    ],
    ids=["single", "multiple", "empty"],
)
async def test_get_participant_metadata(
    mock_participant, mock_participant_meta, meta_records, expected_result
):
    """Test get_participant_metadata with various inputs."""
    # Arrange
    mock_participant.participant_meta = meta_records(mock_participant_meta)
    expected = expected_result(mock_participant_meta)

    # Act
    result = await get_participant_metadata(mock_participant)

    # Assert
    assert result == expected


# Parameterized tests for check_re_enrollment_status
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "solera_records,deleted_email_exists,expected_result",
    [
        # Deleted email exists
        ([MagicMock()], True, True),
        # Multiple solera records
        ([MagicMock(), MagicMock()], False, True),
        # Not re-enrolled
        ([MagicMock()], False, False),
    ],
    ids=["deleted_email", "multiple_solera", "not_re_enrolled"],
)
async def test_check_re_enrollment_status(
    mock_participant, solera_records, deleted_email_exists, expected_result
):
    """Test check_re_enrollment_status with various inputs."""
    # Arrange
    from ciba_participant.notifications.email.send_grid_email import (
        check_re_enrollment_status,
    )

    mock_participant.solera_participant = solera_records

    # Mock the check_re_enrolled_participant function
    with patch(
        "ciba_participant.notifications.email.send_grid_email.check_re_enrolled_participant",
        return_value=deleted_email_exists,
    ):
        # Act
        result = await check_re_enrollment_status(mock_participant)

        # Assert
        assert result is expected_result


@pytest.mark.asyncio
async def test_process_participant_for_csv_success(
    mock_activity, mock_solera_participant, mock_participant_meta
):
    """Test process_participant_for_csv with successful processing."""
    # Arrange & Act
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant,
        metadata=mock_participant_meta.metadata,
        re_enrollment_status=True,
    ):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is not None
        assert result.email == mock_activity.participant.email
        assert result.first_name == mock_activity.participant.first_name
        assert result.last_name == mock_activity.participant.last_name
        assert result.solera_program_id == mock_solera_participant.solera_program_id
        assert result.re_enrolled is True


@pytest.mark.asyncio
async def test_process_participant_for_csv_test_participant(mock_activity):
    """Test process_participant_for_csv with test participant."""
    # Arrange
    mock_activity.participant.is_test = True

    # Act
    result = await process_participant_for_csv(mock_activity)

    # Assert
    assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_no_solera_participant(mock_activity):
    """Test process_participant_for_csv with no solera participant."""
    # Arrange & Act
    async with setup_process_participant_patches(solera_participant=None):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_no_metadata(
    mock_activity, mock_solera_participant
):
    """Test process_participant_for_csv with no metadata."""
    # Arrange & Act
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant, metadata=None
    ):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_invalid_address(
    mock_activity, mock_solera_participant, mock_participant_meta
):
    """Test process_participant_for_csv with invalid address."""
    # Arrange & Act
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant,
        metadata=mock_participant_meta.metadata,
        address_data=None,
    ):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_exception(
    mock_activity, mock_solera_participant, mock_participant_meta
):
    """Test process_participant_for_csv with an exception."""
    # Arrange
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant,
        metadata=mock_participant_meta.metadata,
    ):
        # Override the re-enrollment status patch to raise exception
        with patch(
            "ciba_participant.notifications.email.send_grid_email.check_re_enrollment_status",
            side_effect=Exception("Test exception"),
        ):
            # Act
            result = await process_participant_for_csv(mock_activity)

            # Assert
            assert result is None


@pytest.mark.asyncio
@freeze_time("2023-01-01 12:00:00")
@patch(
    "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
    return_value=Path("/tmp/test.csv"),
)
@patch(
    "ciba_participant.notifications.email.send_grid_email.process_participant_for_csv"
)
@patch(
    "ciba_participant.notifications.email.send_grid_email.ParticipantActivity.filter"
)
async def test_get_participants_for_last_24_hours_csv_success(
    mock_filter, mock_process, mock_generate_csv, mock_activity
):
    """Test get_participants_for_last_24_hours_csv with successful processing."""
    # Arrange
    mock_scale_participant = create_scale_participant_data()

    # Setup the mock filter chain
    mock_prefetch = AsyncMock()
    mock_prefetch.all.return_value = [mock_activity]
    mock_filter.return_value.prefetch_related.return_value = mock_prefetch

    # Setup mock_process return value
    mock_process.return_value = mock_scale_participant

    # Act
    result = await get_participants_for_last_24_hours_csv()

    # Assert
    assert result == Path("/tmp/test.csv")
    mock_filter.assert_called_once()
    mock_filter.return_value.prefetch_related.assert_called_once_with(
        "participant__solera_participant", "participant__participant_meta"
    )
    mock_generate_csv.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2023-01-01 12:00:00")
@patch(
    "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
    return_value=Path("/tmp/test.csv"),
)
async def test_get_participants_for_last_24_hours_csv_empty(mock_generate_csv):
    """Test get_participants_for_last_24_hours_csv with no participants."""
    # Import the module

    # Arrange
    with (
        patch(
            "ciba_participant.notifications.email.send_grid_email.ParticipantActivity.filter",
        ) as mock_filter,
        patch(
            "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
            return_value=Path("/tmp/test.csv"),
        ),
    ):
        # Setup the mock filter chain
        from ciba_participant.notifications.email.send_grid_email import (
            get_participants_for_last_24_hours_csv,
        )

        mock_prefetch = AsyncMock()
        mock_prefetch.all.return_value = []
        mock_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_for_last_24_hours_csv()

        # Assert
        assert result == Path("/tmp/test.csv")
        mock_filter.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2023-01-01 12:00:00")
@patch(
    "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
    return_value=Path("/tmp/test.csv"),
)
@patch(
    "builtins.open",
    mock_open(),
)
async def test_get_participants_for_last_24_hours_csv_exception(mock_generate_csv):
    """Test get_participants_for_last_24_hours_csv with an exception."""
    # Import the module

    result = await get_participants_for_last_24_hours_csv()

    # Assert
    assert result is not None
    assert str(result).startswith("/tmp/empty_participants_")


# Tests for cohort ending email functionality
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_in_cohorts_ending_in_28_days_success(
    mock_cohort, mock_cohort_member
):
    """Test get_participants_in_cohorts_ending_in_28_days with successful data retrieval."""
    # Arrange & Act
    async with setup_cohort_filter_chain(
        cohorts=[mock_cohort], members=[mock_cohort_member]
    ):
        result = await get_participants_in_cohorts_ending_in_28_days()

        # Assert
        assert len(result) == 1
        assert result[0]["email"] == TEST_EMAIL
        assert result[0]["first_name"] == TEST_FIRST_NAME
        assert result[0]["program_name"] == TEST_PROGRAM_NAME


@pytest.mark.asyncio
async def test_get_participants_in_cohorts_ending_in_28_days_no_cohorts():
    """Test get_participants_in_cohorts_ending_in_28_days with no cohorts ending."""
    # Arrange & Act
    async with setup_cohort_filter_chain(cohorts=[]):
        result = await get_participants_in_cohorts_ending_in_28_days()

        # Assert
        assert result == []


@pytest.mark.asyncio
async def test_get_participants_in_cohorts_ending_in_28_days_inactive_participant(
    mock_cohort, mock_cohort_member
):
    """Test get_participants_in_cohorts_ending_in_28_days with inactive participant."""
    # Arrange
    mock_cohort_member.participant.status = ParticipantStatus.DELETED

    # Act
    async with setup_cohort_filter_chain(
        cohorts=[mock_cohort], members=[mock_cohort_member]
    ):
        result = await get_participants_in_cohorts_ending_in_28_days()

        # Assert
        assert result == []


@pytest.mark.asyncio
async def test_send_cohort_ending_in_28_days_email_success():
    """Test send_cohort_ending_in_28_days_email with successful email sending."""
    # Arrange
    mock_participants = [
        {
            "email": TEST_EMAIL,
            "first_name": TEST_FIRST_NAME,
            "program_name": TEST_PROGRAM_NAME,
            "end_date": TEST_END_DATE,
        },
        {
            "email": "<EMAIL>",
            "first_name": "Test2",
            "program_name": "Another Program",
            "end_date": "2024-02-20",
        },
    ]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_participants_in_cohorts_ending_in_28_days",
        return_value=mock_participants,
    ):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ending_in_28_days_email()

            # Assert
            assert mocks["generate"].call_count == 2
            assert mocks["send"].call_count == 2

            # Verify first email call
            assert_email_generation_call(
                mocks["generate"],
                TEST_EMAIL,
                "Your 28-Day AscendWell Countdown",
                COHORT_ENDING_TEMPLATE_ID,
                {
                    "first_name": TEST_FIRST_NAME,
                    "program_name": TEST_PROGRAM_NAME,
                    "end_date": TEST_END_DATE,
                },
            )


@pytest.mark.asyncio
async def test_send_cohort_ending_in_28_days_email_no_participants():
    """Test send_cohort_ending_in_28_days_email with no participants."""
    # Arrange
    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_participants_in_cohorts_ending_in_28_days",
        return_value=[],
    ):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ending_in_28_days_email()

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_cohort_ending_in_28_days_email_send_error():
    """Test send_cohort_ending_in_28_days_email with email sending error."""
    # Arrange
    mock_participants = [
        {
            "email": TEST_EMAIL,
            "first_name": TEST_FIRST_NAME,
            "program_name": TEST_PROGRAM_NAME,
            "end_date": TEST_END_DATE,
        }
    ]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_participants_in_cohorts_ending_in_28_days",
        return_value=mock_participants,
    ):
        async with setup_email_handler_mocks() as mocks:
            # Override send to raise exception
            mocks["send"].side_effect = Exception("Send error")

            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert - Should raise exception
            with pytest.raises(Exception, match="Send error"):
                await email_handler.send_cohort_ending_in_28_days_email()

            # Verify methods were called before error
            mocks["generate"].assert_called_once()
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_success(
    mock_cohort_member, mock_solera_participant
):
    """Test send_cohort_ended_email with successful email sending."""
    # Arrange
    cohort_id = TEST_COHORT_ID
    mock_cohort_member.participant.solera_participant = [mock_solera_participant]
    mock_solera_participant.status = ParticipantStatus.ACTIVE

    async with setup_cohort_filter_chain(members=[mock_cohort_member]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            assert_email_generation_call(
                mocks["generate"],
                TEST_EMAIL,
                "You've reached the AscendWell finish line. Here's what's next...",
                COHORT_ENDED_TEMPLATE_ID,
                {
                    "first_name": TEST_FIRST_NAME,
                    "program_name": TEST_PROGRAM_ID,
                },
            )
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_no_members():
    """Test send_cohort_ended_email with no cohort members."""
    # Arrange
    cohort_id = TEST_COHORT_ID

    async with setup_cohort_filter_chain(members=[]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_no_active_solera_participant(mock_cohort_member):
    """Test send_cohort_ended_email with no active solera participant."""
    # Arrange
    cohort_id = TEST_COHORT_ID
    inactive_solera = MagicMock()
    inactive_solera.status = ParticipantStatus.DELETED
    mock_cohort_member.participant.solera_participant = [inactive_solera]

    async with setup_cohort_filter_chain(members=[mock_cohort_member]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_multiple_members(mock_solera_participant):
    """Test send_cohort_ended_email with multiple cohort members."""
    # Arrange
    cohort_id = TEST_COHORT_ID

    # Create multiple mock members
    member1 = MagicMock()
    member1.participant.email = "<EMAIL>"
    member1.participant.first_name = "Test1"
    member1.participant.solera_participant = [mock_solera_participant]

    member2 = MagicMock()
    member2.participant.email = "<EMAIL>"
    member2.participant.first_name = "Test2"
    member2.participant.solera_participant = [mock_solera_participant]

    mock_solera_participant.status = ParticipantStatus.ACTIVE

    async with setup_cohort_filter_chain(members=[member1, member2]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            assert mocks["generate"].call_count == 2
            assert mocks["send"].call_count == 2


@pytest.mark.asyncio
async def test_send_cohort_ended_email_exception_handling(
    mock_cohort_member, mock_solera_participant
):
    """Test send_cohort_ended_email with exception during email sending."""
    # Arrange
    cohort_id = TEST_COHORT_ID
    mock_cohort_member.participant.solera_participant = [mock_solera_participant]
    mock_solera_participant.status = ParticipantStatus.ACTIVE

    async with setup_cohort_filter_chain(members=[mock_cohort_member]):
        async with setup_email_handler_mocks() as mocks:
            # Override send to raise exception
            mocks["send"].side_effect = Exception("Email error")

            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert - Should raise exception
            with pytest.raises(Exception, match="Email error"):
                await email_handler.send_cohort_ended_email(cohort_id)

            # Verify methods were called before error
            mocks["generate"].assert_called_once()
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_in_cohorts_ending_in_28_days_date_filtering():
    """Test get_participants_in_cohorts_ending_in_28_days with correct date filtering."""
    # Arrange & Act
    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup empty cohort result to focus on filter call verification
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_cohort_filter.return_value.annotate.return_value = mock_annotate

        await get_participants_in_cohorts_ending_in_28_days()

        # Assert - Verify the filter was called with correct parameters
        mock_cohort_filter.assert_called_once()
        call_args = mock_cohort_filter.call_args[1]

        # Verify status filter
        assert call_args["status"] == CohortStatusEnum.ACTIVE.value

        # Verify date range (should be 28 days from frozen time)
        assert "cohort_end_date__gte" in call_args
        assert "cohort_end_date__lte" in call_args


@pytest.mark.asyncio
async def test_get_participants_in_cohorts_ending_in_28_days_database_error():
    """Test get_participants_in_cohorts_ending_in_28_days with database error."""
    # Arrange & Act & Assert
    with patch.object(Cohort, "filter", side_effect=Exception("Database error")):
        with pytest.raises(Exception, match="Database error"):
            await get_participants_in_cohorts_ending_in_28_days()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_invalid_cohort_id():
    """Test send_cohort_ended_email with invalid cohort ID."""
    # Arrange
    invalid_cohort_id = uuid.uuid4()

    async with setup_cohort_filter_chain(members=[]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(invalid_cohort_id)

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


# Tests for generate_cohort_ending_tomorrow_csv_file function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_success(mock_cohort_data):
    """Test generate_cohort_ending_tomorrow_csv_file with successful CSV generation."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [mock_cohort_data]

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer_class.return_value = mock_writer

            # Act
            result = generate_cohort_ending_tomorrow_csv_file(headers, cohorts)

            # Assert
            assert result is not None
            assert "cohorts_ending_2024-01-16.csv" in result
            assert "/tmp/cohorts_ending/2024/01/" in result

            # Verify file operations
            mock_file.assert_called_once()
            mock_writer_class.assert_called_once_with(mock_file.return_value.__enter__.return_value, fieldnames=headers)
            mock_writer.writeheader.assert_called_once()
            mock_writer.writerows.assert_called_once_with(cohorts)


@pytest.mark.asyncio
async def test_generate_cohort_ending_tomorrow_csv_file_empty_headers():
    """Test generate_cohort_ending_tomorrow_csv_file with empty headers."""
    # Arrange
    headers = []
    cohorts = [{"test": "data"}]

    # Act & Assert
    with pytest.raises(ValueError, match="Headers and cohorts cannot be empty"):
        generate_cohort_ending_tomorrow_csv_file(headers, cohorts)


@pytest.mark.asyncio
async def test_generate_cohort_ending_tomorrow_csv_file_empty_cohorts():
    """Test generate_cohort_ending_tomorrow_csv_file with empty cohorts."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = []

    # Act & Assert
    with pytest.raises(ValueError, match="Headers and cohorts cannot be empty"):
        generate_cohort_ending_tomorrow_csv_file(headers, cohorts)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_custom_base_dir(mock_cohort_data):
    """Test generate_cohort_ending_tomorrow_csv_file with custom base directory."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [mock_cohort_data]
    custom_base_dir = "/custom/path"

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer_class.return_value = mock_writer

            # Act
            result = generate_cohort_ending_tomorrow_csv_file(headers, cohorts, custom_base_dir)

            # Assert
            assert result is not None
            assert "cohorts_ending_2024-01-16.csv" in result
            assert "/custom/path/cohorts_ending/2024/01/" in result

            # Verify file operations
            mock_file.assert_called_once()
            mock_writer.writeheader.assert_called_once()
            mock_writer.writerows.assert_called_once_with(cohorts)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_file_write_error(mock_cohort_data):
    """Test generate_cohort_ending_tomorrow_csv_file with file write error."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [mock_cohort_data]

    with patch("builtins.open", side_effect=OSError("Permission denied")):
        # Act & Assert
        with pytest.raises(OSError, match="Failed to create CSV file"):
            generate_cohort_ending_tomorrow_csv_file(headers, cohorts)


@pytest.mark.asyncio
@freeze_time("2024-12-31 23:59:59")
async def test_generate_cohort_ending_tomorrow_csv_file_year_boundary(mock_cohort_data):
    """Test generate_cohort_ending_tomorrow_csv_file crossing year boundary."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [mock_cohort_data]

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer_class.return_value = mock_writer

            # Act
            result = generate_cohort_ending_tomorrow_csv_file(headers, cohorts)

            # Assert
            assert result is not None
            assert "cohorts_ending_2025-01-01.csv" in result
            assert "/tmp/cohorts_ending/2025/01/" in result

            # Verify file operations
            mock_file.assert_called_once()
            mock_writer.writeheader.assert_called_once()
            mock_writer.writerows.assert_called_once_with(cohorts)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_multiple_cohorts():
    """Test generate_cohort_ending_tomorrow_csv_file with multiple cohorts."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [
        {
            "cohort_name": "Cohort 1",
            "program_name": "Program 1",
            "cohort_start_date": "2024-01-01",
            "cohort_end_date": "2024-01-16",
        },
        {
            "cohort_name": "Cohort 2",
            "program_name": "Program 2",
            "cohort_start_date": "2024-01-02",
            "cohort_end_date": "2024-01-16",
        },
    ]

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer_class.return_value = mock_writer

            # Act
            result = generate_cohort_ending_tomorrow_csv_file(headers, cohorts)

            # Assert
            assert result is not None
            assert "cohorts_ending_2024-01-16.csv" in result

            # Verify file operations
            mock_file.assert_called_once()
            mock_writer.writeheader.assert_called_once()
            mock_writer.writerows.assert_called_once_with(cohorts)


# Tests for get_cohorts_ending_tomorrow_csv function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_success(mock_cohort_tomorrow):
    """Test get_cohorts_ending_tomorrow_csv with successful data retrieval and CSV generation."""
    # Arrange
    expected_csv_path = "/tmp/cohorts_ending/2024/01/cohorts_ending_2024-01-16.csv"

    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort_tomorrow])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        with patch(
            "ciba_participant.notifications.email.send_grid_email.generate_cohort_ending_tomorrow_csv_file",
            return_value=expected_csv_path
        ) as mock_generate_csv:
            # Act
            result = await get_cohorts_ending_tomorrow_csv()

            # Assert
            assert result == expected_csv_path

            # Verify database query
            mock_filter.assert_called_once()
            call_args = mock_filter.call_args[1]
            assert call_args["status"] == CohortStatusEnum.ACTIVE.value
            assert "cohort_end_date__gte" in call_args
            assert "cohort_end_date__lte" in call_args

            # Verify CSV generation
            mock_generate_csv.assert_called_once()
            csv_call_args = mock_generate_csv.call_args
            assert csv_call_args[1]["headers"] == TEST_CSV_HEADERS
            assert len(csv_call_args[1]["cohorts"]) == 1
            assert csv_call_args[1]["cohorts"][0]["cohort_name"] == TEST_COHORT_NAME


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_no_cohorts():
    """Test get_cohorts_ending_tomorrow_csv with no cohorts ending tomorrow."""
    # Arrange
    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain to return empty list
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        # Act
        result = await get_cohorts_ending_tomorrow_csv()

        # Assert
        assert result is None

        # Verify database query was made
        mock_filter.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_multiple_cohorts():
    """Test get_cohorts_ending_tomorrow_csv with multiple cohorts ending tomorrow."""
    # Arrange
    cohort1 = MagicMock()
    cohort1.name = "Cohort 1"
    cohort1.started_at = pendulum.parse("2024-01-01")
    cohort1.program.title = "Program 1"
    cohort1.end_date = AsyncMock(return_value=pendulum.parse("2024-01-16"))

    cohort2 = MagicMock()
    cohort2.name = "Cohort 2"
    cohort2.started_at = pendulum.parse("2024-01-02")
    cohort2.program.title = "Program 2"
    cohort2.end_date = AsyncMock(return_value=pendulum.parse("2024-01-16"))

    expected_csv_path = "/tmp/cohorts_ending/2024/01/cohorts_ending_2024-01-16.csv"

    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[cohort1, cohort2])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        with patch(
            "ciba_participant.notifications.email.send_grid_email.generate_cohort_ending_tomorrow_csv_file",
            return_value=expected_csv_path
        ) as mock_generate_csv:
            # Act
            result = await get_cohorts_ending_tomorrow_csv()

            # Assert
            assert result == expected_csv_path

            # Verify CSV generation with multiple cohorts
            mock_generate_csv.assert_called_once()
            csv_call_args = mock_generate_csv.call_args
            assert len(csv_call_args[1]["cohorts"]) == 2
            assert csv_call_args[1]["cohorts"][0]["cohort_name"] == "Cohort 1"
            assert csv_call_args[1]["cohorts"][1]["cohort_name"] == "Cohort 2"


@pytest.mark.asyncio
async def test_get_cohorts_ending_tomorrow_csv_database_error():
    """Test get_cohorts_ending_tomorrow_csv with database error."""
    # Arrange
    with patch.object(Cohort, "filter", side_effect=Exception("Database connection error")):
        # Act
        result = await get_cohorts_ending_tomorrow_csv()

        # Assert
        assert result is None


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_csv_generation_error(mock_cohort_tomorrow):
    """Test get_cohorts_ending_tomorrow_csv with CSV generation error."""
    # Arrange
    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort_tomorrow])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        with patch(
            "ciba_participant.notifications.email.send_grid_email.generate_cohort_ending_tomorrow_csv_file",
            side_effect=Exception("CSV generation failed")
        ):
            # Act
            result = await get_cohorts_ending_tomorrow_csv()

            # Assert
            assert result is None


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_date_filtering():
    """Test get_cohorts_ending_tomorrow_csv with correct date filtering."""
    # Arrange
    with patch.object(Cohort, "filter") as mock_filter:
        # Setup empty cohort result to focus on filter call verification
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        # Act
        await get_cohorts_ending_tomorrow_csv()

        # Assert - Verify the filter was called with correct parameters
        mock_filter.assert_called_once()
        call_args = mock_filter.call_args[1]

        # Verify status filter
        assert call_args["status"] == CohortStatusEnum.ACTIVE.value

        # Verify date range (should be tomorrow from frozen time)
        assert "cohort_end_date__gte" in call_args
        assert "cohort_end_date__lte" in call_args

        # The dates should be for tomorrow (2024-01-16)
        gte_date = call_args["cohort_end_date__gte"]
        lte_date = call_args["cohort_end_date__lte"]

        # Verify it's filtering for tomorrow's date range
        assert gte_date.date() == pendulum.parse("2024-01-16").date()
        assert lte_date.date() == pendulum.parse("2024-01-16").date()
