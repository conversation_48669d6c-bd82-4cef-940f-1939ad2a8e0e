import pendulum
import pytest

from app.helpers.mappers.devices import map_withings_device


@pytest.mark.parametrize(
    "test_dict",
    [
        {
            "deviceid": "test1",
            "type": "scale",
            "last_session_date": 1749229533,
        },
        {
            "deviceid": "test2",
            "type": "tracker",
            "last_session_date": 1749229533,
        },
    ],
)
def test_map_withings_device(test_dict):
    """
    map_withings_device should return a Device object from a dictionary.
    """
    actual_value = map_withings_device(test_dict)

    assert actual_value.id == test_dict["deviceid"]
    assert actual_value.device_type == test_dict["type"]
    assert actual_value.last_synced_at == pendulum.from_timestamp(
        test_dict["last_session_date"]
    )
