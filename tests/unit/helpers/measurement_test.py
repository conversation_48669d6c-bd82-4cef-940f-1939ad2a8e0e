from datetime import datetime

import pytest

from ciba_iot_etl.helpers.measurement import get_date_from_measure

test_date = datetime(year=2024, month=1, day=1)


@pytest.mark.parametrize(
    "test_measure", [({"created_at": test_date}), ({"group_date": test_date})]
)
def test_get_date_from_measure_1(test_measure):
    """
    get_date_from_measure should return the available date field.
    """

    assert test_date == get_date_from_measure(test_measure)
