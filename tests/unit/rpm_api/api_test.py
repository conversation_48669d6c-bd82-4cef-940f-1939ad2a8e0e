from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mock, patch
from uuid import <PERSON><PERSON><PERSON>

import pytest
from httpx import <PERSON>TTP<PERSON>rror, HTTPStatusError, Response, codes

from ciba_participant.rpm_api.api import sync_measures
from ciba_participant.rpm_api.exceptions import RPMCallError

test_participant_id = UUID("8f844654-e2b1-4d75-bbd7-84d492b327a7")
GET_SYNC_DATE = "ciba_participant.rpm_api.api.get_sync_start_date"
POST_METHOD = "ciba_participant.rpm_api.api.AsyncClient.post"


@pytest.mark.asyncio
async def test_sync_measures_with_api_call_error():
    """
    sync_measures should raise an RPMCallError
    when the endpoint call raises an HTTPError.
    """
    test_error = HTTPError("test network error.")

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error),
    ):
        with pytest.raises(RPMCallError):
            await sync_measures(test_participant_id)


@pytest.mark.asyncio
async def test_sync_measures_with_error_in_response():
    """
    sync_measures should raise an RPMCallError
    when the api call return an unsuccessful response.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Invalid device."})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        with pytest.raises(RPMCallError) as expected_error:
            await sync_measures(test_participant_id)

        assert expected_error.value.args[0] == "Invalid device."


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_json, expected_value",
    [
        ({"synced": True}, True),
        ({"synced": False}, False),
    ],
)
async def test_get_latest_data_success(test_json, expected_value):
    """
    get_latest_data should return the latest data from RPM.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_json)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=1749168000),
        patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        actual_value = await sync_measures(test_participant_id)

        assert actual_value.success == expected_value
