import pytest
import pendulum

from httpx import Response, TransportError

from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.extract.transtek_api.common import (
    ACTIVATE_ERROR,
    HOST,
    DEVICE_SERVICE,
    MIO_ERROR,
    NETWORK_ERROR,
    DEVICE_NOT_FOUND_ERROR,
    TELEMETRY_DATES_ERROR_1,
    TELEMETRY_DATES_ERROR_2,
    UNKNOWN_ERROR,
    MioConnectError,
)

DEVICE_ID_1 = "00000001111"
DEVICE_ID_2 = "00000002222"
ERROR_MESSAGE_500 = "Internal Server Error"
TEST_API_KEY = "THIS IS A MOCK KEY"
IMEI_1 = "012412415532"
IMEI_2 = "010000000000"
TEST_DEVICE = {
    "serialNumber": DEVICE_ID_1,
    "modemVersion": "string",
    "activatedAt": "string",
    "imsi": "string",
    "battery": 0,
    "manufacturer": "string",
    "lastActiveAt": "string",
    "createdAt": "string",
    "iccid": "string",
    "connectionStatus": "string",
    "imei": IMEI_1,
    "modelNumber": "string",
    "hardwareVersion": "string",
    "userEmail": "string",
    "firmwareVersion": "string",
    "status": "string",
}
SIMPLE_DEVICE_1 = {"deviceId": DEVICE_ID_1, "imei": IMEI_1}
SIMPLE_DEVICE_2 = {"deviceId": DEVICE_ID_2, "imei": IMEI_2}
TEST_STATUS_OK = {"status": "true"}
TEST_STATUS_ERROR = {"status": "false"}
TEST_TELEMETRY = {
    "nextToken": "string",
    "count": 0,
    "items": [
        {
            "createdAt": 0,
            "forwardedAt": 0,
            "deviceData": {},
            "id": "string",
            "deviceId": "string",
        }
    ],
}


@pytest.mark.asyncio
async def test_list_devices(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(
        return_value=Response(status_code=200, json={"items": [SIMPLE_DEVICE_1]})
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.list_devices()

    assert len(response) == 1
    assert response[0]["deviceId"] == DEVICE_ID_1


@pytest.mark.asyncio
async def test_list_devices_mio_error(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(
        return_value=Response(status_code=500, json={"message": ERROR_MESSAGE_500})
    )
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.list_devices()

    assert e.value.args[0] == f"{MIO_ERROR} [500] : {ERROR_MESSAGE_500}"


@pytest.mark.asyncio
async def test_list_devices_network_error(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(
        side_effect=TransportError("Network error")
    )
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.list_devices()

    assert e.value.args[0] == NETWORK_ERROR


@pytest.mark.asyncio
async def test_list_devices_unknown_error(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(side_effect=Exception("Weird error"))
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.list_devices()

    assert e.value.args[0] == UNKNOWN_ERROR


@pytest.mark.asyncio
async def test_list_devices_unknown_error_2(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(
        Response(status_code=200, json={"devices": [SIMPLE_DEVICE_1]})
    )
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.list_devices()

    assert e.value.args[0] == UNKNOWN_ERROR


@pytest.mark.asyncio
async def test_get_device(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}").mock(
        return_value=Response(
            status_code=200,
            json=TEST_DEVICE,
        )
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.get_device(DEVICE_ID_1)

    assert response["serialNumber"] == DEVICE_ID_1


@pytest.mark.asyncio
async def test_get_device_by_imei(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(
        return_value=Response(status_code=200, json={"items": [SIMPLE_DEVICE_1]})
    )
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}").mock(
        return_value=Response(
            status_code=200,
            json=TEST_DEVICE,
        )
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.get_device_by_imei(IMEI_1)

    assert response["serialNumber"] == DEVICE_ID_1
    assert response["imei"] == IMEI_1


@pytest.mark.asyncio
async def test_get_device_by_imei_not_found(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}").mock(
        return_value=Response(status_code=200, json={"items": [SIMPLE_DEVICE_2]})
    )
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.get_device_by_imei(IMEI_1)

    assert e.value.args[0] == DEVICE_NOT_FOUND_ERROR


@pytest.mark.asyncio
async def test_get_device_data(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/dataUsage").mock(
        return_value=Response(
            status_code=200,
            json=TEST_DEVICE,
        )
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.get_device_data_usage(DEVICE_ID_1)

    assert response["serialNumber"] == DEVICE_ID_1


@pytest.mark.asyncio
async def test_get_device_params(respx_mock):
    respx_mock.get(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/params").mock(
        return_value=Response(
            status_code=200,
            json=TEST_DEVICE,
        )
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.get_device_parameters(DEVICE_ID_1)

    assert response["serialNumber"] == DEVICE_ID_1


@pytest.mark.asyncio
async def test_activate_device(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/activate").mock(
        return_value=Response(
            status_code=200,
            json=TEST_STATUS_OK,
        )
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.activate_device(DEVICE_ID_1)

    assert response["status"] == "true"


@pytest.mark.asyncio
async def test_activate_device_fail(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/activate").mock(
        return_value=Response(
            status_code=200,
            json=TEST_STATUS_ERROR,
        )
    )
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.activate_device(DEVICE_ID_1)

    assert e.value.args[0] == ACTIVATE_ERROR


@pytest.mark.asyncio
async def test_deactivate_device(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/deactivate").mock(
        return_value=Response(
            status_code=200,
            json=TEST_STATUS_OK,
        )
    )
    client = MioConnectClient(TEST_API_KEY)
    response = await client.deactivate_device(DEVICE_ID_1)

    assert response["status"] == "true"


@pytest.mark.asyncio
async def test_deactivate_device_fail(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/deactivate").mock(
        return_value=Response(
            status_code=200,
            json=TEST_STATUS_ERROR,
        )
    )
    client = MioConnectClient(TEST_API_KEY)

    with pytest.raises(MioConnectError) as e:
        await client.deactivate_device(DEVICE_ID_1)

    assert e.value.args[0] == ACTIVATE_ERROR


@pytest.mark.asyncio
async def test_device_telemetry(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/telemetry").mock(
        return_value=Response(
            status_code=200,
            json=TEST_TELEMETRY,
        )
    )
    client = MioConnectClient(TEST_API_KEY)

    start_time = int(pendulum.now().subtract(months=1).timestamp())
    end_time = int(pendulum.now().timestamp())

    response = await client.get_device_telemetry(DEVICE_ID_1, start_time, end_time)

    assert "items" in response
    assert len(response["items"]) == 1


@pytest.mark.asyncio
async def test_device_telemetry_wrong_dates(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/telemetry").mock(
        return_value=Response(
            status_code=200,
            json=TEST_TELEMETRY,
        )
    )
    client = MioConnectClient(TEST_API_KEY)

    start_time = int(pendulum.now().add(months=1).timestamp())
    end_time = int(pendulum.now().timestamp())

    with pytest.raises(MioConnectError) as e:
        await client.get_device_telemetry(DEVICE_ID_1, start_time, end_time)

    assert e.value.args[0] == TELEMETRY_DATES_ERROR_1


@pytest.mark.asyncio
async def test_device_telemetry_wrong_date_types(respx_mock):
    respx_mock.post(f"{HOST}{DEVICE_SERVICE}/{DEVICE_ID_1}/telemetry").mock(
        return_value=Response(
            status_code=200,
            json=TEST_TELEMETRY,
        )
    )
    client = MioConnectClient(TEST_API_KEY)

    start_time = pendulum.now().timestamp()
    end_time = pendulum.now().add(months=1).timestamp()

    with pytest.raises(MioConnectError) as e:
        await client.get_device_telemetry(DEVICE_ID_1, start_time, end_time)

    assert e.value.args[0] == TELEMETRY_DATES_ERROR_2
