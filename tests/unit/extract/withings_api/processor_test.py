import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from ciba_iot_etl.extract.withings_api.processor import (
    update_withings_health_status,
    handle_expired_tokens,
    refresh_withings_token,
    WithingsToTortoiseConverter,
)
from ciba_iot_etl.models.db.withings import Withings
from ciba_iot_etl.extract.withings_api.core import WithingsStatusCode
from ciba_iot_etl.extract.withings_api.common import (
    MeasureGetMeasResponse,
    MeasureGetMeasGroup,
    MeasureGetMeasMeasure,
)
from ciba_iot_etl.extract.withings_api.const import MeasureType
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.blood_pressure import BloodPressure
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.helpers.measurement import (
    UnitOfMeasurement as CibaUnitOfMeasurement,
    DeviceType,
)


# Define constants to avoid duplication
TEST_REASON = "Test reason"
UPDATE_HEALTH_STATUS_PATH = (
    "ciba_iot_etl.extract.withings_api.processor.update_withings_health_status"
)
LOG_METRIC_PATH = "ciba_iot_etl.extract.withings_api.processor.log_metric"


@pytest.mark.asyncio
async def test_update_withings_health_status_success():
    """
    update_withings_health_status should update the health status when the connection exists
    and the status is different
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.healthy = False
    mock_connection.user_id = (
        "test_user_id"  # Add user_id to avoid JSON serialization error
    )

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter and update methods
        with patch.object(Withings, "filter") as mock_filter:
            mock_filter.return_value.first = AsyncMock(return_value=mock_connection)
            mock_filter.return_value.update = AsyncMock()

            # Call the function
            result = await update_withings_health_status(withings_id, True, TEST_REASON)

            # Verify the result
            assert result is True
            # The filter method is called twice in the implementation
            assert mock_filter.call_count == 2
            mock_filter.assert_any_call(id=withings_id)
            mock_filter.return_value.update.assert_called_once_with(healthy=True)

            # Verify that log_metric was called
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_update_withings_health_status_no_update_needed():
    """
    update_withings_health_status should not update the health status when the connection exists
    but the status is the same
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.healthy = True

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter and update methods
        with patch.object(Withings, "filter") as mock_filter:
            mock_filter.return_value.first = AsyncMock(return_value=mock_connection)
            mock_filter.return_value.update = AsyncMock()

            # Call the function
            result = await update_withings_health_status(withings_id, True, TEST_REASON)

            # Verify the result
            assert result is True
            mock_filter.assert_called_once_with(id=withings_id)
            mock_filter.return_value.update.assert_not_called()

            # Verify that log_metric was not called (no status change)
            mock_log_metric.assert_not_called()


@pytest.mark.asyncio
async def test_update_withings_health_status_connection_not_found():
    """
    update_withings_health_status should return False when the connection does not exist
    """
    # Mock the Withings model
    withings_id = "test_id"

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter method
        with patch.object(Withings, "filter") as mock_filter:
            mock_filter.return_value.first = AsyncMock(return_value=None)

            # Call the function
            result = await update_withings_health_status(withings_id, True, TEST_REASON)

            # Verify the result
            assert result is False
            mock_filter.assert_called_once_with(id=withings_id)

            # Verify that log_metric was called for missing connection
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_update_withings_health_status_exception():
    """
    update_withings_health_status should return False when an exception occurs
    """
    # Mock the Withings model
    withings_id = "test_id"

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter method to raise an exception
        with patch.object(Withings, "filter") as mock_filter:
            mock_filter.side_effect = Exception("Test exception")

            # Call the function
            result = await update_withings_health_status(withings_id, True, TEST_REASON)

            # Verify the result
            assert result is False
            mock_filter.assert_called_once_with(id=withings_id)

            # Verify that log_metric was called for the exception
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_handle_expired_tokens_success():
    """
    handle_expired_tokens should mark the connection as unhealthy and return True
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.user_id = "test_user_id"

    # Mock the log_metric function
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the update_withings_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Mock the Withings filter method
            with patch.object(Withings, "filter") as mock_filter:
                mock_filter.return_value.first = AsyncMock(return_value=mock_connection)

                # Call the function
                result = await handle_expired_tokens(withings_id)

                # Verify the result
                assert result is True
                mock_update_status.assert_called_once()
                mock_filter.assert_called_once_with(id=withings_id)

                # Verify that log_metric was called for expired tokens
                mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_handle_expired_tokens_connection_not_found():
    """
    handle_expired_tokens should return False when the connection does not exist
    """
    # Mock the Withings model
    withings_id = "test_id"

    # Mock the log_metric function
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the update_withings_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Mock the Withings filter method
            with patch.object(Withings, "filter") as mock_filter:
                mock_filter.return_value.first = AsyncMock(return_value=None)

                # Call the function
                result = await handle_expired_tokens(withings_id)

                # Verify the result
                assert result is False
                mock_update_status.assert_called_once()
                mock_filter.assert_called_once_with(id=withings_id)

                # Verify that log_metric was called for missing connection
                mock_log_metric.assert_called_once()


# Define more constants
HANDLE_EXPIRED_TOKENS_PATH = (
    "ciba_iot_etl.extract.withings_api.processor.handle_expired_tokens"
)


@pytest.mark.asyncio
async def test_refresh_withings_token_refresh_token_expired():
    """
    refresh_withings_token should return None when the refresh token is expired
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = True

    # Mock the WithingsLoader
    mock_service = MagicMock()

    # Mock the handle_expired_tokens function
    with patch(HANDLE_EXPIRED_TOKENS_PATH) as mock_handle_expired:
        mock_handle_expired.return_value = True

        # Call the function
        result = await refresh_withings_token(
            mock_service, withings_id, mock_connection
        )

        # Verify the result
        assert result is None
        mock_connection.is_refresh_token_expired.assert_called_once()
        mock_handle_expired.assert_called_once_with(withings_id)


@pytest.mark.asyncio
async def test_refresh_withings_token_use_old_refresh_token():
    """
    refresh_withings_token should use the old refresh token if it's not expired
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.is_old_refresh_token_expired.return_value = False
    mock_connection.old_refresh_token = "old_token"
    mock_connection.refresh_token = "current_token"

    # Mock the WithingsLoader
    mock_service = MagicMock()
    mock_service.get_new_token = AsyncMock(
        return_value={
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
        }
    )

    # Mock the Withings.update_tokens method
    with patch.object(Withings, "update_tokens") as mock_update_tokens:
        mock_updated_connection = MagicMock()
        mock_updated_connection.access_token = "new_access_token"
        mock_update_tokens.return_value = mock_updated_connection

        # Mock the update_withings_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Call the function
            result = await refresh_withings_token(
                mock_service, withings_id, mock_connection
            )

            # Verify the result
            assert result == "new_access_token"
            mock_connection.is_refresh_token_expired.assert_called_once()
            mock_connection.is_old_refresh_token_expired.assert_called_once()
            mock_service.get_new_token.assert_called_once_with(
                refresh_token="old_token"
            )
            mock_update_tokens.assert_called_once()
            mock_update_status.assert_called_once()


@pytest.mark.asyncio
async def test_refresh_withings_token_use_current_refresh_token():
    """
    refresh_withings_token should use the current refresh token if the old one is expired
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.is_old_refresh_token_expired.return_value = True
    mock_connection.refresh_token = "current_token"

    # Mock the WithingsLoader
    mock_service = MagicMock()
    mock_service.get_new_token = AsyncMock(
        return_value={
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
        }
    )

    # Mock the Withings.update_tokens method
    with patch.object(Withings, "update_tokens") as mock_update_tokens:
        mock_updated_connection = MagicMock()
        mock_updated_connection.access_token = "new_access_token"
        mock_update_tokens.return_value = mock_updated_connection

        # Mock the update_withings_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Call the function
            result = await refresh_withings_token(
                mock_service, withings_id, mock_connection
            )

            # Verify the result
            assert result == "new_access_token"
            mock_connection.is_refresh_token_expired.assert_called_once()
            mock_connection.is_old_refresh_token_expired.assert_called_once()
            mock_service.get_new_token.assert_called_once_with(
                refresh_token="current_token"
            )
            mock_update_tokens.assert_called_once()
            mock_update_status.assert_called_once()


@pytest.mark.asyncio
async def test_refresh_withings_token_rate_limit_error():
    """
    refresh_withings_token should return None when a rate limit error occurs
    but should not mark the connection as unhealthy
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.is_old_refresh_token_expired.return_value = True
    mock_connection.refresh_token = "current_token"

    # Mock the WithingsLoader
    mock_service = MagicMock()
    mock_service.get_new_token = AsyncMock(
        return_value={"status": WithingsStatusCode.RATE_LIMIT_EXCEEDED}
    )

    # Mock the log_metric function
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the update_withings_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Call the function
            result = await refresh_withings_token(
                mock_service, withings_id, mock_connection
            )

            # Verify the result
            assert result is None
            mock_connection.is_refresh_token_expired.assert_called_once()
            mock_connection.is_old_refresh_token_expired.assert_called_once()
            mock_service.get_new_token.assert_called_once_with(
                refresh_token="current_token"
            )

            # Verify that update_withings_health_status was NOT called for rate limit errors
            mock_update_status.assert_not_called()

            # Verify that log_metric was called for rate limit
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_refresh_withings_token_invalid_grant_error():
    """
    refresh_withings_token should return None and call handle_expired_tokens when an invalid_grant error occurs
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.is_old_refresh_token_expired.return_value = True
    mock_connection.refresh_token = "current_token"

    # Mock the WithingsLoader
    mock_service = MagicMock()
    mock_service.get_new_token = AsyncMock(return_value={"error": "invalid_grant"})

    # Mock the handle_expired_tokens function
    with patch(HANDLE_EXPIRED_TOKENS_PATH) as mock_handle_expired:
        mock_handle_expired.return_value = True

        # Mock the update_withings_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Call the function
            result = await refresh_withings_token(
                mock_service, withings_id, mock_connection
            )

            # Verify the result
            assert result is None
            mock_connection.is_refresh_token_expired.assert_called_once()
            mock_connection.is_old_refresh_token_expired.assert_called_once()
            mock_service.get_new_token.assert_called_once_with(
                refresh_token="current_token"
            )
            mock_handle_expired.assert_called_once_with(withings_id)
            mock_update_status.assert_not_called()


@pytest.mark.asyncio
async def test_refresh_withings_token_other_error():
    """
    refresh_withings_token should return None and update health status when another error occurs
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.is_old_refresh_token_expired.return_value = True
    mock_connection.refresh_token = "current_token"

    # Mock the WithingsLoader
    mock_service = MagicMock()
    mock_service.get_new_token = AsyncMock(return_value={"error": "other_error"})

    # Mock the update_withings_health_status function
    with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
        mock_update_status.return_value = True

        # Call the function
        result = await refresh_withings_token(
            mock_service, withings_id, mock_connection
        )

        # Verify the result
        assert result is None
        mock_connection.is_refresh_token_expired.assert_called_once()
        mock_connection.is_old_refresh_token_expired.assert_called_once()
        mock_service.get_new_token.assert_called_once_with(
            refresh_token="current_token"
        )
        mock_update_status.assert_called_once()


@pytest.mark.asyncio
async def test_refresh_withings_token_invalid_token_response():
    """
    refresh_withings_token should return None when the token response is invalid
    """
    # Mock the Withings model
    withings_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.is_old_refresh_token_expired.return_value = True
    mock_connection.refresh_token = "current_token"

    # Mock the WithingsLoader
    mock_service = MagicMock()
    mock_service.get_new_token = AsyncMock(
        return_value={
            # Missing access_token and refresh_token
        }
    )

    # Mock the update_withings_health_status function
    with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
        mock_update_status.return_value = True

        # Call the function
        result = await refresh_withings_token(
            mock_service, withings_id, mock_connection
        )

        # Verify the result
        assert result is None
        mock_connection.is_refresh_token_expired.assert_called_once()
        mock_connection.is_old_refresh_token_expired.assert_called_once()
        mock_service.get_new_token.assert_called_once_with(
            refresh_token="current_token"
        )
        mock_update_status.assert_called_once()


# ============================================================================
# WithingsToTortoiseConverter Tests
# ============================================================================


class TestWithingsToTortoiseConverter:
    """Test suite for WithingsToTortoiseConverter class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.member_id = 12345
        self.converter = WithingsToTortoiseConverter(self.member_id)
        self.test_timestamp = 1640995200  # 2022-01-01 00:00:00 UTC

    def test_init(self):
        """Test converter initialization."""
        converter = WithingsToTortoiseConverter(999)
        assert converter.member_id == 999

    @pytest.mark.asyncio
    async def test_transform_weight(self):
        """Test weight transformation."""
        timestamp = self.test_timestamp
        value = 75.5

        weight = await self.converter.transform_weight(timestamp, value)

        assert isinstance(weight, Weight)
        assert abs(float(weight.value) - value) < 0.01
        assert weight.member_id == self.member_id
        assert weight.unit == CibaUnitOfMeasurement.KG
        assert weight.device == DeviceType.WITHINGS
        expected_datetime = datetime.fromtimestamp(timestamp)
        assert weight.created_at.replace(tzinfo=None) == expected_datetime

    @pytest.mark.asyncio
    async def test_transform_blood_pressure(self):
        """Test blood pressure transformation."""
        timestamp = self.test_timestamp
        systolic = 120.0
        diastolic = 80.0

        bp = await self.converter.transform_blood_pressure(
            timestamp, systolic, diastolic
        )

        assert isinstance(bp, BloodPressure)
        assert abs(bp.systolic_value - systolic) < 0.01
        assert abs(bp.diastolic_value - diastolic) < 0.01
        assert bp.member_id == self.member_id
        assert bp.unit == CibaUnitOfMeasurement.MM_HG
        assert bp.device == DeviceType.WITHINGS
        expected_datetime = datetime.fromtimestamp(timestamp)
        assert bp.created_at.replace(tzinfo=None) == expected_datetime

    @pytest.mark.asyncio
    async def test_transform_heart_rate(self):
        """Test heart rate transformation."""
        timestamp = self.test_timestamp
        value = 72

        hr = await self.converter.transform_heart_rate(timestamp, value)

        assert isinstance(hr, HeartRate)
        assert hr.value == int(float(value))
        assert hr.member_id == self.member_id
        assert hr.unit == CibaUnitOfMeasurement.BPM
        assert hr.device == DeviceType.WITHINGS
        expected_datetime = datetime.fromtimestamp(timestamp)
        assert hr.created_at.replace(tzinfo=None) == expected_datetime

    @pytest.mark.asyncio
    async def test_transform_heart_rate_int_value(self):
        """Test heart rate transformation with int value."""
        timestamp = self.test_timestamp
        value = 72  # Int value

        hr = await self.converter.transform_heart_rate(timestamp, value)

        assert hr.value == 72
        assert isinstance(hr.value, int)

    def test_verify_misentered_lb_no_correction_needed(self):
        """Test verify_misentered_lb when no correction is needed."""
        weights = [70.0, 71.0, 72.0, 73.0]  # All reasonable kg values

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        assert corrected == weights
        assert index is None

    def test_verify_misentered_lb_single_correction(self):
        """Test verify_misentered_lb with one value needing correction."""
        # 160 lbs = ~72.6 kg, which should fit between 70 and 75
        weights = [70.0, 160.0, 75.0]  # Middle value is likely in pounds

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        assert index == 1
        assert abs(corrected[0] - 70.0) < 0.01
        assert abs(corrected[2] - 75.0) < 0.01
        # 160 * (1/2.20462) ≈ 72.57
        assert abs(corrected[1] - 72.57) < 0.1

    def test_verify_misentered_lb_no_correction_boundary(self):
        """Test verify_misentered_lb at 10% boundary."""
        weights = [70.0, 77.0, 75.0]  # 77 is exactly 10% off from 70

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        # Should not correct because it's exactly at 10% boundary
        assert corrected == weights
        assert index is None

    def test_verify_misentered_lb_empty_list(self):
        """Test verify_misentered_lb with empty list."""
        weights = []

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        assert corrected == []
        assert index is None

    def test_verify_misentered_lb_single_weight(self):
        """Test verify_misentered_lb with single weight."""
        weights = [70.0]

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        assert corrected == weights
        assert index is None

    def test_verify_misentered_lb_two_weights(self):
        """Test verify_misentered_lb with two weights."""
        weights = [70.0, 160.0]

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        assert corrected == weights
        assert index is None

    def test_verify_misentered_lb_multiple_candidates(self):
        """Test verify_misentered_lb with multiple potential candidates."""
        # Only the first qualifying candidate should be corrected
        weights = [70.0, 160.0, 75.0, 170.0, 80.0]

        corrected, index = WithingsToTortoiseConverter.verify_misentered_lb(weights)

        assert index == 1  # First candidate
        assert abs(corrected[0] - 70.0) < 0.01
        assert abs(corrected[2] - 75.0) < 0.01
        assert abs(corrected[3] - 170.0) < 0.01  # This should remain unchanged
        assert abs(corrected[4] - 80.0) < 0.01

    def test_update_blood_pressure_dict_systolic(self):
        """Test updating blood pressure dict with systolic value."""
        bp_dict = {}
        timestamp = self.test_timestamp

        self.converter._update_blood_pressure_dict(
            bp_dict, timestamp, MeasureType.SYSTOLIC_BLOOD_PRESSURE.value, 120.0
        )

        assert timestamp in bp_dict
        assert abs(bp_dict[timestamp]["systolic_value"] - 120.0) < 0.01
        assert bp_dict[timestamp]["diastolic_value"] is None

    def test_update_blood_pressure_dict_diastolic(self):
        """Test updating blood pressure dict with diastolic value."""
        bp_dict = {}
        timestamp = self.test_timestamp

        self.converter._update_blood_pressure_dict(
            bp_dict, timestamp, MeasureType.DIASTOLIC_BLOOD_PRESSURE.value, 80.0
        )

        assert timestamp in bp_dict
        assert bp_dict[timestamp]["systolic_value"] is None
        assert abs(bp_dict[timestamp]["diastolic_value"] - 80.0) < 0.01

    def test_update_blood_pressure_dict_both_values(self):
        """Test updating blood pressure dict with both systolic and diastolic."""
        bp_dict = {}
        timestamp = self.test_timestamp

        # Add systolic first
        self.converter._update_blood_pressure_dict(
            bp_dict, timestamp, MeasureType.SYSTOLIC_BLOOD_PRESSURE.value, 120.0
        )
        # Add diastolic second
        self.converter._update_blood_pressure_dict(
            bp_dict, timestamp, MeasureType.DIASTOLIC_BLOOD_PRESSURE.value, 80.0
        )

        assert timestamp in bp_dict
        assert abs(bp_dict[timestamp]["systolic_value"] - 120.0) < 0.01
        assert abs(bp_dict[timestamp]["diastolic_value"] - 80.0) < 0.01

    @pytest.mark.asyncio
    async def test_collect_blood_pressure_measurements_complete_pairs(self):
        """Test collecting blood pressure measurements with complete pairs."""
        bp_dict = {
            self.test_timestamp: {"systolic_value": 120.0, "diastolic_value": 80.0},
            self.test_timestamp + 3600: {
                "systolic_value": 130.0,
                "diastolic_value": 85.0,
            },
        }
        bp_measurements = []

        await self.converter._collect_blood_pressure_measurements(
            bp_dict, bp_measurements
        )

        assert len(bp_measurements) == 2
        for bp in bp_measurements:
            assert isinstance(bp, BloodPressure)
            assert bp.member_id == self.member_id
            assert bp.unit == CibaUnitOfMeasurement.MM_HG
            assert bp.device == DeviceType.WITHINGS

    @pytest.mark.asyncio
    async def test_collect_blood_pressure_measurements_incomplete_pairs(self):
        """Test collecting blood pressure measurements with incomplete pairs."""
        bp_dict = {
            self.test_timestamp: {"systolic_value": 120.0, "diastolic_value": None},
            self.test_timestamp + 3600: {
                "systolic_value": None,
                "diastolic_value": 80.0,
            },
            self.test_timestamp + 7200: {
                "systolic_value": 130.0,
                "diastolic_value": 85.0,
            },
        }
        bp_measurements = []

        await self.converter._collect_blood_pressure_measurements(
            bp_dict, bp_measurements
        )

        # Only the complete pair should be collected
        assert len(bp_measurements) == 1
        bp = bp_measurements[0]
        assert abs(bp.systolic_value - 130.0) < 0.01
        assert abs(bp.diastolic_value - 85.0) < 0.01

    def _create_measure(
        self, measure_type: MeasureType, value: int, unit: int = 0
    ) -> MeasureGetMeasMeasure:
        """Helper to create a MeasureGetMeasMeasure."""
        return MeasureGetMeasMeasure(type=measure_type, value=value, unit=unit)

    def _create_measure_group(
        self, timestamp: int, measures: list
    ) -> MeasureGetMeasGroup:
        """Helper to create a MeasureGetMeasGroup."""
        return MeasureGetMeasGroup(
            grpid=1,
            attrib=0,
            date=timestamp,
            created=timestamp,
            modified=timestamp,
            category=1,
            measures=measures,
        )

    def _create_response(self, measuregrps: list) -> MeasureGetMeasResponse:
        """Helper to create a MeasureGetMeasResponse."""
        return MeasureGetMeasResponse(
            measuregrps=measuregrps,
            timezone="UTC",
            updatetime=datetime.fromtimestamp(self.test_timestamp),
        )

    @pytest.mark.asyncio
    async def test_convert_weight_only(self):
        """Test convert method with weight measurements only."""
        measures = [
            self._create_measure(MeasureType.WEIGHT, 755, -1),  # 75.5 kg
            self._create_measure(MeasureType.WEIGHT, 760, -1),  # 76.0 kg
        ]
        measure_group = self._create_measure_group(self.test_timestamp, measures)
        response = self._create_response([measure_group])

        result = await self.converter.convert(response)

        assert "weight_measurements" in result
        assert "blood_pressure_measurements" in result
        assert "heart_rate_measurements" in result

        assert len(result["weight_measurements"]) == 2
        assert len(result["blood_pressure_measurements"]) == 0
        assert len(result["heart_rate_measurements"]) == 0

        # Check weight values
        weights = result["weight_measurements"]
        assert abs(float(weights[0].value) - 75.5) < 0.01
        assert abs(float(weights[1].value) - 76.0) < 0.01

    @pytest.mark.asyncio
    async def test_convert_heart_rate_only(self):
        """Test convert method with heart rate measurements only."""
        measures = [
            self._create_measure(MeasureType.HEART_RATE, 72, 0),
            self._create_measure(MeasureType.HEART_RATE, 75, 0),
        ]
        measure_group = self._create_measure_group(self.test_timestamp, measures)
        response = self._create_response([measure_group])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 0
        assert len(result["blood_pressure_measurements"]) == 0
        assert len(result["heart_rate_measurements"]) == 2

        # Check heart rate values
        hrs = result["heart_rate_measurements"]
        assert hrs[0].value == 72
        assert hrs[1].value == 75

    @pytest.mark.asyncio
    async def test_convert_blood_pressure_only(self):
        """Test convert method with blood pressure measurements only."""
        measures = [
            self._create_measure(MeasureType.SYSTOLIC_BLOOD_PRESSURE, 120, 0),
            self._create_measure(MeasureType.DIASTOLIC_BLOOD_PRESSURE, 80, 0),
        ]
        measure_group = self._create_measure_group(self.test_timestamp, measures)
        response = self._create_response([measure_group])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 0
        assert len(result["blood_pressure_measurements"]) == 1
        assert len(result["heart_rate_measurements"]) == 0

        # Check blood pressure values
        bp = result["blood_pressure_measurements"][0]
        assert bp.systolic_value == 120
        assert bp.diastolic_value == 80

    @pytest.mark.asyncio
    async def test_convert_mixed_measurements(self):
        """Test convert method with mixed measurement types."""
        measures = [
            self._create_measure(MeasureType.WEIGHT, 755, -1),  # 75.5 kg
            self._create_measure(MeasureType.HEART_RATE, 72, 0),
            self._create_measure(MeasureType.SYSTOLIC_BLOOD_PRESSURE, 120, 0),
            self._create_measure(MeasureType.DIASTOLIC_BLOOD_PRESSURE, 80, 0),
        ]
        measure_group = self._create_measure_group(self.test_timestamp, measures)
        response = self._create_response([measure_group])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 1
        assert len(result["blood_pressure_measurements"]) == 1
        assert len(result["heart_rate_measurements"]) == 1

        # Verify all measurements have correct member_id and device
        weight = result["weight_measurements"][0]
        assert weight.member_id == self.member_id
        assert weight.device == DeviceType.WITHINGS

        bp = result["blood_pressure_measurements"][0]
        assert bp.member_id == self.member_id
        assert bp.device == DeviceType.WITHINGS

        hr = result["heart_rate_measurements"][0]
        assert hr.member_id == self.member_id
        assert hr.device == DeviceType.WITHINGS

    @pytest.mark.asyncio
    async def test_convert_unknown_measurement_type(self):
        """Test convert method with unknown measurement types."""
        measures = [
            self._create_measure(MeasureType.WEIGHT, 755, -1),  # Known type
            self._create_measure(
                MeasureType.TEMPERATURE, 365, -1
            ),  # Unknown/unsupported type
        ]
        measure_group = self._create_measure_group(self.test_timestamp, measures)
        response = self._create_response([measure_group])

        result = await self.converter.convert(response)

        # Should only process the weight measurement
        assert len(result["weight_measurements"]) == 1
        assert len(result["blood_pressure_measurements"]) == 0
        assert len(result["heart_rate_measurements"]) == 0

    @pytest.mark.asyncio
    async def test_convert_empty_measurements(self):
        """Test convert method with empty measurements."""
        response = self._create_response([])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 0
        assert len(result["blood_pressure_measurements"]) == 0
        assert len(result["heart_rate_measurements"]) == 0

    @pytest.mark.asyncio
    async def test_convert_with_weight_correction(self):
        """Test convert method with weight correction applied."""
        # Create weights where middle one needs lb-to-kg conversion
        # 70kg, 160lbs (~72.6kg), 75kg
        measures_group1 = [self._create_measure(MeasureType.WEIGHT, 700, -1)]  # 70.0 kg
        measures_group2 = [
            self._create_measure(MeasureType.WEIGHT, 1600, -1)
        ]  # 160.0 (should be lbs)
        measures_group3 = [self._create_measure(MeasureType.WEIGHT, 750, -1)]  # 75.0 kg

        group1 = self._create_measure_group(self.test_timestamp, measures_group1)
        group2 = self._create_measure_group(self.test_timestamp + 3600, measures_group2)
        group3 = self._create_measure_group(self.test_timestamp + 7200, measures_group3)

        response = self._create_response([group1, group2, group3])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 3
        weights = result["weight_measurements"]

        # First and third should be unchanged
        assert abs(float(weights[0].value) - 70.0) < 0.01
        assert abs(float(weights[2].value) - 75.0) < 0.01

        # Second should be corrected from 160 lbs to ~72.6 kg
        assert abs(float(weights[1].value) - 72.57) < 0.1

    @pytest.mark.asyncio
    async def test_convert_incomplete_blood_pressure_pairs(self):
        """Test convert method with incomplete blood pressure pairs."""
        # Create separate measure groups with only systolic or diastolic
        measures_group1 = [
            self._create_measure(MeasureType.SYSTOLIC_BLOOD_PRESSURE, 120, 0)
        ]
        measures_group2 = [
            self._create_measure(MeasureType.DIASTOLIC_BLOOD_PRESSURE, 80, 0)
        ]
        measures_group3 = [
            self._create_measure(MeasureType.SYSTOLIC_BLOOD_PRESSURE, 130, 0),
            self._create_measure(MeasureType.DIASTOLIC_BLOOD_PRESSURE, 85, 0),
        ]

        group1 = self._create_measure_group(self.test_timestamp, measures_group1)
        group2 = self._create_measure_group(self.test_timestamp + 3600, measures_group2)
        group3 = self._create_measure_group(self.test_timestamp + 7200, measures_group3)

        response = self._create_response([group1, group2, group3])

        result = await self.converter.convert(response)

        # Only the complete pair should be collected
        assert len(result["blood_pressure_measurements"]) == 1
        bp = result["blood_pressure_measurements"][0]
        assert bp.systolic_value == 130
        assert bp.diastolic_value == 85

    @pytest.mark.asyncio
    async def test_convert_multiple_measure_groups(self):
        """Test convert method with multiple measure groups."""
        # Group 1: Weight and heart rate
        measures_group1 = [
            self._create_measure(MeasureType.WEIGHT, 755, -1),
            self._create_measure(MeasureType.HEART_RATE, 72, 0),
        ]

        # Group 2: Blood pressure
        measures_group2 = [
            self._create_measure(MeasureType.SYSTOLIC_BLOOD_PRESSURE, 120, 0),
            self._create_measure(MeasureType.DIASTOLIC_BLOOD_PRESSURE, 80, 0),
        ]

        # Group 3: Another weight
        measures_group3 = [self._create_measure(MeasureType.WEIGHT, 760, -1)]

        group1 = self._create_measure_group(self.test_timestamp, measures_group1)
        group2 = self._create_measure_group(self.test_timestamp + 3600, measures_group2)
        group3 = self._create_measure_group(self.test_timestamp + 7200, measures_group3)

        response = self._create_response([group1, group2, group3])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 2
        assert len(result["blood_pressure_measurements"]) == 1
        assert len(result["heart_rate_measurements"]) == 1

        # Verify timestamps are preserved
        weights = result["weight_measurements"]
        expected_datetime1 = datetime.fromtimestamp(self.test_timestamp)
        expected_datetime2 = datetime.fromtimestamp(self.test_timestamp + 7200)
        assert weights[0].created_at.replace(tzinfo=None) == expected_datetime1
        assert weights[1].created_at.replace(tzinfo=None) == expected_datetime2

    @pytest.mark.asyncio
    async def test_convert_unit_scaling(self):
        """Test convert method with different unit scaling."""
        # Test different unit values for proper scaling
        measures = [
            self._create_measure(MeasureType.WEIGHT, 755, -1),  # 75.5 kg (755 * 10^-1)
            self._create_measure(
                MeasureType.WEIGHT, 7550, -2
            ),  # 75.5 kg (7550 * 10^-2)
            self._create_measure(MeasureType.HEART_RATE, 72, 0),  # 72 bpm (72 * 10^0)
        ]
        measure_group = self._create_measure_group(self.test_timestamp, measures)
        response = self._create_response([measure_group])

        result = await self.converter.convert(response)

        assert len(result["weight_measurements"]) == 2
        assert len(result["heart_rate_measurements"]) == 1

        # Both weights should have the same value after scaling
        weights = result["weight_measurements"]
        assert abs(float(weights[0].value) - 75.5) < 0.01
        assert abs(float(weights[1].value) - 75.5) < 0.01

        hr = result["heart_rate_measurements"][0]
        assert hr.value == 72
