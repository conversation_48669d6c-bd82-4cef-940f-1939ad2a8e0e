import pytest
from datetime import date, datetime

from ciba_iot_etl.extract.fitbit_api.core import (
    FitbitLoader,
    Subscription,
    SubscriptionResponse,
    RefreshTokenResp,
)
from ciba_iot_etl.models.pydantic.fitbit import SubscriptionCollectionPath

CLIENT_ID = "123456"
CLIENT_SECRET = "abcdef"
MOCK_URL = "https://localhost:8080"
MOCK_TOKEN = "Mock token"
TEXT_RESPONSE = "Text response"
MOCK_RESPONSE = "Mock response"
START_DATE = date(2021, 1, 1)
END_DATE = date(2021, 1, 2)
INDIVIDUAL_SUBSCRIPTION = {
    "collectionType": "sleep",
    "ownerId": "12345",
    "ownerType": "user",
    "subscriberId": "12345",
    "subscriptionId": "12345_12345",
}

STATE = 123456

fitbit_loader = FitbitLoader(
    client_id=CLIENT_ID, client_secret=CLIENT_SECRET, redirect_uri=MOCK_URL
)


@pytest.mark.asyncio
async def test_get_auth_page_url_1(mocker):
    """
    get_auth_page_url should return object with auth_url
    """

    body_dict = {
        "field1": "Mock Response 1",
        "field2": "Mock Response 2",
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    result = await fitbit_loader.get_auth_page_url()

    MIN_LENGTH_CODE = 43
    MAX_LENGTH_CODE = 128

    assert (
        len(result["code_verifier"]) >= MIN_LENGTH_CODE
        and len(result["code_verifier"]) <= MAX_LENGTH_CODE
    )
    assert result["field1"] == body_dict["field1"]
    assert result["field2"] == body_dict["field2"]


@pytest.mark.asyncio
async def test_get_token_1(mocker):
    """
    get_token should return object with token
    """

    body_dict = {"field1": MOCK_RESPONSE, "token": MOCK_TOKEN}

    code = "12345"
    code_verifier = "12345"

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    result = await fitbit_loader.get_token(
        code=code, state=STATE, site=MOCK_URL, code_verifier=code_verifier
    )

    assert result["field1"] == body_dict["field1"]
    assert result["token"] == body_dict["token"]


@pytest.mark.asyncio
async def test_get_user_1(mocker):
    """
    get_user should return object with user
    """

    body_dict = {"field1": MOCK_RESPONSE, "user": "Mock user"}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    result = await fitbit_loader.get_user(MOCK_TOKEN)

    assert result["user"] == body_dict["user"]


@pytest.mark.asyncio
async def test_get_all_activities_1(mocker):
    """
    get_all_activities should return object with activities
    """

    body_dict = {"activities": [{"field1": MOCK_RESPONSE, "activity": "Mock activity"}]}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    after_date = date(2021, 1, 1)

    result = await fitbit_loader.get_all_activities(MOCK_TOKEN, after_date=after_date)

    assert result["activities"] == body_dict["activities"]


@pytest.mark.asyncio
async def test_get_steps_1(mocker):
    """
    get_steps should return object with steps
    """

    body_dict = {"steps": [{"field1": MOCK_RESPONSE, "steps": "Mock steps"}]}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    result = await fitbit_loader.get_steps(MOCK_TOKEN, START_DATE, END_DATE)

    assert result["steps"] == body_dict["steps"]


@pytest.mark.asyncio
async def test_get_distance_1(mocker):
    """
    get_distance should return the distance
    """

    body_dict = {"distance": 12345}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    result = await fitbit_loader.get_distance(MOCK_TOKEN, START_DATE, END_DATE)

    assert result["distance"] == body_dict["distance"]


@pytest.mark.asyncio
async def test_get_active_minutes_1(mocker):
    """
    get_active_minutes should return the active minutes
    """

    body_dict = {"active_minutes": 12345}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=TEXT_RESPONSE)

    result_1 = await fitbit_loader.get_active_minutes_lightly(
        MOCK_TOKEN, START_DATE, END_DATE
    )
    result_2 = await fitbit_loader.get_active_minutes_fairly(
        MOCK_TOKEN, START_DATE, END_DATE
    )
    result_3 = await fitbit_loader.get_active_minutes_very(
        MOCK_TOKEN, START_DATE, END_DATE
    )

    assert result_1["active_minutes"] == body_dict["active_minutes"]
    assert result_2["active_minutes"] == body_dict["active_minutes"]
    assert result_3["active_minutes"] == body_dict["active_minutes"]


@pytest.mark.asyncio
async def test_get_sleep_data_1(mocker):
    """
    get_sleep_data should return the sleep data
    """

    body_dict = {
        "sleep_value_1": 100,
        "sleep_value_2": 200,
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text="Mock response")

    result = await fitbit_loader.get_sleep_data(MOCK_TOKEN, START_DATE, END_DATE)

    assert result["sleep_value_1"] == body_dict["sleep_value_1"]
    assert result["sleep_value_2"] == body_dict["sleep_value_2"]


@pytest.mark.asyncio
async def test_get_heart_rate_day_1(mocker):
    """
    get_heart_rate_day should return the heart rate data
    """

    date_value = date(2021, 1, 1)

    body_dict = {
        "heart_rate_value_1": 100,
        "heart_rate_value_2": 200,
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_heart_rate_day(MOCK_TOKEN, date_value)

    assert result["heart_rate_value_1"] == body_dict["heart_rate_value_1"]
    assert result["heart_rate_value_2"] == body_dict["heart_rate_value_2"]


@pytest.mark.asyncio
async def test_get_heart_rate_range_1(mocker):
    """
    get_heart_rate_range should return the heart rate data
    """

    START_DATETIME = datetime(2021, 1, 1)
    END_DATETIME = datetime(2021, 1, 2)

    body_dict = {
        "heart_rate_value_1": 100,
        "heart_rate_value_2": 200,
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_heart_rate_range(
        MOCK_TOKEN, START_DATETIME, END_DATETIME
    )

    assert result["heart_rate_value_1"] == body_dict["heart_rate_value_1"]
    assert result["heart_rate_value_2"] == body_dict["heart_rate_value_2"]


@pytest.mark.asyncio
async def test_get_body_weight_1(mocker):
    """
    get_body_weight should return the body weight data when next_url is present
    """

    date_value = date(2021, 1, 1)

    body_dict = {
        "weights": [100, 200],
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_body_weight(MOCK_TOKEN, date_value, MOCK_URL)

    assert result["weights"] == body_dict["weights"]


@pytest.mark.asyncio
async def test_get_body_weight_2(mocker):
    """
    get_body_weight should return the body weight data when next_url is not present
    """

    date_value = date(2021, 1, 1)
    next_url = None

    body_dict = {
        "weights": [100, 200],
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_body_weight(MOCK_TOKEN, date_value, next_url)

    assert result["weights"] == body_dict["weights"]


@pytest.mark.asyncio
async def test_get_sp02_1(mocker):
    """
    get_sp02 should return the sp02 data when next_url is present
    """

    date_value = date(2021, 1, 1)

    body_dict = {
        "sp02": [100, 200],
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_sp02(MOCK_TOKEN, date_value, MOCK_URL)

    assert result["sp02"] == body_dict["sp02"]


@pytest.mark.asyncio
async def test_get_sp02_2(mocker):
    """
    get_sp02 should return the sp02 data when next_url is not present
    """

    date_value = date(2021, 1, 1)
    next_url = None

    body_dict = {
        "sp02": [100, 200],
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_sp02(MOCK_TOKEN, date_value, next_url)

    assert result["sp02"] == body_dict["sp02"]


@pytest.mark.asyncio
async def test_get_activity_descriptions_1(mocker):
    """
    get_activity_descriptions should return the activity descriptions
    """

    body_dict = {
        "activity_descriptions": ["Description 1", "Description 2"],
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_activity_descriptions(MOCK_TOKEN)

    assert result["activity_descriptions"] == body_dict["activity_descriptions"]


@pytest.mark.asyncio
async def test_create_subscription_1(mocker):
    """
    create_subscription should return the subscription when collection_path is present
    """

    collection_path = SubscriptionCollectionPath.SLEEP
    member_id = "12345"

    body_dict = INDIVIDUAL_SUBSCRIPTION

    mock_httpx_response(
        mocker, body=INDIVIDUAL_SUBSCRIPTION, status_code=200, text=MOCK_RESPONSE
    )

    result = await fitbit_loader.create_subscription(
        MOCK_TOKEN, collection_path, member_id
    )

    assert isinstance(result, (Subscription))
    assert result.collection_type == body_dict["collectionType"]
    assert result.owner_id == body_dict["ownerId"]
    assert result.owner_type == body_dict["ownerType"]
    assert result.susbcriber_id == body_dict["subscriberId"]
    assert result.subscription_id == body_dict["subscriptionId"]


@pytest.mark.asyncio
async def test_create_subscription_2(mocker):
    """
    create_subscription should return the subscription when collection_path is not present
    """

    collection_path = None
    member_id = "12345"

    body_dict = INDIVIDUAL_SUBSCRIPTION

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.create_subscription(
        MOCK_TOKEN, collection_path, member_id
    )

    assert isinstance(result, (Subscription))
    assert result.collection_type == body_dict["collectionType"]
    assert result.owner_id == body_dict["ownerId"]
    assert result.owner_type == body_dict["ownerType"]
    assert result.susbcriber_id == body_dict["subscriberId"]
    assert result.subscription_id == body_dict["subscriptionId"]


@pytest.mark.asyncio
async def test_get_subscriptions_1(mocker):
    """
    get_subscriptions should return the subscriptions when collection_path is present
    """

    collection_path = SubscriptionCollectionPath.SLEEP
    user_id = "12345"

    body_dict = {"apiSubscriptions": [INDIVIDUAL_SUBSCRIPTION]}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_subscriptions(MOCK_TOKEN, collection_path, user_id)

    assert isinstance(result, (SubscriptionResponse))


@pytest.mark.asyncio
async def test_get_subscriptions_2(mocker):
    """
    get_subscriptions should return the subscriptions when collection_path is not present
    """

    collection_path = None
    user_id = "12345"

    body_dict = {"apiSubscriptions": [INDIVIDUAL_SUBSCRIPTION]}

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.get_subscriptions(MOCK_TOKEN, collection_path, user_id)

    assert isinstance(result, (SubscriptionResponse))


@pytest.mark.asyncio
async def test_delete_subscription_1(mocker):
    """
    delete_subscription should return the subscription when get_subscriptions response return subscriptions
    """

    collection_path = SubscriptionCollectionPath.SLEEP
    user_id = "12345"

    body_dict = {"apiSubscriptions": [INDIVIDUAL_SUBSCRIPTION]}

    mocker.patch(
        "httpx.AsyncClient.request",
        side_effect=[
            type(
                "ResponseClass",
                (),
                {"status_code": 200, "text": MOCK_RESPONSE, "json": lambda: body_dict},
            ),
            type("ResponseClass", (), {"status_code": 204, "text": MOCK_RESPONSE}),
        ],
    )

    result = await fitbit_loader.delete_subscription(
        MOCK_TOKEN, collection_path, user_id
    )

    assert result is True


@pytest.mark.asyncio
async def test_refresh_token_1(mocker):
    """
    refresh_token should return the token
    """

    refresh_token = "Mock refresh token"

    body_dict = {
        "token": MOCK_TOKEN,
        "expires_in": 12345,
        "refresh_token": "Mock refresh token",
    }

    mock_httpx_response(mocker, body=body_dict, status_code=200, text=MOCK_RESPONSE)

    result = await fitbit_loader.refresh_token(refresh_token)

    assert isinstance(result, (RefreshTokenResp))


def mock_httpx_response(mocker, body={}, status_code=200, text=""):
    object_response = type(
        "ResponseClass",
        (),
        {"status_code": status_code, "text": text, "json": lambda: body},
    )

    mocker.patch("httpx.AsyncClient.request", return_value=object_response)
