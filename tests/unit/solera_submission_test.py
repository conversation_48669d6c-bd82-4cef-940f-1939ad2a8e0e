import pytest
import pendulum
import logging
from unittest.mock import patch

from mint_vault.solera import get_milestone_participants
from ciba_participant.activity.models import ParticipantActivityEnum
from freezegun import freeze_time

logger = logging.getLogger(__name__)


class Activity:
    """Mock Activity that uses Pendulum for 'created_at'."""

    def __init__(self, activity_type, created_at: pendulum.DateTime):
        self.activity_type = activity_type
        self.created_at = created_at

    def __repr__(self):
        return f"Activity(type={self.activity_type}, created_at={self.created_at.isoformat()})"


@pytest.fixture(scope="class", autouse=True)
def freeze_time_fixture():
    """
    Freezes Pendulum's 'now' to 1 AM UTC on 2025-02-01 for all tests in this class,
    to mimic the script running daily at 1 AM.
    """
    frozen_time = freeze_time("2025-02-01 01:00:00", tz_offset=0)
    with frozen_time:
        yield


@pytest.mark.asyncio
@patch("logging.Logger.warning")
class TestGetMilestoneParticipantsAt1Am:
    """
    Each scenario is tested in its own method. The clock is frozen at 1 AM UTC, 2025-02-01.
    Exactly 24h is considered 'in' => participants go to m1_activities.
    """

    async def test_scenario_1_older_than_24h(self, mock_warning):
        """Scenario 1: Cohort start is 25h ago => goes to other_m_activities."""
        now = pendulum.now("UTC")  # 2025-02-01 01:00:00
        older_than_24h = now.subtract(hours=25)

        activities = [
            Activity(ParticipantActivityEnum.GROUP, older_than_24h.add(minutes=30))
        ]
        merged = {("p_old", older_than_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_old" in other_m_activities
        assert "p_old" not in m1_activities
        assert (
            mock_warning.call_count == 0
        )  # No warning for valid group, just older date

    async def test_scenario_2_within_24h_first_activity_weight(self, mock_warning):
        """Scenario 2: Within 24h, first activity = WEIGHT => goes to m1_activities."""
        now = pendulum.now("UTC")
        within_24h = now.subtract(hours=1)

        activities = [
            Activity(ParticipantActivityEnum.WEIGHT, within_24h),
            Activity(ParticipantActivityEnum.GROUP, within_24h.add(minutes=5)),
        ]
        merged = {("p_weight_first", within_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_weight_first" in m1_activities
        assert "p_weight_first" not in other_m_activities

        stored_activities = m1_activities["p_weight_first"]
        assert stored_activities[0].activity_type == ParticipantActivityEnum.WEIGHT
        assert mock_warning.call_count == 0

    async def test_scenario_3_within_24h_first_not_weight_but_has_weight(
        self, mock_warning
    ):
        """Scenario 3: Within 24h, first is NOT WEIGHT, but there's a WEIGHT => shift earliest WEIGHT."""
        now = pendulum.now("UTC")
        within_24h = now.subtract(hours=1)

        first_not_weight_time = within_24h.add(minutes=10)
        earliest_weight_time = within_24h.add(minutes=15)

        activities = [
            Activity(ParticipantActivityEnum.ENROLL, first_not_weight_time),
            Activity(ParticipantActivityEnum.WEIGHT, earliest_weight_time),
        ]
        merged = {("p_not_weight_first", within_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_not_weight_first" in m1_activities
        assert "p_not_weight_first" not in other_m_activities

        updated = m1_activities["p_not_weight_first"]
        # The earliest weight should be 5 min before the first activity
        expected_time = first_not_weight_time.subtract(minutes=5)
        assert updated[0].activity_type == ParticipantActivityEnum.WEIGHT
        assert updated[0].created_at == expected_time
        assert mock_warning.call_count == 0

    async def test_scenario_4_within_24h_no_weight(self, mock_warning):
        """Scenario 4: Within 24h, but no WEIGHT => logs warning & participant is skipped."""
        now = pendulum.now("UTC")
        within_24h = now.subtract(hours=1)

        activities = [
            Activity(ParticipantActivityEnum.ENROLL, within_24h.add(minutes=5))
        ]
        merged = {("p_no_weight", within_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_no_weight" in m1_activities, (
            "p_no_weight should not be in m1_activities"
        )
        assert "p_no_weight" not in other_m_activities, (
            "p_no_weight should not be in other_m_activities"
        )

    async def test_scenario_5_within_24h_empty_activities(self, mock_warning):
        """Scenario 5: Within 24h, but no activities => skip."""
        now = pendulum.now("UTC")
        within_24h = now.subtract(hours=1)
        merged = {("p_empty", within_24h): []}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_empty" not in m1_activities
        assert "p_empty" not in other_m_activities
        assert mock_warning.call_count == 0

    async def test_scenario_6_exactly_24h_is_older(self, mock_warning):
        """Scenario 6: Exactly 24h => treat as in => goes to m1_activities."""
        now = pendulum.now("UTC")
        boundary_24h = now.subtract(hours=24)

        activities = [
            Activity(ParticipantActivityEnum.WEIGHT, boundary_24h.add(minutes=1))
        ]
        merged = {("p_24h_boundary", boundary_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_24h_boundary" in m1_activities
        assert "p_24h_boundary" not in other_m_activities
        assert mock_warning.call_count == 0

    async def test_scenario_7_future_cohort(self, mock_warning):
        """Scenario 7: Cohort in the future (e.g. 2h from now) => within 24h => goes to m1_activities."""
        now = pendulum.now("UTC")
        future_time = now.add(hours=2)

        activities = [
            Activity(ParticipantActivityEnum.WEIGHT, future_time.add(minutes=5))
        ]
        merged = {("p_future", future_time): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_future" not in m1_activities
        assert "p_future" not in other_m_activities
        assert mock_warning.call_count == 0

    async def test_scenario_8_multiple_weights_first_not_weight(self, mock_warning):
        """
        Scenario 8: Multiple WEIGHTS;
        earliest WEIGHT is moved if the first activity is not weight.
        """
        now = pendulum.now("UTC")
        within_24h = now.subtract(hours=1)

        activities = [
            Activity(ParticipantActivityEnum.ENROLL, within_24h.add(minutes=1)),
            Activity(ParticipantActivityEnum.WEIGHT, within_24h.add(minutes=3)),
            Activity(ParticipantActivityEnum.WEIGHT, within_24h.add(minutes=4)),
        ]
        merged = {("p_multi_weight", within_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_multi_weight" in m1_activities
        assert "p_multi_weight" not in other_m_activities

        updated = m1_activities["p_multi_weight"]
        earliest_moved = within_24h.add(minutes=1).subtract(minutes=5)
        assert updated[0].activity_type == ParticipantActivityEnum.WEIGHT
        assert updated[0].created_at == earliest_moved
        # The second WEIGHT remains where it was
        assert mock_warning.call_count == 0

    async def test_scenario_9_tie_timestamp_between_first_and_earliest_weight(
        self, mock_warning
    ):
        """Scenario 9: First activity & earliest WEIGHT share the same timestamp => shift WEIGHT by 5 min."""
        now = pendulum.now("UTC")
        within_24h = now.subtract(hours=1)

        tie_time = within_24h.add(minutes=10)
        activities = [
            Activity(ParticipantActivityEnum.ENROLL, tie_time),
            Activity(ParticipantActivityEnum.WEIGHT, tie_time),
        ]
        merged = {("p_tie_timestamp", within_24h): activities}

        m1_activities, other_m_activities = await get_milestone_participants(merged)
        assert "p_tie_timestamp" in m1_activities
        assert "p_tie_timestamp" not in other_m_activities

        updated = m1_activities["p_tie_timestamp"]
        expected_shifted = tie_time.subtract(minutes=5)
        assert updated[0].activity_type == ParticipantActivityEnum.WEIGHT
        assert updated[0].created_at == expected_shifted
        assert mock_warning.call_count == 0
