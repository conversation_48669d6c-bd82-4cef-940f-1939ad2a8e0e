import pytest
from datetime import datetime
from unittest.mock import AsyncMock, patch
from ciba_iot_etl.models.pydantic.common import Platform
from app.services.withings import MemberWithings, WithingsServiceException


class DummyQuery:
    def __init__(self, result):
        self.result = result

    async def get_or_none(self):
        # Return the dummy result asynchronously
        return self.result


@pytest.fixture
def mock_member():
    """Fixture to create a mock Member object with necessary attributes and methods."""
    member = AsyncMock()
    member.get_withings_id.return_value = None
    member.get_platforms.return_value = []
    member.id = "mock_member_id"  # Add the 'id' attribute
    member.created_at = datetime.now()
    return member


@pytest.fixture
def mock_withings_loader():
    """Fixture to create a mock WithingsLoader object with necessary methods."""
    loader = AsyncMock()
    loader.get_token.return_value = {
        "body": {
            "access_token": "token",
            "refresh_token": "refresh",
            "userid": "user_id",
            "expires_in": 3600,
        }
    }
    return loader


@pytest.fixture
def member_withings(mock_withings_loader, mock_member):
    """Fixture to create a MemberWithings instance with mocked dependencies."""
    return MemberWithings(client=mock_withings_loader, member=mock_member)


@pytest.mark.asyncio
async def test_handle_subscribe_to_account_failure(
    member_withings, mock_withings_loader
):
    """
    Test that handle_subscribe_to_account returns an error message on failure.

    This test simulates an exception during token retrieval.
    It ensures that the method returns a response containing an error message.
    """
    mock_withings_loader.get_token.side_effect = Exception("Error")
    response = await member_withings.handle_subscribe_to_account(
        state="state", code="code", email="email", site="site"
    )
    assert "error" in response


@pytest.mark.asyncio
async def test_subcribe_to_user_devices_existing(member_withings, mock_member):
    """
    Test that subscribe_to_user_devices returns an existing Withings ID.

    This test mocks the Member object to simulate an existing Withings ID.
    It ensures that the method returns the existing ID without making further API calls.
    """
    withings = AsyncMock()
    withings.access_token = "mock_access_token"
    withings.id = "existing_id"
    withings.user_id = "user_id"
    withings.member_id = "mock_member_id"
    mock_member.get_withings_id = AsyncMock(return_value=withings.id)
    dummy_query = DummyQuery(withings)
    with patch("app.services.withings.Withings.filter") as mocked_device:
        mocked_device.return_value = dummy_query
        withings_id = await member_withings.subcribe_to_user_devices(
            state="state", code="code", email="email", site="site"
        )
        assert withings_id == "existing_id"


@pytest.mark.asyncio
async def test_subcribe_to_notification_success(
    member_withings, mock_withings_loader
):
    """
    Test that subcribe_to_notification returns True on success.

    This test mocks the WithingsLoader to simulate successful subscription creation.
    It ensures that the method returns True when all subscriptions are successful.
    """
    mock_withings_loader.create_subscription.return_value = {"status": 0}
    withings = AsyncMock()
    withings.access_token = "mock_access_token"
    withings.id = "mock_withings_id"
    with patch(
        "app.services.withings.Withings.get_or_none",
        new_callable=AsyncMock,
        return_value=withings,
    ):
        result = await member_withings.subcribe_to_notification(
            withings_id="mock_withings_id"
        )
        assert result


@pytest.mark.asyncio
async def test_subcribe_to_notification_failure(
    member_withings, mock_withings_loader
):
    """
    Test that subcribe_to_notification returns False on failure.

    This test mocks the WithingsLoader to simulate a failed subscription creation.
    It ensures that the method returns False when any subscription fails.
    """
    mock_withings_loader.create_subscription.return_value = {
        "status": 1,
        "error": "error",
    }
    withings = AsyncMock()
    withings.access_token = "mock_access_token"
    withings.id = "mock_withings_id"
    with patch(
        "app.services.withings.Withings.get_or_none",
        new_callable=AsyncMock,
        return_value=withings,
    ):
        result = await member_withings.subcribe_to_notification(
            withings_id="mock_withings_id"
        )
        assert not result


@pytest.mark.asyncio
async def test_handle_notification_process(member_withings, mock_member):
    """
    Test that handle_notification processes notifications for supported applications.

    This test mocks the Member object to simulate platform retrieval.
    It ensures that the method processes notifications for supported application types.
    """
    mock_member.get_platforms.return_value = [
        Platform(id="mock_platform_id", type="patient")
    ]
    mock_member.get_withings_id.return_value = "withings_id"
    with pytest.raises(WithingsServiceException):
        await member_withings.handle_notification(
            userid=1,
            appli=1,
            startdate=0,
            enddate=0,
            correlation_id="correlation_id_test",
        )


@pytest.mark.asyncio
async def test_send_pull_notification_success(member_withings, mock_member):
    """
    Test that send_pull_notification returns True on success.

    This test mocks the queue service to simulate successful message sending.
    It ensures that the method returns True when the notification is sent successfully.
    """
    mock_member.get_withings_id.return_value = "withings_id"
    with patch("app.services.queue.get_queue") as mock_get_queue:
        mock_queue = AsyncMock()
        mock_get_queue.return_value = mock_queue
        with pytest.raises(WithingsServiceException):
            await member_withings.send_pull_notification(
                startdate=0,
                enddate=0,
                platforms=[],
                correlation_id="correlation_id_test",
            )


@pytest.mark.asyncio
async def test_send_pull_notification_failure(member_withings, mock_member):
    """
    Test that send_pull_notification raises WithingsServiceException when Withings ID is missing.

    This test ensures that the method raises an exception when the Withings ID is not found.
    """
    mock_member.get_withings_id.return_value = None
    with pytest.raises(WithingsServiceException):
        await member_withings.send_pull_notification(
            startdate=0,
            enddate=0,
            platforms=[],
            correlation_id="correlation_id_test",
        )


@pytest.mark.asyncio
async def test_sync_device(member_withings, mock_member):
    """
    Test that sync_device sends a pull notification.

    This test mocks the queue service to simulate successful message sending.
    It ensures that the method sends a pull notification to sync the device.
    """
    mock_member.get_platforms.return_value = [
        Platform(id="mock_platform_id", type="patient")
    ]
    mock_member.get_withings_id.return_value = "withings_id"
    with patch("app.services.queue.get_queue") as mock_get_queue:
        mock_queue = AsyncMock()
        mock_get_queue.return_value = mock_queue
        with pytest.raises(WithingsServiceException):
            await member_withings.sync_device(
                start_date=0, correlation_id="correlation_id_test"
            )


@pytest.mark.asyncio
async def test_disconnect_device_success(
    member_withings, mock_withings_loader, mock_member
):
    """
    Test that disconnect_device returns True on successful disconnection.

    This test mocks the WithingsLoader to simulate successful subscription deletion.
    It ensures that the method returns True when the device is successfully disconnected.
    """
    mock_withings_loader.delete_subscription.return_value = {"status": 0}
    mock_member.get_withings_id.return_value = "withings_id"
    with patch(
        "ciba_iot_etl.models.db.withings.Withings.delete_withings",
        new_callable=AsyncMock,
    ):
        result = await member_withings.disconnect_device()
        assert result, result


@pytest.mark.asyncio
async def test_disconnect_device_failure(
    member_withings, mock_withings_loader, mock_member
):
    """
    Test that disconnect_device handles errors during disconnection.

    This test mocks the WithingsLoader to simulate a failed subscription deletion.
    It ensures that the method handles errors gracefully and still returns True.
    """
    mock_withings_loader.delete_subscription.return_value = {
        "status": 1,
        "error": "error",
    }
    mock_member.get_withings_id.return_value = "withings_id"
    with patch(
        "ciba_iot_etl.models.db.withings.Withings.delete_withings",
        new_callable=AsyncMock,
    ):
        result = await member_withings.disconnect_device()
        assert result, result


@pytest.mark.asyncio
async def test_renew_credential_success(
    member_withings, mock_withings_loader, mock_member
):
    """
    Test that renew_credential updates tokens successfully.

    This test mocks the WithingsLoader to simulate successful token refresh.
    It ensures that the method updates the member's credentials with new tokens.
    """
    mock_member.get_withings_refresh_token.return_value = "refresh_token"
    mock_withings_loader.refresh_token.return_value = {
        "body": {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
        }
    }
    await member_withings.renew_credential()


@pytest.mark.asyncio
async def test_renew_credential_failure(
    member_withings, mock_withings_loader, mock_member
):
    """
    Test that renew_credential raises WithingsServiceException when refresh token is missing.

    This test ensures that the method raises an exception when the refresh token is not found.
    """
    mock_member.get_withings_refresh_token.return_value = None
    with pytest.raises(WithingsServiceException):
        await member_withings.renew_credential()
