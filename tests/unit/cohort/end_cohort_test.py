import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from contextlib import asynccontextmanager

import pytest
import pendulum

from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
    CohortMembershipStatus,
    CohortStatusEnum,
)
from ciba_participant.participant.models import ParticipantStatus
from ciba_participant.cohort.crud.end_cohort import process, CohortNotEndedException


@asynccontextmanager
async def mock_cohort_filter(cohort_result=None):
    """Mock Cohort.filter() chain."""
    with patch.object(Cohort, "filter", autospec=True) as mock_filter:
        mock_filter_obj = MagicMock()
        mock_filter_obj.prefetch_related.return_value = mock_filter_obj
        mock_filter_obj.first = AsyncMock(return_value=cohort_result)
        mock_filter_obj.update = AsyncMock(return_value=None)
        mock_filter.return_value = mock_filter_obj

        yield mock_filter, mock_filter_obj


@asynccontextmanager
async def mock_cohort_members_filter(participant_ids=None):
    """Mock CohortMembers.filter() chain."""
    with patch.object(CohortMembers, "filter", autospec=True) as mock_filter:
        mock_filter_obj = MagicMock()
        mock_filter_obj.values_list = AsyncMock(return_value=participant_ids or [])
        mock_filter.return_value = mock_filter_obj

        yield mock_filter, mock_filter_obj


@asynccontextmanager
async def setup_end_cohort_mocks(
    cohort_filter_result=None,
    cohort_members_filter_result=None,
    exception_on_cohort_update=None,
    mock_pendulum_now=False,
):
    """Set up all the mocks needed for end_cohort tests."""
    # Mock the transaction context
    transaction_patch = patch("ciba_participant.cohort.crud.end_cohort.in_transaction")

    # Mock the logger
    logger_patch = patch("ciba_participant.cohort.crud.end_cohort.logger")

    # Conditionally mock pendulum.now
    pendulum_now_patch = None
    mock_pendulum_now_obj = None
    if mock_pendulum_now:
        pendulum_now_patch = patch("ciba_participant.cohort.crud.end_cohort.pendulum.now")

    # Start all patches
    mock_transaction = transaction_patch.start()
    mock_logger = logger_patch.start()
    if pendulum_now_patch:
        mock_pendulum_now_obj = pendulum_now_patch.start()

    try:
        # Set up transaction context manager
        mock_transaction.return_value.__aenter__ = AsyncMock()
        mock_transaction.return_value.__aexit__ = AsyncMock()

        # Set up pendulum.now to return a real pendulum object by default if mocked
        # Use a fixed base time to ensure consistent comparisons
        base_time = pendulum.now("UTC")
        if mock_pendulum_now_obj:
            mock_pendulum_now_obj.return_value = base_time

        # Use the individual mock context managers
        async with mock_cohort_filter(cohort_filter_result) as (mock_cohort_filter_func, mock_cohort_filter_obj):
            async with mock_cohort_members_filter(cohort_members_filter_result) as (mock_cohort_members_filter_func, mock_cohort_members_filter_obj):

                # Handle exception on cohort update if specified
                if exception_on_cohort_update:
                    mock_cohort_filter_obj.update = AsyncMock(side_effect=exception_on_cohort_update)

                # Create results dictionary
                mocks = {
                    "cohort_filter": mock_cohort_filter_func,
                    "cohort_filter_obj": mock_cohort_filter_obj,
                    "cohort_members_filter": mock_cohort_members_filter_func,
                    "cohort_members_filter_obj": mock_cohort_members_filter_obj,
                    "transaction": mock_transaction,
                    "logger": mock_logger,
                    "pendulum_now": mock_pendulum_now_obj,
                }

                yield mocks
    finally:
        # Stop all patches explicitly to ensure cleanup
        transaction_patch.stop()
        logger_patch.stop()
        if pendulum_now_patch:
            pendulum_now_patch.stop()


@pytest.fixture
def mock_cohort():
    """Create a mock cohort for testing."""
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = "test-cohort"

    # Create an awaitable that returns a real pendulum object
    # Use a fixed date to avoid timing issues
    fixed_future_date = pendulum.now("UTC").add(days=30)
    async def end_date_coro():
        return fixed_future_date

    cohort.end_date = end_date_coro()

    return cohort


@pytest.fixture
def mock_cohort_near_end():
    """Create a mock cohort that is near end for testing."""
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = "test-cohort-near-end"

    # Create an awaitable that returns a real pendulum object
    # Use a fixed date to avoid timing issues
    fixed_near_end_date = pendulum.now("UTC").add(days=20)
    async def end_date_coro():
        return fixed_near_end_date

    cohort.end_date = end_date_coro()

    return cohort


@pytest.fixture
def mock_participant_ids():
    """Create mock participant IDs for testing."""
    return [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]


@pytest.mark.asyncio
async def test_process_cohort_end_success(mock_cohort_near_end, mock_participant_ids):
    """Test successful cohort ending process."""
    # Arrange
    cohort_id = mock_cohort_near_end.id

    # Mock the Participant model that gets imported inside the function
    with patch("ciba_participant.participant.models.Participant") as mock_participant_model:
        mock_participant_filter = AsyncMock()
        mock_participant_filter.update = AsyncMock()
        mock_participant_model.filter.return_value = mock_participant_filter

        async with setup_end_cohort_mocks(
            cohort_filter_result=mock_cohort_near_end,
            cohort_members_filter_result=mock_participant_ids,
        ) as mocks:
            # Act
            result = await process(cohort_id)

            # Assert
            assert result is True

            # Verify cohort was updated to COMPLETED
            mocks["cohort_filter_obj"].update.assert_called_once_with(status=CohortStatusEnum.COMPLETED)

            # Verify participants were updated to COMPLETED
            mock_participant_model.filter.assert_called_once_with(id__in=mock_participant_ids)
            mock_participant_filter.update.assert_called_once_with(status=ParticipantStatus.COMPLETED)

            # Verify logging
            mocks["logger"].info.assert_called_once_with(
                f"Successfully processed cohort {cohort_id} with {len(mock_participant_ids)} participants"
            )


@pytest.mark.asyncio
async def test_process_cohort_end_success_no_participants(mock_cohort_near_end):
    """Test successful cohort ending process with no participants."""
    # Arrange
    cohort_id = mock_cohort_near_end.id
    empty_participant_ids = []

    async with setup_end_cohort_mocks(
        cohort_filter_result=mock_cohort_near_end,
        cohort_members_filter_result=empty_participant_ids,
    ) as mocks:
        # Act
        result = await process(cohort_id)

        # Assert
        assert result is True

        # Verify cohort was updated to COMPLETED
        mocks["cohort_filter_obj"].update.assert_called_once_with(status=CohortStatusEnum.COMPLETED)

        # Verify logging
        mocks["logger"].info.assert_called_once_with(
            f"Successfully processed cohort {cohort_id} with {len(empty_participant_ids)} participants"
        )


@pytest.mark.asyncio
async def test_process_cohort_not_found():
    """Test processing when cohort is not found."""
    # Arrange
    cohort_id = uuid.uuid4()

    async with setup_end_cohort_mocks(
        cohort_filter_result=None,
    ) as mocks:
        # Act & Assert
        with pytest.raises(ValueError, match=f"Cohort with ID {cohort_id} not found"):
            await process(cohort_id)


@pytest.mark.asyncio
async def test_process_cohort_not_near_end(mock_cohort):
    """Test processing when cohort is not near end."""
    # Arrange
    cohort_id = mock_cohort.id

    async with setup_end_cohort_mocks(
        cohort_filter_result=mock_cohort,
        mock_pendulum_now=True,
    ) as mocks:
        # Mock pendulum.now to return a date that makes the cohort not near end
        mock_now = pendulum.now("UTC")
        mocks["pendulum_now"].return_value = mock_now

        # Act & Assert
        with pytest.raises(CohortNotEndedException):
            await process(cohort_id)


@pytest.mark.asyncio
async def test_process_cohort_end_exception_during_processing(mock_cohort_near_end, mock_participant_ids):
    """Test handling of exceptions during cohort processing."""
    # Arrange
    cohort_id = mock_cohort_near_end.id

    async with setup_end_cohort_mocks(
        cohort_filter_result=mock_cohort_near_end,
        cohort_members_filter_result=mock_participant_ids,
        exception_on_cohort_update=Exception("Database error"),
    ) as mocks:
        # Act & Assert
        with pytest.raises(Exception, match="Database error"):
            await process(cohort_id)

        # Verify error logging
        mocks["logger"].error.assert_called_once_with(
            f"Failed to process cohort end {cohort_id}: Database error"
        )


@pytest.mark.asyncio
async def test_process_cohort_end_participant_update_exception(mock_cohort_near_end, mock_participant_ids):
    """Test handling of exceptions during participant update."""
    # Arrange
    cohort_id = mock_cohort_near_end.id

    # Mock the Participant model that gets imported inside the function
    with patch("ciba_participant.participant.models.Participant") as mock_participant_model:
        mock_participant_filter = AsyncMock()
        mock_participant_filter.update = AsyncMock(side_effect=Exception("Participant update error"))
        mock_participant_model.filter.return_value = mock_participant_filter

        async with setup_end_cohort_mocks(
            cohort_filter_result=mock_cohort_near_end,
            cohort_members_filter_result=mock_participant_ids,
        ) as mocks:
            # Act & Assert
            with pytest.raises(Exception, match="Participant update error"):
                await process(cohort_id)

            # Verify error logging
            mocks["logger"].error.assert_called_once_with(
                f"Failed to process cohort end {cohort_id}: Participant update error"
            )


@pytest.mark.asyncio
async def test_process_cohort_end_date_boundary_exactly_28_days():
    """Test cohort ending when end_date is exactly 28 days from now."""
    # Arrange
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = "test-cohort-boundary"

    # Create an awaitable that returns a real pendulum object
    # Use a fixed date to avoid timing issues
    fixed_boundary_date = pendulum.now("UTC").add(days=28)
    async def end_date_coro():
        return fixed_boundary_date

    cohort.end_date = end_date_coro()

    cohort_id = cohort.id
    participant_ids = [uuid.uuid4()]

    # Mock the Participant model that gets imported inside the function
    with patch("ciba_participant.participant.models.Participant") as mock_participant_model:
        mock_participant_filter = AsyncMock()
        mock_participant_filter.update = AsyncMock()
        mock_participant_model.filter.return_value = mock_participant_filter

        async with setup_end_cohort_mocks(
            cohort_filter_result=cohort,
            cohort_members_filter_result=participant_ids,
        ) as mocks:
            # Act
            result = await process(cohort_id)

            # Assert
            assert result is True

            # Verify cohort was updated to COMPLETED
            mocks["cohort_filter_obj"].update.assert_called_once_with(status=CohortStatusEnum.COMPLETED)

            # Verify participants were updated to COMPLETED
            mock_participant_model.filter.assert_called_once_with(id__in=participant_ids)
            mock_participant_filter.update.assert_called_once_with(status=ParticipantStatus.COMPLETED)


@pytest.mark.asyncio
async def test_process_cohort_end_date_boundary_over_28_days():
    """Test cohort ending when end_date is over 28 days from now (should raise exception)."""
    # Arrange
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = "test-cohort-too-far"

    # Create an awaitable that returns a real pendulum object
    # Use a fixed date to avoid timing issues
    fixed_far_date = pendulum.now("UTC").add(days=29)
    async def end_date_coro():
        return fixed_far_date

    cohort.end_date = end_date_coro()

    cohort_id = cohort.id

    async with setup_end_cohort_mocks(
        cohort_filter_result=cohort,
        mock_pendulum_now=True,
    ) as mocks:
        # Mock pendulum.now to return a consistent time
        mock_now = pendulum.now("UTC")
        mocks["pendulum_now"].return_value = mock_now

        # Act & Assert
        with pytest.raises(CohortNotEndedException):
            await process(cohort_id)


@pytest.mark.asyncio
async def test_process_cohort_end_with_none_end_date():
    """Test cohort ending when end_date is None."""
    # Arrange
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = "test-cohort-no-end-date"

    # Mock the end_date property as an AsyncMock that returns None
    cohort.end_date = AsyncMock(return_value=None)

    cohort_id = cohort.id

    async with setup_end_cohort_mocks(
        cohort_filter_result=cohort,
    ) as mocks:
        # Act & Assert
        # When end_date is None, the comparison with pendulum.now().add(days=28) should fail
        # This will likely raise a TypeError or similar exception
        with pytest.raises((TypeError, AttributeError)):
            await process(cohort_id)


@pytest.mark.asyncio
async def test_cohort_not_ended_exception_inheritance():
    """Test that CohortNotEndedException is properly inherited from ValueError."""
    # Arrange & Act
    exception = CohortNotEndedException("Test message")

    # Assert
    assert isinstance(exception, ValueError)
    assert isinstance(exception, CohortNotEndedException)
