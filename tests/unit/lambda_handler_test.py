import base64
import json
import pytest
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch

from push_notifications.notification_template import NotificationTemplate
from ciba_participant.notifications.push.queries import ParticipantPushOutput
from push_notifications.app import (
    send_daily_push,
    send_chat_push,
    send_daily_class_reminder,
    send_hourly_class_reminder,
    send_class_reminder,
    ensure_db_initialized,
    async_lambda_handler,
    lambda_handler,
    send_batch_messages,
    BATCH_SIZE,
)


@pytest.fixture
def mock_notification_template():
    """Returns a mocked NotificationTemplate that returns a test notification message."""
    template = MagicMock(spec=NotificationTemplate)
    template.notification_message.return_value = {"message": "test notification"}
    return template


@pytest.fixture
def mock_participant():
    """Returns a mock ParticipantPushOutput with test user data."""
    return ParticipantPushOutput(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )


@pytest.fixture
def mock_sqs():
    """Returns a mocked SQS client that returns successful responses for all messages."""
    sqs = MagicMock()
    sqs.send_message.return_value = {"MessageId": "test-message-id"}

    # Create a side effect function to return dynamic successful responses
    def message_batch_side_effect(QueueUrl, Entries):
        return {"Successful": [{"Id": entry["Id"]} for entry in Entries], "Failed": []}

    sqs.send_message_batch.side_effect = message_batch_side_effect
    return sqs


@pytest.fixture
def mock_event_daily():
    """Returns a mock event that simulates a daily push notification trigger from CloudWatch Events."""
    return {
        "source": "aws.events",
        "resources": [
            "arn:aws:events:us-east-1:123456789012:rule/DailyParticipantPushNotifications"
        ],
    }


@pytest.fixture
def mock_event_daily_class():
    """Returns a mock event that simulates a daily class reminder trigger from CloudWatch Events."""
    return {
        "source": "aws.events",
        "resources": ["arn:aws:events:us-east-1:123456789012:rule/DailyClassReminder"],
    }


@pytest.fixture
def mock_event_hourly_class():
    """Returns a mock event that simulates an hourly class reminder trigger from CloudWatch Events."""
    return {
        "source": "aws.events",
        "resources": ["arn:aws:events:us-east-1:123456789012:rule/HourlyClassReminder"],
    }


@pytest.fixture
def mock_event_chat():
    """Returns a mock event that simulates a chat notification trigger from API Gateway."""
    return {
        "requestContext": {"apiId": "api123"},
        "isBase64Encoded": False,
        "body": "From=client123&ChannelSid=CH123",
    }


@pytest.fixture
def mock_event_chat_base64():
    """Returns a mock event with base64 encoded body that simulates a chat notification trigger."""
    body = "From=client123&ChannelSid=CH123"
    encoded = base64.b64encode(body.encode()).decode()
    return {
        "requestContext": {"apiId": "api123"},
        "isBase64Encoded": True,
        "body": encoded,
    }


@pytest.fixture
def mock_context():
    """Returns a mock Lambda context object with test function name."""
    context = MagicMock()
    context.function_name = "test-function"
    return context


@pytest.mark.asyncio
async def test_send_batch_messages_successful(mock_sqs):
    """Tests successful batch message sending."""
    messages = [{"message": f"test-{i}"} for i in range(5)]

    sent, failed = await send_batch_messages(mock_sqs, "queue-url", messages)

    assert sent == 5
    assert len(failed) == 0
    assert mock_sqs.send_message_batch.call_count == 1
    mock_sqs.send_message_batch.assert_called_with(
        QueueUrl="queue-url",
        Entries=[
            {"Id": "0", "MessageBody": json.dumps({"message": "test-0"})},
            {"Id": "1", "MessageBody": json.dumps({"message": "test-1"})},
            {"Id": "2", "MessageBody": json.dumps({"message": "test-2"})},
            {"Id": "3", "MessageBody": json.dumps({"message": "test-3"})},
            {"Id": "4", "MessageBody": json.dumps({"message": "test-4"})},
        ],
    )


@pytest.mark.asyncio
async def test_send_batch_messages_with_failures(mock_sqs):
    """Tests batch message sending with some failures."""
    messages = [{"message": f"test-{i}"} for i in range(3)]

    # Mock a partial failure
    mock_sqs.send_message_batch.return_value = {
        "Successful": [{"Id": "0"}],
        "Failed": [{"Id": "1"}, {"Id": "2"}],
    }

    # For retry, make it successful
    mock_sqs.send_message_batch.side_effect = [
        {"Successful": [{"Id": "0"}], "Failed": [{"Id": "1"}, {"Id": "2"}]},
        {"Successful": [{"Id": "0"}, {"Id": "1"}], "Failed": []},
    ]

    sent, failed = await send_batch_messages(mock_sqs, "queue-url", messages)

    assert sent == 3
    assert len(failed) == 0
    assert mock_sqs.send_message_batch.call_count == 2


@pytest.mark.asyncio
async def test_send_batch_messages_large_batch(mock_sqs):
    """Tests sending messages larger than batch size."""
    # Create more messages than batch size
    messages = [{"message": f"test-{i}"} for i in range(BATCH_SIZE + 5)]

    sent, failed = await send_batch_messages(mock_sqs, "queue-url", messages)

    assert sent == BATCH_SIZE + 5
    assert len(failed) == 0
    assert mock_sqs.send_message_batch.call_count == 2


@pytest.mark.asyncio
async def test_send_batch_messages_exception_retry(mock_sqs):
    """Tests retry behavior when exceptions occur."""
    messages = [{"message": "test"}]

    # First call raises exception, second succeeds
    mock_sqs.send_message_batch.side_effect = [
        RuntimeError("Test error"),
        {"Successful": [{"Id": "0"}], "Failed": []},
    ]

    sent, failed = await send_batch_messages(mock_sqs, "queue-url", messages)

    assert sent == 1
    assert len(failed) == 0
    assert mock_sqs.send_message_batch.call_count == 2


@pytest.mark.asyncio
async def test_send_batch_messages_max_retries_exceeded(mock_sqs):
    """Tests behavior when max retries are exceeded."""
    messages = [{"message": "test"}]

    # Always raise exception
    mock_sqs.send_message_batch.side_effect = RuntimeError("Test error")

    with pytest.raises(RuntimeError):
        await send_batch_messages(mock_sqs, "queue-url", messages)


@pytest.mark.asyncio
async def test_ensure_db_initialized():
    """
    Tests that ensure_db_initialized calls init_db only once.
    """
    with patch(
        "push_notifications.app.init_db", new_callable=AsyncMock
    ) as mock_init_db:
        await ensure_db_initialized()
        mock_init_db.assert_called_once()

        # Second call should not reinitialize
        await ensure_db_initialized()
        mock_init_db.assert_called_once()


@pytest.mark.asyncio
async def test_send_daily_push_with_users(mock_notification_template, mock_sqs):
    """
    Tests send_daily_push when users are available.
    """
    mock_user = ParticipantPushOutput(
        email="<EMAIL>",
        last_name="Test",
        first_name="User",
    )

    with (
        patch(
            "push_notifications.app.get_start_module_participants",
            new_callable=AsyncMock,
        ) as mock_start_module,
        patch(
            "push_notifications.app.get_participants_no_weight_activity",
            new_callable=AsyncMock,
        ) as mock_no_weight,
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_start_module.return_value = [mock_user]
        mock_no_weight.return_value = [mock_user]
        mock_get_sqs.return_value = mock_sqs
        mock_send_batch.side_effect = [(1, []), (3, [])]

        await send_daily_push(mock_notification_template, "us-east-1", "queue-url")

        # Check batch message was called twice (once for each user type)
        assert mock_send_batch.call_count == 2
        mock_notification_template.notification_message.assert_any_call(
            participant=mock_user.model_dump(), type="no_weight_input"
        )


@pytest.mark.asyncio
async def test_send_daily_push_no_users(mock_notification_template, mock_sqs):
    """
    Tests send_daily_push when no users are available.
    """
    with (
        patch(
            "push_notifications.app.get_start_module_participants",
            new_callable=AsyncMock,
        ) as mock_start_module,
        patch(
            "push_notifications.app.get_participants_no_weight_activity",
            new_callable=AsyncMock,
        ) as mock_no_weight,
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_start_module.return_value = []
        mock_no_weight.return_value = []
        mock_get_sqs.return_value = mock_sqs

        await send_daily_push(mock_notification_template, "us-east-1", "queue-url")

        mock_send_batch.assert_not_called()


@pytest.mark.asyncio
async def test_send_daily_push_exception(mock_notification_template, mock_sqs):
    """Tests exception handling in send_daily_push."""
    with (
        patch(
            "push_notifications.app.get_start_module_participants",
            new_callable=AsyncMock,
        ) as mock_start_module,
        patch(
            "push_notifications.app.get_participants_no_weight_activity",
            new_callable=AsyncMock,
        ),
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
    ):
        mock_start_module.side_effect = RuntimeError("Test error")
        mock_get_sqs.return_value = mock_sqs

        with pytest.raises(RuntimeError):
            await send_daily_push(mock_notification_template, "us-east-1", "queue-url")


@pytest.mark.asyncio
async def test_send_chat_push_with_external_ids(mock_notification_template, mock_sqs):
    """
    Tests send_chat_push when external IDs are found for the chat event.
    """
    mock_participant = ParticipantPushOutput(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )

    with (
        patch("push_notifications.app.get_chat_query") as mock_get_chat_query,
        patch(
            "push_notifications.app.query_db", new_callable=AsyncMock
        ) as mock_query_db,
        patch(
            "push_notifications.app.get_participants", new_callable=AsyncMock
        ) as mock_get_participants,
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_get_chat_query.return_value = "SELECT query"
        mock_query_db.return_value = [{"external_id": "123"}]
        mock_get_participants.return_value = [mock_participant]
        mock_get_sqs.return_value = mock_sqs
        mock_send_batch.return_value = (1, [])

        event = {"isBase64Encoded": False, "body": "From=client123&ChannelSid=CH123"}

        await send_chat_push(
            event, mock_notification_template, "us-east-1", "queue-url"
        )

        mock_get_chat_query.assert_called_once_with("client123", "CH123")
        mock_query_db.assert_called_once_with("SELECT query")
        mock_get_participants.assert_called_once_with(["123"])
        mock_send_batch.assert_called_once()


@pytest.mark.asyncio
async def test_send_chat_push_base64_encoded(mock_notification_template, mock_sqs):
    """
    Tests send_chat_push with a base64 encoded request body.
    """
    body = "From=client123&ChannelSid=CH123"
    encoded = base64.b64encode(body.encode()).decode()
    event = {"isBase64Encoded": True, "body": encoded}

    with (
        patch("push_notifications.app.get_chat_query") as mock_get_chat_query,
        patch(
            "push_notifications.app.query_db", new_callable=AsyncMock
        ) as mock_query_db,
        patch(
            "push_notifications.app.get_participants", new_callable=AsyncMock
        ) as mock_get_participants,
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_get_chat_query.return_value = "SELECT query"
        mock_query_db.return_value = [{"external_id": "123"}]
        mock_get_participants.return_value = []
        mock_get_sqs.return_value = mock_sqs

        await send_chat_push(
            event, mock_notification_template, "us-east-1", "queue-url"
        )

        mock_get_chat_query.assert_called_once_with("client123", "CH123")
        mock_query_db.assert_called_once()
        mock_get_participants.assert_called_once()
        mock_send_batch.assert_not_called()


@pytest.mark.asyncio
async def test_send_chat_push_no_external_ids(mock_notification_template, mock_sqs):
    """
    Tests send_chat_push when no external IDs are found.
    """
    with (
        patch("push_notifications.app.get_chat_query") as mock_get_chat_query,
        patch(
            "push_notifications.app.query_db", new_callable=AsyncMock
        ) as mock_query_db,
        patch(
            "push_notifications.app.get_participants", new_callable=AsyncMock
        ) as mock_get_participants,
        patch("push_notifications.app.get_sqs_client"),
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_get_chat_query.return_value = "SELECT query"
        mock_query_db.return_value = []

        event = {"isBase64Encoded": False, "body": "From=client123&ChannelSid=CH123"}

        await send_chat_push(
            event, mock_notification_template, "us-east-1", "queue-url"
        )

        mock_get_chat_query.assert_called_once()
        mock_query_db.assert_called_once()
        mock_get_participants.assert_not_called()
        mock_send_batch.assert_not_called()


@pytest.mark.asyncio
async def test_send_chat_push_exception(mock_notification_template):
    """Tests exception handling in send_chat_push."""
    with patch("push_notifications.app.get_chat_query") as mock_get_chat_query:
        mock_get_chat_query.side_effect = RuntimeError("Test error")

        event = {"isBase64Encoded": False, "body": "From=client123&ChannelSid=CH123"}

        with pytest.raises(RuntimeError):
            await send_chat_push(
                event, mock_notification_template, "us-east-1", "queue-url"
            )


@pytest.mark.asyncio
async def test_send_class_reminder_with_participants(
    mock_notification_template, mock_sqs, mock_participant
):
    """
    Tests send_class_reminder when participants are provided.
    """
    with (
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_get_sqs.return_value = mock_sqs
        mock_send_batch.return_value = (1, [])

        sent, failed = await send_class_reminder(
            notification_template=mock_notification_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
            participants=[mock_participant],
            reminder_type="test_reminder",
        )

        assert sent == 1
        assert failed == 0
        mock_notification_template.notification_message.assert_called_once_with(
            participant=mock_participant.model_dump(), type="test_reminder"
        )
        mock_send_batch.assert_called_once()


@pytest.mark.asyncio
async def test_send_class_reminder_no_participants(
    mock_notification_template, mock_sqs
):
    """
    Tests send_class_reminder when no participants are provided.
    """
    with (
        patch("push_notifications.app.get_sqs_client") as mock_get_sqs,
        patch(
            "push_notifications.app.send_batch_messages", new_callable=AsyncMock
        ) as mock_send_batch,
    ):
        mock_get_sqs.return_value = mock_sqs

        sent, failed = await send_class_reminder(
            notification_template=mock_notification_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
            participants=[],
            reminder_type="test_reminder",
        )

        assert sent == 0
        assert failed == 0
        mock_notification_template.notification_message.assert_not_called()
        mock_send_batch.assert_not_called()


@pytest.mark.asyncio
async def test_send_class_reminder_exception(
    mock_notification_template, mock_participant
):
    """Tests exception handling in send_class_reminder."""
    with patch("push_notifications.app.get_sqs_client") as mock_get_sqs:
        mock_get_sqs.side_effect = RuntimeError("Test error")

        with pytest.raises(RuntimeError):
            await send_class_reminder(
                notification_template=mock_notification_template,
                sqs_region="us-east-1",
                sqs_queue="queue-url",
                participants=[mock_participant],
                reminder_type="test_reminder",
            )


@pytest.mark.asyncio
async def test_send_daily_class_reminder(
    mock_notification_template, mock_sqs, mock_participant
):
    """
    Tests send_daily_class_reminder functionality.
    """
    with (
        patch(
            "push_notifications.app.get_participants_with_class_tomorrow",
            new_callable=AsyncMock,
        ) as mock_get_participants,
        patch(
            "push_notifications.app.send_class_reminder", new_callable=AsyncMock
        ) as mock_send_reminder,
    ):
        mock_get_participants.return_value = [mock_participant]
        mock_send_reminder.return_value = (1, 0)

        sent, failed = await send_daily_class_reminder(
            notification_template=mock_notification_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
        )

        assert sent == 1
        assert failed == 0
        mock_get_participants.assert_called_once_with(timezone="America/Los_Angeles")
        mock_send_reminder.assert_called_once_with(
            notification_template=mock_notification_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
            participants=[mock_participant],
            reminder_type="before_live_session_24_hours",
        )


@pytest.mark.asyncio
async def test_send_daily_class_reminder_exception(mock_notification_template):
    """Tests exception handling in send_daily_class_reminder."""
    with patch(
        "push_notifications.app.get_participants_with_class_tomorrow",
        new_callable=AsyncMock,
    ) as mock_get_participants:
        mock_get_participants.side_effect = RuntimeError("Test error")

        with pytest.raises(RuntimeError):
            await send_daily_class_reminder(
                notification_template=mock_notification_template,
                sqs_region="us-east-1",
                sqs_queue="queue-url",
            )


@pytest.mark.asyncio
async def test_send_hourly_class_reminder(
    mock_notification_template, mock_sqs, mock_participant
):
    """
    Tests send_hourly_class_reminder functionality.
    """
    with (
        patch(
            "push_notifications.app.get_participants_with_class_in_next_hour",
            new_callable=AsyncMock,
        ) as mock_get_participants,
        patch(
            "push_notifications.app.send_class_reminder", new_callable=AsyncMock
        ) as mock_send_reminder,
    ):
        mock_get_participants.return_value = [mock_participant]
        mock_send_reminder.return_value = (1, 0)

        sent, failed = await send_hourly_class_reminder(
            notification_template=mock_notification_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
        )

        assert sent == 1
        assert failed == 0
        mock_get_participants.assert_called_once()
        mock_send_reminder.assert_called_once_with(
            notification_template=mock_notification_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
            participants=[mock_participant],
            reminder_type="before_live_session_1_hour",
        )


@pytest.mark.asyncio
async def test_send_hourly_class_reminder_exception(mock_notification_template):
    """Tests exception handling in send_hourly_class_reminder."""
    with patch(
        "push_notifications.app.get_participants_with_class_in_next_hour",
        new_callable=AsyncMock,
    ) as mock_get_participants:
        mock_get_participants.side_effect = RuntimeError("Test error")

        with pytest.raises(RuntimeError):
            await send_hourly_class_reminder(
                notification_template=mock_notification_template,
                sqs_region="us-east-1",
                sqs_queue="queue-url",
            )


@pytest.mark.asyncio
async def test_async_lambda_handler_daily_push(mock_event_daily, mock_context):
    """
    Tests async_lambda_handler for daily push notification events.
    """
    with (
        patch(
            "push_notifications.app.ensure_db_initialized", new_callable=AsyncMock
        ) as mock_ensure_db,
        patch("push_notifications.app.NotificationTemplate") as mock_template_cls,
        patch("push_notifications.app.get_parameter") as mock_get_parameter,
        patch(
            "push_notifications.app.send_daily_push", new_callable=AsyncMock
        ) as mock_send_daily,
        patch(
            "push_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        mock_template = MagicMock()
        mock_template_cls.return_value = mock_template
        mock_get_parameter.side_effect = ["us-east-1", "queue-url"]

        result = await async_lambda_handler(mock_event_daily, mock_context)

        mock_ensure_db.assert_called_once()
        mock_send_daily.assert_called_once_with(
            notification_template=mock_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
        )
        mock_close_db.assert_called_once()
        assert result["statusCode"] == 200


@pytest.mark.asyncio
async def test_async_lambda_handler_daily_class_reminder(
    mock_event_daily_class, mock_context
):
    """
    Tests async_lambda_handler for daily class reminder events with updated return values.
    """
    with (
        patch(
            "push_notifications.app.ensure_db_initialized", new_callable=AsyncMock
        ) as mock_ensure_db,
        patch("push_notifications.app.NotificationTemplate") as mock_template_cls,
        patch("push_notifications.app.get_parameter") as mock_get_parameter,
        patch(
            "push_notifications.app.send_daily_class_reminder", new_callable=AsyncMock
        ) as mock_send_daily_class,
        patch(
            "push_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        mock_template = MagicMock()
        mock_template_cls.return_value = mock_template
        mock_get_parameter.side_effect = ["us-east-1", "queue-url"]
        mock_send_daily_class.return_value = (5, 1)  # 5 sent, 1 failed

        result = await async_lambda_handler(mock_event_daily_class, mock_context)

        mock_ensure_db.assert_called_once()
        mock_send_daily_class.assert_called_once_with(
            notification_template=mock_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
        )
        mock_close_db.assert_called_once()
        assert result["statusCode"] == 200
        assert result["body"] == {"sent": 5, "failed": 1}


@pytest.mark.asyncio
async def test_async_lambda_handler_hourly_class_reminder(
    mock_event_hourly_class, mock_context
):
    """
    Tests async_lambda_handler for hourly class reminder events with updated return values.
    """
    with (
        patch(
            "push_notifications.app.ensure_db_initialized", new_callable=AsyncMock
        ) as mock_ensure_db,
        patch("push_notifications.app.NotificationTemplate") as mock_template_cls,
        patch("push_notifications.app.get_parameter") as mock_get_parameter,
        patch(
            "push_notifications.app.send_hourly_class_reminder",
            new_callable=AsyncMock,
        ) as mock_send_hourly_class,
        patch(
            "push_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        mock_template = MagicMock()
        mock_template_cls.return_value = mock_template
        mock_get_parameter.side_effect = ["us-east-1", "queue-url"]
        mock_send_hourly_class.return_value = (3, 0)  # 3 sent, 0 failed

        result = await async_lambda_handler(mock_event_hourly_class, mock_context)

        mock_ensure_db.assert_called_once()
        mock_send_hourly_class.assert_called_once_with(
            notification_template=mock_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
        )
        mock_close_db.assert_called_once()
        assert result["statusCode"] == 200
        assert result["body"] == {"sent": 3, "failed": 0}


@pytest.mark.asyncio
async def test_async_lambda_handler_chat_push(mock_event_chat, mock_context):
    """
    Tests async_lambda_handler for chat push notification events.
    """
    with (
        patch(
            "push_notifications.app.ensure_db_initialized", new_callable=AsyncMock
        ) as mock_ensure_db,
        patch("push_notifications.app.NotificationTemplate") as mock_template_cls,
        patch("push_notifications.app.get_parameter") as mock_get_parameter,
        patch(
            "push_notifications.app.send_chat_push", new_callable=AsyncMock
        ) as mock_send_chat,
        patch(
            "push_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        mock_template = MagicMock()
        mock_template_cls.return_value = mock_template
        mock_get_parameter.side_effect = ["us-east-1", "queue-url"]

        result = await async_lambda_handler(mock_event_chat, mock_context)

        mock_ensure_db.assert_called_once()
        mock_send_chat.assert_called_once_with(
            event=mock_event_chat,
            notification_template=mock_template,
            sqs_region="us-east-1",
            sqs_queue="queue-url",
        )
        mock_close_db.assert_called_once()
        assert result["statusCode"] == 200


@pytest.mark.asyncio
async def test_async_lambda_handler_unknown_source(mock_context):
    """
    Tests async_lambda_handler for events with unknown sources.

    Should initialize and close the database but not call any notification functions.
    Returns a 200 status code response even when the event source is unknown.
    """
    unknown_event = {"source": "unknown"}

    with (
        patch(
            "push_notifications.app.ensure_db_initialized", new_callable=AsyncMock
        ) as mock_ensure_db,
        patch("push_notifications.app.NotificationTemplate") as mock_template_cls,
        patch("push_notifications.app.get_parameter") as mock_get_parameter,
        patch(
            "push_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        mock_template = MagicMock()
        mock_template_cls.return_value = mock_template
        mock_get_parameter.side_effect = ["us-east-1", "queue-url"]

        result = await async_lambda_handler(unknown_event, mock_context)

        mock_ensure_db.assert_called_once()
        mock_close_db.assert_called_once()
        assert result["statusCode"] == 200


@pytest.mark.asyncio
async def test_async_lambda_handler_exception(mock_event_daily, mock_context):
    """
    Tests async_lambda_handler error handling.

    Should report errors to Slack and SNS when an exception occurs.
    Returns a 500 status code response with error details in the body.
    """
    with (
        patch(
            "push_notifications.app.ensure_db_initialized", new_callable=AsyncMock
        ) as mock_ensure_db,
        patch("push_notifications.app.NotificationTemplate"),
        patch("push_notifications.app.get_parameter"),
        patch(
            "push_notifications.app.send_daily_push", new_callable=AsyncMock
        ) as mock_send_daily,
        patch("push_notifications.app.SlackNotification") as mock_slack,
        patch("push_notifications.app.publish_to_sns") as mock_publish,
        patch(
            "push_notifications.app.close_db", new_callable=AsyncMock
        ) as mock_close_db,
    ):
        mock_ensure_db.side_effect = Exception("Test error")
        mock_slack_instance = MagicMock()
        mock_slack.return_value = mock_slack_instance

        result = await async_lambda_handler(mock_event_daily, mock_context)

        mock_ensure_db.assert_called_once()
        mock_send_daily.assert_not_called()
        mock_slack.assert_called_once()
        mock_publish.assert_called_once()
        mock_close_db.assert_called_once()
        assert result["statusCode"] == 500
        assert "error" in result["body"]


def test_lambda_handler(mock_event_daily, mock_context):
    """
    Tests the synchronous lambda_handler wrapper function.

    Should call asyncio.run with the async_lambda_handler function and provided event/context.
    Returns a JSON string with the response from the async handler.
    """
    with (
        patch("push_notifications.app.asyncio.run") as mock_run,
        patch("push_notifications.app.async_lambda_handler"),
    ):
        mock_run.return_value = {"statusCode": 200}

        result = lambda_handler(mock_event_daily, mock_context)

        mock_run.assert_called_once()
        assert result == json.dumps({"statusCode": 200})
