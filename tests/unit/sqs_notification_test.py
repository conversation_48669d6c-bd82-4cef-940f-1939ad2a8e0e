import json
from unittest.mock import patch

import pytest

from email_notifications.app import handle_sqs_event
from ciba_participant.common.aws_handler import EmailNotificationEvent
from tests.unit.conftest import create_sqs_event


@pytest.mark.asyncio
async def test_handle_sqs_event_new_participant(mock_email_handler):
    """Test handle_sqs_event function with NEW_PARTICIPANT event"""
    # Arrange
    participant_id = "test-participant-id"
    event = create_sqs_event(
        EmailNotificationEvent.NEW_PARTICIPANT.value, {"participant_id": participant_id}
    )

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.new_participant_joined_email.assert_called_once_with(
        participant_id=participant_id
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_reset_password(mock_email_handler):
    """Test handle_sqs_event function with RESET_PASSWORD event"""
    # Arrange
    data = {
        "email": "<EMAIL>",
        "reset_code": "123456",
        "sub": "test-user-id",
        "user_type": "participant",
    }
    event = create_sqs_event(EmailNotificationEvent.RESET_PASSWORD.value, data)

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.send_reset_password_email.assert_called_once_with(
        email=data["email"],
        reset_code=data["reset_code"],
        user_id=data["sub"],
        user_type=data["user_type"],
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_welcome_participant(mock_email_handler):
    """Test handle_sqs_event function with WELCOME_PARTICIPANT event"""
    # Arrange
    participant_id = "test-participant-id"
    event = create_sqs_event(
        EmailNotificationEvent.WELCOME_PARTICIPANT.value,
        {"participant_id": participant_id},
    )

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.send_welcome_email.assert_called_once_with(
        participant_id=participant_id
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_disenroll_participant(mock_email_handler):
    """Test handle_sqs_event function with DISENROLL_PARTICIPANT event"""
    # Arrange
    participant_id = "test-participant-id"
    event = create_sqs_event(
        EmailNotificationEvent.DISENROLL_PARTICIPANT.value,
        {"participant_id": participant_id},
    )

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.send_disenrolled_email.assert_called_once_with(
        participant_id=participant_id
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_with_bytes_body(mock_email_handler):
    """Test handle_sqs_event function with body as bytes"""
    # Arrange
    participant_id = "test-participant-id"
    event = {
        "Records": [
            {
                "body": json.dumps(
                    {
                        "type": "sqs",
                        "email_event": EmailNotificationEvent.NEW_PARTICIPANT.value,
                        "data": {"participant_id": participant_id},
                        "correlation_id": "test-correlation-id",
                    }
                ).encode("utf-8")
            }
        ]
    }

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.new_participant_joined_email.assert_called_once_with(
        participant_id=participant_id
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_multiple_records(mock_email_handler):
    """Test handle_sqs_event function with multiple records"""
    # Arrange
    participant_id_1 = "test-participant-id-1"
    participant_id_2 = "test-participant-id-2"
    event = {
        "Records": [
            {
                "body": json.dumps(
                    {
                        "type": "sqs",
                        "email_event": EmailNotificationEvent.NEW_PARTICIPANT.value,
                        "data": {"participant_id": participant_id_1},
                        "correlation_id": "test-correlation-id-1",
                    }
                )
            },
            {
                "body": json.dumps(
                    {
                        "type": "sqs",
                        "email_event": EmailNotificationEvent.WELCOME_PARTICIPANT.value,
                        "data": {"participant_id": participant_id_2},
                        "correlation_id": "test-correlation-id-2",
                    }
                )
            },
        ]
    }

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    # Only the first record should be processed
    mock_email_handler.new_participant_joined_email.assert_called_once_with(
        participant_id=participant_id_1
    )
    mock_email_handler.send_welcome_email.assert_not_called()
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_unsupported_event(mock_email_handler):
    """Test handle_sqs_event function with unsupported event"""
    # Arrange
    # Use a valid email_event but we'll patch the handler to simulate an unsupported event
    event = create_sqs_event(
        EmailNotificationEvent.NEW_PARTICIPANT.value,
        {"participant_id": "test-participant-id"},
    )

    # Mock the handler to return an unsupported event response
    mock_email_handler.new_participant_joined_email.side_effect = ValueError(
        "Unsupported event"
    )

    # Act and Assert
    with patch("email_notifications.app.logger"):
        with pytest.raises(ValueError, match="Unsupported event"):
            await handle_sqs_event(event, mock_email_handler)

    # Verify the handler was called
    mock_email_handler.new_participant_joined_email.assert_called_once()


@pytest.mark.asyncio
async def test_handle_sqs_event_invalid_json(mock_email_handler):
    """Test handle_sqs_event function with invalid JSON in body"""
    # Arrange
    event = {"Records": [{"body": "invalid json"}]}

    # Act and Assert
    with pytest.raises(json.JSONDecodeError):
        await handle_sqs_event(event, mock_email_handler)
