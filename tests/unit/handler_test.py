import json
from unittest.mock import patch

import pytest

from email_notifications.app import (
    lambda_handler,
    async_lambda_handler,
    ensure_db_initialized,
    handle_sqs_event,
)
from tests.unit.conftest import create_cloudwatch_event, create_sqs_event


@pytest.mark.asyncio
async def test_ensure_db_initialized(mock_db_functions):
    """Test ensure_db_initialized function"""
    mock_init_db, _ = mock_db_functions

    # First call should initialize DB
    await ensure_db_initialized()
    mock_init_db.assert_called_once()

    # Second call should not initialize DB again
    await ensure_db_initialized()
    assert mock_init_db.call_count == 1


@pytest.mark.asyncio
async def test_handle_sqs_event_new_participant(mock_email_handler):
    """Test handle_sqs_event function with NEW_PARTICIPANT event"""
    # Arrange
    event = create_sqs_event(
        email_event="new_participant", data={"participant_id": "test-participant-id"}
    )

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.new_participant_joined_email.assert_called_once_with(
        participant_id="test-participant-id"
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_reset_password(mock_email_handler):
    """Test handle_sqs_event function with RESET_PASSWORD event"""
    # Arrange
    data = {
        "email": "<EMAIL>",
        "reset_code": "123456",
        "sub": "test-user-id",
        "user_type": "participant",
    }
    event = create_sqs_event(email_event="reset_password", data=data)

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.send_reset_password_email.assert_called_once_with(
        email=data["email"],
        reset_code=data["reset_code"],
        user_id=data["sub"],
        user_type=data["user_type"],
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_welcome_participant(mock_email_handler):
    """Test handle_sqs_event function with WELCOME_PARTICIPANT event"""
    # Arrange
    event = create_sqs_event(
        email_event="welcome_participant",
        data={"participant_id": "test-participant-id"},
    )

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.send_welcome_email.assert_called_once_with(
        participant_id="test-participant-id"
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_disenroll_participant(mock_email_handler):
    """Test handle_sqs_event function with DISENROLL_PARTICIPANT event"""
    # Arrange
    event = create_sqs_event(
        email_event="disenroll_participant",
        data={"participant_id": "test-participant-id"},
    )

    # Act
    result = await handle_sqs_event(event, mock_email_handler)

    # Assert
    mock_email_handler.send_disenrolled_email.assert_called_once_with(
        participant_id="test-participant-id"
    )
    assert result == {}


@pytest.mark.asyncio
async def test_handle_sqs_event_unsupported_event(mock_email_handler):
    """Test handle_sqs_event function with unsupported event"""
    # Arrange
    # Use a valid email_event but we'll patch the handler to simulate an unsupported event
    event = create_sqs_event(
        email_event="new_participant", data={"participant_id": "test-participant-id"}
    )

    # Mock the handler to return an unsupported event response
    mock_email_handler.new_participant_joined_email.side_effect = ValueError(
        "Unsupported event"
    )

    # Act and Assert
    with patch("email_notifications.app.logger"):
        with pytest.raises(ValueError, match="Unsupported event"):
            await handle_sqs_event(event, mock_email_handler)

    # Verify the handler was called
    mock_email_handler.new_participant_joined_email.assert_called_once()


@pytest.mark.asyncio
async def test_async_lambda_handler_cloudwatch_scales_event(
    lambda_context,
    mock_settings,
    mock_db_functions,
    mock_email_handler,
    mock_presigned_url,
    mock_publish_to_sns,
):
    """Test async_lambda_handler function with CloudWatch Scales event"""
    # Arrange
    event = create_cloudwatch_event("Scales")

    # Act
    result = await async_lambda_handler(event, lambda_context)

    # Assert
    mock_email_handler.send_new_participant_email.assert_called_once()
    mock_presigned_url.assert_called_once_with(
        bucket_name=mock_settings.AWS_BUCKET_NAME,
        object_name="test/path/to/file.csv",
        expiration=604799,
    )
    assert result["environment"] == mock_settings.ENV
    assert result["is_test"] == bool(mock_settings.DEBUG)
    assert result["source"] == lambda_context.function_name
    assert result["url"] == "https://test-bucket.s3.amazonaws.com/test/path/to/file.csv"


@pytest.mark.asyncio
async def test_async_lambda_handler_cloudwatch_new_module_event(
    lambda_context, mock_settings, mock_db_functions, mock_email_handler
):
    """Test async_lambda_handler function with CloudWatch NewModule event"""
    # Arrange
    event = create_cloudwatch_event("NewModule")

    # Act
    result = await async_lambda_handler(event, lambda_context)

    # Assert
    mock_email_handler.send_new_module_starting_email.assert_called_once()
    assert result == {}


@pytest.mark.asyncio
async def test_async_lambda_handler_cloudwatch_unsupported_rule(
    lambda_context, mock_settings, mock_db_functions, mock_email_handler
):
    """Test async_lambda_handler function with unsupported CloudWatch rule"""
    # Arrange
    event = create_cloudwatch_event("UnsupportedRule")

    # Act and Assert
    with pytest.raises(ValueError, match="Unsupported rule name: UnsupportedRule"):
        await async_lambda_handler(event, lambda_context)


@pytest.mark.asyncio
async def test_async_lambda_handler_sqs_event(
    lambda_context, mock_settings, mock_db_functions, mock_email_handler
):
    """Test async_lambda_handler function with SQS event"""
    # Arrange
    event = {
        "Records": [
            {
                "eventSource": "aws:sqs",
                "body": json.dumps(
                    {
                        "type": "sqs",
                        "email_event": "welcome_participant",
                        "data": {"participant_id": "test-participant-id"},
                        "correlation_id": "test-correlation-id",
                    }
                ),
            }
        ]
    }

    # Act
    result = await async_lambda_handler(event, lambda_context)

    # Assert
    mock_email_handler.send_welcome_email.assert_called_once_with(
        participant_id="test-participant-id"
    )
    assert result == {}


@pytest.mark.asyncio
async def test_async_lambda_handler_unsupported_event_source(
    lambda_context, mock_settings, mock_db_functions, mock_email_handler
):
    """Test async_lambda_handler function with unsupported event source"""
    # Arrange
    event = {"unsupported": "event"}

    # Act
    result = await async_lambda_handler(event, lambda_context)

    # Assert
    assert result == {"statusCode": 400, "body": json.dumps("Unsupported event source")}


@pytest.mark.asyncio
async def test_async_lambda_handler_exception(
    lambda_context,
    mock_settings,
    mock_db_functions,
    mock_email_handler,
    mock_publish_to_sns,
):
    """Test async_lambda_handler function with exception"""
    # Arrange
    event = create_cloudwatch_event("Scales")
    mock_email_handler.send_new_participant_email.side_effect = Exception(
        "Test exception"
    )

    # Act and Assert
    with pytest.raises(Exception, match="Test exception"):
        await async_lambda_handler(event, lambda_context)

    # Verify SNS is called with error details
    mock_publish_to_sns.assert_called_once()


def test_lambda_handler(lambda_context, mock_settings):
    """Test lambda_handler function"""
    # Arrange
    event = create_cloudwatch_event("Scales")

    # Mock asyncio.run to return a predefined result
    expected_result = {"test": "result"}
    with patch("asyncio.run", return_value=expected_result) as mock_run:
        # Act
        result = lambda_handler(event, lambda_context)

        # Assert
        mock_run.assert_called_once()
        assert result == expected_result
