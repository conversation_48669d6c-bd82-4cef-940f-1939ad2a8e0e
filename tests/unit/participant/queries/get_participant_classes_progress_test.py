import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from uuid import uuid4
from datetime import datetime, timezone, timedelta

from src.schema import schema
from tortoise.exceptions import BaseORMException
from ciba_participant.classes.models import BookingStatusEnum
from src.content_library.messages import DB_READ_ERROR
from src.participant.queries.get_participant_classes_progress import (
    ParticipantNotFoundError,
    FullBookingStatusGraphEnum,
)

# GraphQL query for testing
test_query = """
    query GetParticipantClassesProgress(
        $participantId: UUID!,
        $startDate: DateTime,
        $endDate: DateTime
    ) {
        getParticipantClassesProgress(
            participantId: $participantId,
            startDate: $startDate,
            endDate: $endDate
        ) {
            liveSession {
                id
                title
                description
                meetingStartTime
                hostId
            }
            participant {
                id
                email
                firstName
                lastName
                chatIdentity
            }
            host {
                id
                email
                firstName
                lastName
                role
            }
            status
            lastProgressDate
        }
    }
"""

# Test variables
test_variables = {
    "participantId": str(uuid4()),
    "startDate": None,
    "endDate": None,
}

test_error = "Invalid classes progress query"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_exception, expected_message",
    [
        (
            ParticipantNotFoundError("Participant not found"),
            "Participant not found",
        ),
        (ValueError(test_error), test_error),
        (BaseORMException(), DB_READ_ERROR),
    ],
)
async def test_get_participant_classes_progress_with_exceptions(
    test_exception, expected_message, mock_client_context
):
    """
    getParticipantClassesProgress should handle exceptions gracefully
    and return appropriate error responses.
    """
    # Patch the participant existence check to avoid actual DB calls
    with patch(
        "ciba_participant.participant.models.Participant.filter"
    ) as mock_filter:
        mock_filter.return_value.exists = AsyncMock(side_effect=test_exception)

        # Execute the GraphQL query and expect errors in the response
        result = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        # Verify that the GraphQL response contains errors
        assert result.errors is not None
        assert len(result.errors) > 0

        # Check that the error message contains the expected message
        error_message = str(result.errors[0])
        assert (
            expected_message in error_message
            or str(test_exception) in error_message
        )


@pytest.fixture
def mock_participant_class_progress():
    """Fixture to create a consistent mock participant class progress object"""
    mock_progress = MagicMock()

    # Mock live session
    mock_progress.live_session.id = uuid4()
    mock_progress.live_session.title = "Test Class Session"
    mock_progress.live_session.description = "Test class description"
    mock_progress.live_session.meeting_start_time = datetime.now(
        timezone.utc
    ) + timedelta(hours=1)
    mock_progress.live_session.host_id = uuid4()
    mock_progress.live_session.topic = "Test Topic"
    mock_progress.live_session.timezone = "UTC"
    mock_progress.live_session.has_conflict = False
    mock_progress.live_session.meeting_type = "ZOOM"
    mock_progress.live_session.zoom_id = "123456789"
    mock_progress.live_session.zoom_occurrence_id = "987654321"
    mock_progress.live_session.zoom_link = "https://zoom.us/j/123456789"
    mock_progress.live_session.recording_url = None
    mock_progress.live_session.use_custom_meeting_link = False
    mock_progress.live_session.custom_meeting_link = None
    mock_progress.live_session.max_capacity = 50
    mock_progress.live_session.bookings_count = 25

    # Mock participant
    mock_progress.participant.id = uuid4()
    mock_progress.participant.email = "<EMAIL>"
    mock_progress.participant.first_name = "Test"
    mock_progress.participant.last_name = "User"
    mock_progress.participant.chat_identity = "test_chat_identity"
    mock_progress.participant.group_id = uuid4()
    mock_progress.participant.member_id = uuid4()
    mock_progress.participant.status = "ACTIVE"
    mock_progress.participant.cognito_sub = uuid4()
    mock_progress.participant.medical_record = "MR123456"
    mock_progress.participant.is_test = False
    mock_progress.participant.last_reset = None
    mock_progress.participant.created_at = datetime.now(timezone.utc)
    mock_progress.participant.updated_at = datetime.now(timezone.utc)

    # Mock host
    mock_progress.host.id = uuid4()
    mock_progress.host.email = "<EMAIL>"
    mock_progress.host.first_name = "Host"
    mock_progress.host.last_name = "User"
    mock_progress.host.chat_identity = "host_chat_identity"
    mock_progress.host.role = "PROVIDER"
    mock_progress.host.status = "ACTIVE"
    mock_progress.host.cognito_sub = uuid4()
    mock_progress.host.is_test = False
    mock_progress.host.api_id = "api_123"
    mock_progress.host.support_in_chat = True
    mock_progress.host.classes_admin = True
    mock_progress.host.content_admin = False
    mock_progress.host.created_at = datetime.now(timezone.utc)
    mock_progress.host.updated_at = datetime.now(timezone.utc)

    # Mock booking
    mock_progress.booking.status = BookingStatusEnum.BOOKED
    mock_progress.booking.updated_at = datetime.now(timezone.utc)

    return mock_progress


@pytest.mark.asyncio
async def test_get_participant_classes_progress_success(
    mock_client_context, mock_participant_class_progress
):
    """
    getParticipantClassesProgress should return class progress data without errors.
    """
    # Mock the GraphQL execution result directly
    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantClassesProgress": [
            {
                "liveSession": {
                    "id": str(mock_participant_class_progress.live_session.id),
                    "title": mock_participant_class_progress.live_session.title,
                    "description": mock_participant_class_progress.live_session.description,
                    "meetingStartTime": mock_participant_class_progress.live_session.meeting_start_time.isoformat(),
                    "hostId": str(
                        mock_participant_class_progress.live_session.host_id
                    ),
                },
                "participant": {
                    "id": str(mock_participant_class_progress.participant.id),
                    "email": mock_participant_class_progress.participant.email,
                    "firstName": mock_participant_class_progress.participant.first_name,
                    "lastName": mock_participant_class_progress.participant.last_name,
                    "chatIdentity": mock_participant_class_progress.participant.chat_identity,
                },
                "host": {
                    "id": str(mock_participant_class_progress.host.id),
                    "email": mock_participant_class_progress.host.email,
                    "firstName": mock_participant_class_progress.host.first_name,
                    "lastName": mock_participant_class_progress.host.last_name,
                    "role": mock_participant_class_progress.host.role,
                },
                "status": FullBookingStatusGraphEnum.BOOKED.value,
                "lastProgressDate": mock_participant_class_progress.booking.updated_at.isoformat(),
            }
        ]
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "startDate": None,
                "endDate": None,
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantClassesProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert isinstance(result, list)
        assert len(result) == 1

        class_progress = result[0]

        # Validate live session data
        live_session = class_progress["liveSession"]
        assert live_session["id"] == str(
            mock_participant_class_progress.live_session.id
        )
        assert (
            live_session["title"]
            == mock_participant_class_progress.live_session.title
        )
        assert (
            live_session["description"]
            == mock_participant_class_progress.live_session.description
        )
        assert live_session["hostId"] == str(
            mock_participant_class_progress.live_session.host_id
        )

        # Validate participant data
        participant = class_progress["participant"]
        assert participant["id"] == str(
            mock_participant_class_progress.participant.id
        )
        assert (
            participant["email"]
            == mock_participant_class_progress.participant.email
        )
        assert (
            participant["firstName"]
            == mock_participant_class_progress.participant.first_name
        )
        assert (
            participant["lastName"]
            == mock_participant_class_progress.participant.last_name
        )
        assert (
            participant["chatIdentity"]
            == mock_participant_class_progress.participant.chat_identity
        )

        # Validate host data
        host = class_progress["host"]
        assert host["id"] == str(mock_participant_class_progress.host.id)
        assert host["email"] == mock_participant_class_progress.host.email
        assert (
            host["firstName"]
            == mock_participant_class_progress.host.first_name
        )
        assert (
            host["lastName"] == mock_participant_class_progress.host.last_name
        )
        assert host["role"] == mock_participant_class_progress.host.role

        # Validate status and date
        assert (
            class_progress["status"] == FullBookingStatusGraphEnum.BOOKED.value
        )
        assert (
            class_progress["lastProgressDate"]
            == mock_participant_class_progress.booking.updated_at.isoformat()
        )


@pytest.mark.asyncio
async def test_get_participant_classes_progress_with_date_filters(
    mock_client_context, mock_participant_class_progress
):
    """
    getParticipantClassesProgress should handle date filters correctly.
    """
    start_date = datetime.now(timezone.utc) - timedelta(days=7)
    end_date = datetime.now(timezone.utc) + timedelta(days=7)

    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantClassesProgress": [
            {
                "liveSession": {
                    "id": str(mock_participant_class_progress.live_session.id),
                    "title": mock_participant_class_progress.live_session.title,
                    "description": mock_participant_class_progress.live_session.description,
                    "meetingStartTime": mock_participant_class_progress.live_session.meeting_start_time.isoformat(),
                    "hostId": str(
                        mock_participant_class_progress.live_session.host_id
                    ),
                },
                "participant": {
                    "id": str(mock_participant_class_progress.participant.id),
                    "email": mock_participant_class_progress.participant.email,
                    "firstName": mock_participant_class_progress.participant.first_name,
                    "lastName": mock_participant_class_progress.participant.last_name,
                    "chatIdentity": mock_participant_class_progress.participant.chat_identity,
                },
                "host": {
                    "id": str(mock_participant_class_progress.host.id),
                    "email": mock_participant_class_progress.host.email,
                    "firstName": mock_participant_class_progress.host.first_name,
                    "lastName": mock_participant_class_progress.host.last_name,
                    "role": mock_participant_class_progress.host.role,
                },
                "status": FullBookingStatusGraphEnum.BOOKED.value,
                "lastProgressDate": mock_participant_class_progress.booking.updated_at.isoformat(),
            }
        ]
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantClassesProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert isinstance(result, list)
        assert len(result) == 1


@pytest.mark.asyncio
async def test_get_participant_classes_progress_empty_result(
    mock_client_context,
):
    """
    getParticipantClassesProgress should return empty list when no classes are found.
    """
    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {"getParticipantClassesProgress": []}

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "startDate": None,
                "endDate": None,
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantClassesProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert isinstance(result, list)
        assert len(result) == 0


@pytest.mark.asyncio
async def test_get_participant_classes_progress_multiple_classes(
    mock_client_context, mock_participant_class_progress
):
    """
    getParticipantClassesProgress should handle multiple class progress records.
    """
    # Create a second mock class progress
    mock_progress_2 = MagicMock()
    mock_progress_2.live_session.id = uuid4()
    mock_progress_2.live_session.title = "Second Test Class"
    mock_progress_2.participant.id = (
        mock_participant_class_progress.participant.id
    )
    mock_progress_2.host.id = uuid4()

    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantClassesProgress": [
            {
                "liveSession": {
                    "id": str(mock_participant_class_progress.live_session.id),
                    "title": mock_participant_class_progress.live_session.title,
                    "description": mock_participant_class_progress.live_session.description,
                    "meetingStartTime": mock_participant_class_progress.live_session.meeting_start_time.isoformat(),
                    "hostId": str(
                        mock_participant_class_progress.live_session.host_id
                    ),
                },
                "participant": {
                    "id": str(mock_participant_class_progress.participant.id),
                    "email": mock_participant_class_progress.participant.email,
                    "firstName": mock_participant_class_progress.participant.first_name,
                    "lastName": mock_participant_class_progress.participant.last_name,
                    "chatIdentity": mock_participant_class_progress.participant.chat_identity,
                },
                "host": {
                    "id": str(mock_participant_class_progress.host.id),
                    "email": mock_participant_class_progress.host.email,
                    "firstName": mock_participant_class_progress.host.first_name,
                    "lastName": mock_participant_class_progress.host.last_name,
                    "role": mock_participant_class_progress.host.role,
                },
                "status": FullBookingStatusGraphEnum.BOOKED.value,
                "lastProgressDate": mock_participant_class_progress.booking.updated_at.isoformat(),
            },
            {
                "liveSession": {
                    "id": str(mock_progress_2.live_session.id),
                    "title": mock_progress_2.live_session.title,
                    "description": "Second class description",
                    "meetingStartTime": datetime.now(timezone.utc).isoformat(),
                    "hostId": str(mock_progress_2.host.id),
                },
                "participant": {
                    "id": str(mock_progress_2.participant.id),
                    "email": "<EMAIL>",
                    "firstName": "Test",
                    "lastName": "User",
                    "chatIdentity": "test_chat_identity",
                },
                "host": {
                    "id": str(mock_progress_2.host.id),
                    "email": "<EMAIL>",
                    "firstName": "Host2",
                    "lastName": "User2",
                    "role": "PROVIDER",
                },
                "status": FullBookingStatusGraphEnum.ATTENDED.value,
                "lastProgressDate": datetime.now(timezone.utc).isoformat(),
            },
        ]
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "startDate": None,
                "endDate": None,
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantClassesProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert isinstance(result, list)
        assert len(result) == 2

        # Verify both classes are present
        class_titles = [
            class_progress["liveSession"]["title"] for class_progress in result
        ]
        assert "Test Class Session" in class_titles
        assert "Second Test Class" in class_titles


@pytest.mark.asyncio
async def test_get_participant_classes_progress_different_statuses(
    mock_client_context,
):
    """
    getParticipantClassesProgress should handle different booking statuses correctly.
    """
    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantClassesProgress": [
            {
                "liveSession": {
                    "id": str(uuid4()),
                    "title": "Attended Class",
                    "description": "Class that was attended",
                    "meetingStartTime": datetime.now(timezone.utc).isoformat(),
                    "hostId": str(uuid4()),
                },
                "participant": {
                    "id": str(uuid4()),
                    "email": "<EMAIL>",
                    "firstName": "Test",
                    "lastName": "User",
                    "chatIdentity": "test_chat_identity",
                },
                "host": {
                    "id": str(uuid4()),
                    "email": "<EMAIL>",
                    "firstName": "Host",
                    "lastName": "User",
                    "role": "PROVIDER",
                },
                "status": FullBookingStatusGraphEnum.ATTENDED.value,
                "lastProgressDate": datetime.now(timezone.utc).isoformat(),
            },
            {
                "liveSession": {
                    "id": str(uuid4()),
                    "title": "Missed Class",
                    "description": "Class that was missed",
                    "meetingStartTime": (
                        datetime.now(timezone.utc) - timedelta(hours=1)
                    ).isoformat(),
                    "hostId": str(uuid4()),
                },
                "participant": {
                    "id": str(uuid4()),
                    "email": "<EMAIL>",
                    "firstName": "Test",
                    "lastName": "User",
                    "chatIdentity": "test_chat_identity",
                },
                "host": {
                    "id": str(uuid4()),
                    "email": "<EMAIL>",
                    "firstName": "Host",
                    "lastName": "User",
                    "role": "PROVIDER",
                },
                "status": FullBookingStatusGraphEnum.MISSED.value,
                "lastProgressDate": datetime.now(timezone.utc).isoformat(),
            },
            {
                "liveSession": {
                    "id": str(uuid4()),
                    "title": "Canceled Class",
                    "description": "Class that was canceled",
                    "meetingStartTime": datetime.now(timezone.utc).isoformat(),
                    "hostId": str(uuid4()),
                },
                "participant": {
                    "id": str(uuid4()),
                    "email": "<EMAIL>",
                    "firstName": "Test",
                    "lastName": "User",
                    "chatIdentity": "test_chat_identity",
                },
                "host": {
                    "id": str(uuid4()),
                    "email": "<EMAIL>",
                    "firstName": "Host",
                    "lastName": "User",
                    "role": "PROVIDER",
                },
                "status": FullBookingStatusGraphEnum.CANCELED.value,
                "lastProgressDate": datetime.now(timezone.utc).isoformat(),
            },
        ]
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "startDate": None,
                "endDate": None,
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantClassesProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert isinstance(result, list)
        assert len(result) == 3

        # Verify different statuses are present
        statuses = [class_progress["status"] for class_progress in result]
        assert FullBookingStatusGraphEnum.ATTENDED.value in statuses
        assert FullBookingStatusGraphEnum.MISSED.value in statuses
        assert FullBookingStatusGraphEnum.CANCELED.value in statuses


@pytest.mark.asyncio
async def test_get_participant_classes_progress_invalid_participant_id(
    mock_client_context,
):
    """
    getParticipantClassesProgress should handle invalid participant ID gracefully.
    """
    invalid_participant_id = "invalid-uuid"

    # This should cause a validation error at the GraphQL level
    actual_value = await schema.execute(
        test_query,
        variable_values={
            "participantId": invalid_participant_id,
            "startDate": None,
            "endDate": None,
        },
        context_value=mock_client_context,
    )

    # GraphQL should return validation errors for invalid UUID
    assert actual_value.errors is not None
    assert len(actual_value.errors) > 0


@pytest.mark.asyncio
async def test_get_participant_classes_progress_with_recording_url(
    mock_client_context, mock_participant_class_progress
):
    """
    getParticipantClassesProgress should handle classes with recording URLs.
    """
    # Set recording URL for the mock
    mock_participant_class_progress.live_session.recording_url = (
        "https://example.com/recording.mp4"
    )

    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantClassesProgress": [
            {
                "liveSession": {
                    "id": str(mock_participant_class_progress.live_session.id),
                    "title": mock_participant_class_progress.live_session.title,
                    "description": mock_participant_class_progress.live_session.description,
                    "meetingStartTime": mock_participant_class_progress.live_session.meeting_start_time.isoformat(),
                    "hostId": str(
                        mock_participant_class_progress.live_session.host_id
                    ),
                },
                "participant": {
                    "id": str(mock_participant_class_progress.participant.id),
                    "email": mock_participant_class_progress.participant.email,
                    "firstName": mock_participant_class_progress.participant.first_name,
                    "lastName": mock_participant_class_progress.participant.last_name,
                    "chatIdentity": mock_participant_class_progress.participant.chat_identity,
                },
                "host": {
                    "id": str(mock_participant_class_progress.host.id),
                    "email": mock_participant_class_progress.host.email,
                    "firstName": mock_participant_class_progress.host.first_name,
                    "lastName": mock_participant_class_progress.host.last_name,
                    "role": mock_participant_class_progress.host.role,
                },
                "status": FullBookingStatusGraphEnum.WATCHED_RECORDING.value,
                "lastProgressDate": mock_participant_class_progress.booking.updated_at.isoformat(),
            }
        ]
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "startDate": None,
                "endDate": None,
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantClassesProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert isinstance(result, list)
        assert len(result) == 1

        class_progress = result[0]
        assert (
            class_progress["status"]
            == FullBookingStatusGraphEnum.WATCHED_RECORDING.value
        )
