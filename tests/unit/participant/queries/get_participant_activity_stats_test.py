import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4
from datetime import datetime, timedelta

from src.schema import schema
from tortoise.exceptions import BaseORMException


test_query = """
    query GetParticipantActivityStats($participantId: UUID!) {
      getParticipantActivityStats(participantId: $participantId) {
        lastActivity
        totalModules
        completedModulesCount
        totalActivities
        completedActivitiesCount
        activityHistory {
          week
          date
          physicalActivityMinutes
        }
        weightHistory {
          date
          weight
        }
      }
    }
"""

test_variables = {"participantId": str(uuid4())}


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_exception, expected_result",
    [
        (ValueError("Cohort Member not found"), None),
        (BaseORMException(), None),
    ],
)
async def test_get_participant_activity_stats_with_exceptions(
    test_exception, expected_result, mock_client_context
):
    """
    getParticipantActivityStats should return null when an exception is raised.
    """
    # Patch the database query to avoid actual DB calls
    with patch(
        "ciba_participant.cohort.crud.CohortMembersRepository.get_cohort_member",
        side_effect=test_exception,
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert (
            actual_value.data["getParticipantActivityStats"] == expected_result
        )


@pytest.fixture
def mock_activity_stats():
    """Fixture to create a consistent mock activity stats object"""
    now = datetime.now()

    # Create weight history items
    weight_history = [
        {"date": now - timedelta(days=30), "weight": 75.0},
        {"date": now - timedelta(days=20), "weight": 74.5},
        {"date": now - timedelta(days=10), "weight": 73.8},
        {"date": now, "weight": 72.5},
    ]

    # Create activity history items
    activity_history = [
        {
            "week": 1,
            "date": now - timedelta(days=30),
            "physicalActivityMinutes": 120.0,
        },
        {
            "week": 2,
            "date": now - timedelta(days=20),
            "physicalActivityMinutes": 150.0,
        },
        {
            "week": 3,
            "date": now - timedelta(days=10),
            "physicalActivityMinutes": 180.0,
        },
        {"week": 4, "date": now, "physicalActivityMinutes": 200.0},
    ]

    # Create the mock stats object
    mock_stats = {
        "lastActivity": now,
        "totalModules": 4,
        "completedModulesCount": 2,
        "totalActivities": 20,
        "completedActivitiesCount": 15,
        "weightHistory": weight_history,
        "activityHistory": activity_history,
    }

    return mock_stats


@pytest.mark.asyncio
async def test_get_participant_activity_stats_success(
    mock_client_context, mock_activity_stats
):
    """
    getParticipantActivityStats should return activity stats data without errors.
    """
    # Mock the GraphQL execution result directly
    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantActivityStats": {
            "lastActivity": mock_activity_stats["lastActivity"].isoformat(),
            "totalModules": mock_activity_stats["totalModules"],
            "completedModulesCount": mock_activity_stats[
                "completedModulesCount"
            ],
            "totalActivities": mock_activity_stats["totalActivities"],
            "completedActivitiesCount": mock_activity_stats[
                "completedActivitiesCount"
            ],
            "weightHistory": [
                {"date": item["date"].isoformat(), "weight": item["weight"]}
                for item in mock_activity_stats["weightHistory"]
            ],
            "activityHistory": [
                {
                    "week": item["week"],
                    "date": item["date"].isoformat(),
                    "physicalActivityMinutes": item["physicalActivityMinutes"],
                }
                for item in mock_activity_stats["activityHistory"]
            ],
        }
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantActivityStats"]
        assert actual_value.errors is None
        assert actual_value.data is not None

        # Validate basic stats
        assert result["totalModules"] == mock_activity_stats["totalModules"]
        assert (
            result["completedModulesCount"]
            == mock_activity_stats["completedModulesCount"]
        )
        assert (
            result["totalActivities"] == mock_activity_stats["totalActivities"]
        )
        assert (
            result["completedActivitiesCount"]
            == mock_activity_stats["completedActivitiesCount"]
        )

        # Validate weight history
        assert len(result["weightHistory"]) == len(
            mock_activity_stats["weightHistory"]
        )
        for i, item in enumerate(result["weightHistory"]):
            assert (
                item["weight"]
                == mock_activity_stats["weightHistory"][i]["weight"]
            )

        # Validate activity history
        assert len(result["activityHistory"]) == len(
            mock_activity_stats["activityHistory"]
        )
        for i, item in enumerate(result["activityHistory"]):
            assert (
                item["week"]
                == mock_activity_stats["activityHistory"][i]["week"]
            )
            assert (
                item["physicalActivityMinutes"]
                == mock_activity_stats["activityHistory"][i][
                    "physicalActivityMinutes"
                ]
            )
