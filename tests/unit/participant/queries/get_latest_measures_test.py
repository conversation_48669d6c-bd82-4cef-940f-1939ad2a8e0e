from unittest.mock import patch, AsyncMock, MagicMock
from uuid import uuid4

import pytest
from ciba_participant.rpm_api.exceptions import RPM<PERSON>allError
from ciba_participant.rpm_api.models import LatestData

from src.common.messages import PARTICIPANT_NOT_FOUND
from src.schema import schema

PARTICIPANT_MODEL = "src.participant.queries.get_latest_measures.Participant"
test_query = """
query GetLatestMeasures($participantId: UUID!) {
  getLatestMeasures(participantId: $participantId) {
    success
    error
    lastCibaSync
    lastDeviceSync
    devices {
      id
      deviceType
      lastSyncedAt
    }
    measures {
      value
      unit
      createdAt
    }
  }
}
"""
test_variables = {"participantId": str(uuid4())}


@pytest.fixture
def mock_participant_model():
    model_mock = MagicMock()
    mock_filter = MagicMock()
    model_mock.filter.return_value = mock_filter

    return model_mock, mock_filter


@pytest.mark.asyncio
async def test_get_latest_measures_with_errors(
    mock_client_context, mock_participant_model
):
    """
    get_latest_measures should return unsuccessful response
    when the provided participant is not found.
    """
    participant_mock, mock_filter = mock_participant_model
    mock_filter.get_or_none = AsyncMock(return_value=None)

    with patch(PARTICIPANT_MODEL, participant_mock):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["getLatestMeasures"]["success"] is False
        assert (
            actual_value.data["getLatestMeasures"]["error"]
            == PARTICIPANT_NOT_FOUND
        )


@pytest.mark.asyncio
async def test_get_latest_measures_with_call_error(
    mock_client_context, mock_participant_model
):
    """
    get_latest_measures should return unsuccessful response
    when the proxy call fails.
    """
    test_participant = MagicMock()
    participant_mock, mock_filter = mock_participant_model
    mock_filter.get_or_none = AsyncMock(return_value=test_participant)
    test_error = RPMCallError("Unsupported test error.")

    with (
        patch(PARTICIPANT_MODEL, participant_mock),
        patch(
            "src.participant.queries.get_latest_measures.get_latest_data",
            new_callable=AsyncMock,
            side_effect=test_error,
        ),
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["getLatestMeasures"]["success"] is False
        assert (
            actual_value.data["getLatestMeasures"]["error"]
            == "Unsupported test error."
        )


@pytest.mark.asyncio
async def test_get_latest_measures_with_success(
    mock_client_context, mock_participant_model
):
    """
    get_latest_measures should return successful response.
    """
    test_participant = MagicMock()
    participant_mock, mock_filter = mock_participant_model
    mock_filter.get_or_none = AsyncMock(return_value=test_participant)
    test_response = LatestData()

    with (
        patch(PARTICIPANT_MODEL, participant_mock),
        patch(
            "src.participant.queries.get_latest_measures.get_latest_data",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["getLatestMeasures"]["success"] is True
        assert actual_value.data["getLatestMeasures"]["lastCibaSync"] is None
        assert actual_value.data["getLatestMeasures"]["lastDeviceSync"] is None
        assert actual_value.data["getLatestMeasures"]["devices"] == []
        assert actual_value.data["getLatestMeasures"]["measures"] == []
