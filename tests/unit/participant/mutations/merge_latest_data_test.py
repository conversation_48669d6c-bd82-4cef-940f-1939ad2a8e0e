from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest
from ciba_participant.rpm_api.exceptions import RPM<PERSON>allError

from src.common.messages import PARTICIPANT_NOT_FOUND, SYNC_REQUEST_FAILED
from src.schema import schema

PARTICIPANT_MODEL = "src.participant.mutations.merge_latest_data.Participant"
test_mutation = """
mutation Mutation($participantId: UUID!) {
  mergeLatestData(participantId: $participantId) {
    success
    error
  }
}
"""
test_variables = {
    "participantId": str(uuid4()),
}


@pytest.fixture
def mock_participant_model():
    model_mock = MagicMock()
    mock_filter = MagicMock()
    model_mock.filter.return_value = mock_filter

    return model_mock, mock_filter


@pytest.mark.asyncio
async def test_merge_latest_data_with_participant_not_found(
    mock_client_context, mock_participant_model
):
    """
    merge_latest_data should return unsuccessful response
    when the provided participant is not found.
    """
    participant_mock, mock_filter = mock_participant_model
    mock_filter.get_or_none = AsyncMock(return_value=None)

    with patch(PARTICIPANT_MODEL, participant_mock):
        actual_value = await schema.execute(
            test_mutation,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["mergeLatestData"]["success"] is False
        assert (
            actual_value.data["mergeLatestData"]["error"]
            == PARTICIPANT_NOT_FOUND
        )


@pytest.mark.asyncio
async def test_merge_latest_data_with_call_error(
    mock_client_context, mock_participant_model
):
    """
    merge_latest_data should return unsuccessful response
    when the proxy sync call fails.
    """
    test_participant = MagicMock()
    participant_mock, mock_filter = mock_participant_model
    mock_filter.get_or_none = AsyncMock(return_value=test_participant)
    test_error = RPMCallError("Device connection not found.")

    with (
        patch(PARTICIPANT_MODEL, participant_mock),
        patch(
            "src.participant.mutations.merge_latest_data.sync_measures",
            new_callable=AsyncMock,
            side_effect=test_error,
        ),
    ):
        actual_value = await schema.execute(
            test_mutation,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["mergeLatestData"]["success"] is False
        assert (
            actual_value.data["mergeLatestData"]["error"]
            == "Device connection not found."
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_success, test_error",
    [
        (True, None),
        (False, SYNC_REQUEST_FAILED),
    ],
)
async def test_merge_latest_data_with_successful_call(
    test_success, test_error, mock_participant_model, mock_client_context
):
    """
    merge_latest_data should return the respective success and error.
    """
    test_participant = MagicMock()
    participant_mock, mock_filter = mock_participant_model
    mock_filter.get_or_none = AsyncMock(return_value=test_participant)
    test_response = MagicMock()
    test_response.success = test_success

    with (
        patch(PARTICIPANT_MODEL, participant_mock),
        patch(
            "src.participant.mutations.merge_latest_data.sync_measures",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await schema.execute(
            test_mutation,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["mergeLatestData"]["success"] == test_success
        assert actual_value.data["mergeLatestData"]["error"] == test_error
