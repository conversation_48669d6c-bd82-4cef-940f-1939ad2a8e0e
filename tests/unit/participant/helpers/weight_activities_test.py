from datetime import date

import pendulum
import pytest

from ciba_participant.activity.models import ParticipantActivity
from src.participant.helpers.weight_activities import (
    get_daily_weight_activities,
)
from src.participant.types import DayWeightItem


@pytest.fixture
def mock_weight_activities():
    test_values = [
        {"date": pendulum.parse("2000-01-01T00:30:00"), "value": 200},
        {"date": pendulum.parse("2000-01-01T08:30:00"), "value": 10},
        {"date": pendulum.parse("2000-01-01T16:30:00"), "value": None},
        {"date": pendulum.parse("2000-01-02T00:30:00"), "value": 198.5},
        {"date": pendulum.parse("2000-01-02T08:30:00"), "value": 198.6},
        {"date": pendulum.parse("2000-01-02T16:30:00"), "value": 198.4},
        {"date": pendulum.parse("2000-01-03T00:30:00"), "value": 5},
        {"date": pendulum.parse("2000-01-03T08:30:00"), "value": 198.2},
        {"date": pendulum.parse("2000-01-03T16:30:00"), "value": 9.9},
    ]

    mocked_weight_activities = []

    for test_value in test_values:
        mocked_weight_activities.append(
            ParticipantActivity(
                created_at=test_value["date"],
                value=test_value["value"],
            )
        )

    return mocked_weight_activities


def test_get_daily_weight_activities_success(mock_weight_activities):
    """
    get_daily_weight_activities should return a list of daily weight activities
    filtering values below less than 10
    """
    actual_value = get_daily_weight_activities(mock_weight_activities)

    assert actual_value == [
        DayWeightItem(weight=200, date=date(2000, 1, 1)),
        DayWeightItem(weight=198.4, date=date(2000, 1, 2)),
        DayWeightItem(weight=198.2, date=date(2000, 1, 3)),
    ]


@pytest.mark.parametrize(
    "test_value",
    [
        [
            ParticipantActivity(
                created_at=pendulum.parse("2000-01-01T00:00:00"), value=5
            ),
            ParticipantActivity(
                created_at=pendulum.parse("2000-01-02T00:00:00"), value=None
            ),
        ],
        [],
    ],
)
def test_get_daily_weight_activities_with_empty_result(test_value):
    """
    get_daily_weight_activities should return an empty list
    when no activities were provided
    or when no activities remain after filtering
    """
    actual_value = get_daily_weight_activities(test_value)

    assert actual_value == []
