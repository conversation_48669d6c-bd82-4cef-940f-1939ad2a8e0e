import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from ciba_participant.participant.models import (
    Authorized,
    RawAuthorized,
    AutorizedRole,
)
from ciba_participant.participant.crud import AuthorizedRepository


class TestGetFullAdmins:
    """Test suite for the get_full_admins method."""

    @pytest.mark.asyncio
    async def test_get_full_admins_returns_full_admins_when_exist(self):
        """Test that get_full_admins returns full admins when they exist."""
        # Arrange
        mock_user = MagicMock()
        mock_user.id = uuid.uuid4()
        mock_user.email = "<EMAIL>"
        mock_user.role = AutorizedRole.ADMIN
        mock_user.support_in_chat = True
        mock_user.classes_admin = True
        mock_user.content_admin = True

        mock_raw_authorized = MagicMock(spec=RawAuthorized)
        mock_raw_authorized.id = mock_user.id
        mock_raw_authorized.email = mock_user.email

        with (
            patch.object(Authorized, "filter", new_callable=AsyncMock) as mock_filter,
            patch.object(
                RawAuthorized, "model_validate", return_value=mock_raw_authorized
            ) as mock_validate,
        ):
            mock_filter.return_value = [mock_user]

            # Act
            result = await AuthorizedRepository.get_full_admins()

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1
            assert result[0] == mock_raw_authorized

            # Verify the query was built correctly
            mock_filter.assert_called_once_with(
                role=AutorizedRole.ADMIN,
                support_in_chat=True,
                classes_admin=True,
                content_admin=True,
            )
            mock_validate.assert_called_once_with(mock_user)

    @pytest.mark.asyncio
    async def test_get_full_admins_returns_empty_list_when_no_admins_exist(self):
        """Test that get_full_admins returns empty list when no full admins exist."""
        # Arrange
        with (
            patch.object(Authorized, "filter", new_callable=AsyncMock) as mock_filter,
            patch.object(RawAuthorized, "model_validate") as mock_validate,
        ):
            mock_filter.return_value = []

            # Act
            result = await AuthorizedRepository.get_full_admins()

            # Assert
            assert isinstance(result, list)
            assert len(result) == 0

            # Verify the query was built correctly
            mock_filter.assert_called_once_with(
                role=AutorizedRole.ADMIN,
                support_in_chat=True,
                classes_admin=True,
                content_admin=True,
            )
            mock_validate.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_full_admins_returns_multiple_admins(self):
        """Test that get_full_admins returns multiple admins when they exist."""
        # Arrange
        mock_user1 = MagicMock()
        mock_user1.id = uuid.uuid4()
        mock_user1.email = "<EMAIL>"

        mock_user2 = MagicMock()
        mock_user2.id = uuid.uuid4()
        mock_user2.email = "<EMAIL>"

        mock_raw1 = MagicMock(spec=RawAuthorized)
        mock_raw2 = MagicMock(spec=RawAuthorized)

        with (
            patch.object(Authorized, "filter", new_callable=AsyncMock) as mock_filter,
            patch.object(
                RawAuthorized, "model_validate", side_effect=[mock_raw1, mock_raw2]
            ) as mock_validate,
        ):
            mock_filter.return_value = [mock_user1, mock_user2]

            # Act
            result = await AuthorizedRepository.get_full_admins()

            # Assert
            assert isinstance(result, list)
            assert len(result) == 2
            assert result[0] == mock_raw1
            assert result[1] == mock_raw2

            # Verify model validation was called for each user
            assert mock_validate.call_count == 2
            mock_validate.assert_any_call(mock_user1)
            mock_validate.assert_any_call(mock_user2)

    @pytest.mark.asyncio
    async def test_get_full_admins_handles_database_exception(self):
        """Test that get_full_admins handles database exceptions gracefully."""
        # Arrange
        with patch.object(Authorized, "filter", new_callable=AsyncMock) as mock_filter:
            mock_filter.side_effect = Exception("Database error")

            # Act & Assert
            with pytest.raises(Exception, match="Database error"):
                await AuthorizedRepository.get_full_admins()

            # Verify the query was attempted
            mock_filter.assert_called_once_with(
                role=AutorizedRole.ADMIN,
                support_in_chat=True,
                classes_admin=True,
                content_admin=True,
            )

    @pytest.mark.asyncio
    async def test_get_full_admins_handles_model_validation_exception(self):
        """Test that get_full_admins handles model validation exceptions."""
        # Arrange
        mock_user = MagicMock()
        mock_user.id = uuid.uuid4()
        mock_user.email = "<EMAIL>"

        with (
            patch.object(Authorized, "filter", new_callable=AsyncMock) as mock_filter,
            patch.object(
                RawAuthorized,
                "model_validate",
                side_effect=ValueError("Validation error"),
            ) as mock_validate,
        ):
            mock_filter.return_value = [mock_user]

            # Act & Assert
            with pytest.raises(ValueError, match="Validation error"):
                await AuthorizedRepository.get_full_admins()

            # Verify the query was executed
            mock_filter.assert_called_once()
            mock_validate.assert_called_once_with(mock_user)
