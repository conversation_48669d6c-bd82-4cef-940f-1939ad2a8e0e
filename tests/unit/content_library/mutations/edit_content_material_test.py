from unittest.mock import patch, MagicMock
from uuid import uuid4, UUID

import pytest

from ciba_participant.content_library.crud.edit_content_material import (
    UpdateResponse,
)
from src.schema import schema


@pytest.mark.asyncio
async def test_edit_content_material():
    """
    update_archive_status should return success when an status update is made
    """
    mutation = """
        mutation EditContentMaterial($materialId: UUID!, $materialData: MaterialData!) {
          editContentMaterial(materialId: $materialId, materialData: $materialData) {
            id
            success
            error
            uploadUrl
            fields
          }
        }
    """
    variables = {
        "materialId": "ee304470-7fdd-46a3-8053-ff1ed77be287",
        "materialData": {
            "activityTypes": ["RECIPES"],
            "contentUrl": None,
            "description": "Prepare a delitious mac and cheese in minutes.",
            "fileName": "mac n' cheese.pdf",
            "fileSize": 512,
            "formId": None,
            "mimeType": "application/pdf",
            "programs": ["c873f37f-7c32-4aa2-b527-d99ccaf0ef05"],
            "tags": ["HEALTHY_EATING", "RECIPES"],
            "title": "New mac and cheese",
        },
    }
    mock_context = type(
        "Context",
        (),
        {
            "user": type(
                "User", (), {"id": uuid4(), "is_authenticated": True}
            )()
        },
    )()
    mock_edition = MagicMock(spec=UpdateResponse)
    mock_edition.material_id = UUID("ee304470-7fdd-46a3-8053-ff1ed77be287")
    mock_edition.needs_file_upload = False

    with patch(
        "ciba_participant.content_library.crud.ContentMaterialRepository.edit_material",
        return_value=mock_edition,
    ):
        actual_value = await schema.execute(
            mutation, variable_values=variables, context_value=mock_context
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["editContentMaterial"]["success"] is True
