from unittest.mock import patch
from uuid import uuid4

import pytest

from src.schema import schema


@pytest.mark.asyncio
async def test_update_archive_status():
    """
    update_archive_status should return success when a status update is made
    """
    mutation = """
        mutation Mutation($materialId: UUID!) {
          updateArchiveStatus(materialId: $materialId) {
            id
            success
            error
          }
        }
    """
    variables = {"materialId": str(uuid4())}
    mock_context = type(
        "Context",
        (),
        {
            "user": type(
                "User", (), {"id": uuid4(), "is_authenticated": True}
            )()
        },
    )()

    with patch(
        "ciba_participant.content_library.crud.ContentMaterialRepository.update_archive_status",
        return_value=None,
    ):
        actual_value = await schema.execute(
            mutation, variable_values=variables, context_value=mock_context
        )

        assert actual_value.errors is None
        assert actual_value.data["updateArchiveStatus"] is not None
        assert actual_value.data["updateArchiveStatus"]["error"] is None
        assert actual_value.data["updateArchiveStatus"]["success"] is True
