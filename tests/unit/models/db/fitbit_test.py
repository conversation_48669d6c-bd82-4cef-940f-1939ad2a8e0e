from unittest.mock import patch
from datetime import datetime, timedelta

import pytz

from ciba_iot_etl.models.db.fitbit import Fitbit

DATETIME_MOCK_MODULE = "ciba_iot_etl.utils.datetime_utils.datetime"


class TestFitbitModel:
    """Tests for the Fitbit model datetime handling."""

    def test_is_access_token_expired_with_naive_datetime(self):
        """Test is_access_token_expired with naive datetime."""
        # Create a Fitbit instance with a naive datetime
        fitbit = Fitbit()
        # Set expiration to 1 hour in the past
        fitbit.access_token_expires_at = datetime.now() - timedelta(hours=1)

        # Token should be expired
        assert fitbit.is_access_token_expired() is True

        # Set expiration to 1 hour in the future
        fitbit.access_token_expires_at = datetime.now() + timedelta(hours=1)

        # Token should not be expired
        assert fitbit.is_access_token_expired() is False

    def test_is_access_token_expired_with_aware_datetime(self):
        """Test is_access_token_expired with timezone-aware datetime."""

        fitbit = Fitbit()
        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_MODULE) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)

            fitbit.access_token_expires_at = now - timedelta(hours=1)

            assert fitbit.is_access_token_expired() is True

            fitbit.access_token_expires_at = now + timedelta(hours=1)

            assert fitbit.is_access_token_expired() is False

    def test_is_refresh_token_expired_with_naive_datetime(self):
        """Test is_refresh_token_expired with naive datetime."""
        # Create a Fitbit instance with a naive datetime
        fitbit = Fitbit()
        # Set expiration to 1 hour in the past
        fitbit.refresh_token_expires_at = datetime.now() - timedelta(hours=1)

        # Token should be expired
        assert fitbit.is_refresh_token_expired() is True

        # Set expiration to 1 hour in the future
        fitbit.refresh_token_expires_at = datetime.now() + timedelta(hours=1)

        # Token should not be expired
        assert fitbit.is_refresh_token_expired() is False

    def test_is_refresh_token_expired_with_aware_datetime(self):
        """Test is_refresh_token_expired with timezone-aware datetime."""

        fitbit = Fitbit()
        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_MODULE) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)

            fitbit.refresh_token_expires_at = now - timedelta(hours=1)

            assert fitbit.is_refresh_token_expired() is True

            fitbit.refresh_token_expires_at = now + timedelta(hours=1)

            assert fitbit.is_refresh_token_expired() is False

    def test_is_access_token_expired_fallback(self):
        """Test is_access_token_expired fallback to expires_in."""
        # Create a Fitbit instance with no access_token_expires_at
        fitbit = Fitbit()
        fitbit.access_token_expires_at = None
        fitbit.expires_in = 3600  # 1 hour

        # Set updated_at to 2 hours ago (naive)
        fitbit.updated_at = datetime.now() - timedelta(hours=2)

        # Token should be expired
        assert fitbit.is_access_token_expired() is True

        # Set updated_at to 30 minutes ago (naive)
        fitbit.updated_at = datetime.now() - timedelta(minutes=30)

        # Token should not be expired
        assert fitbit.is_access_token_expired() is False

        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_MODULE) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)
            fitbit.updated_at = datetime.now(pytz.UTC) - timedelta(hours=2)

            assert fitbit.is_access_token_expired() is True

    def test_update_tokens_timezone_handling(self):
        """Test update_tokens handles timezones correctly."""
        # Mock datetime.now to return a fixed time
        with patch(DATETIME_MOCK_MODULE) as mock_datetime:
            now = datetime.now()
            mock_datetime.now.return_value = now

            # Calculate expected timestamps
            expected_access_token_expires_at = now + timedelta(seconds=3600)

            # Call the method directly
            result = Fitbit._calculate_expiration_timestamps(3600)

            # Verify that the timestamps are timezone-naive
            assert result["access_token_expires_at"].tzinfo is None
            assert result["updated_at"].tzinfo is None
            assert expected_access_token_expires_at == result["access_token_expires_at"]
