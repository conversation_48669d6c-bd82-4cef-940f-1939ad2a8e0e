from unittest.mock import patch

import pytest

from ciba_participant.schedule_manager.types import ScheduleManagerError
from ciba_participant.classes.errors import LiveSessionError
from src.schema import schema
from src.webinar.mutations.delete_live_session import UNEXPECTED_ERROR

MUTATION = """
    mutation Mutation($liveSessionId: UUID!) {
      deleteLiveSession(liveSessionId: $liveSessionId) {
        success
        error
      }
    }
"""
VARIABLES = {"liveSessionId": "375ce8ec-3006-425b-a13e-ce06ea2f1386"}


@pytest.mark.asyncio
async def test_delete_session_with_sm_error(mock_client_context):
    """
    delete_live_session should return an unsuccessful response with an error
    when an exception is raised.
    """

    error_message = "sm error"
    with patch(
        "src.webinar.mutations.delete_live_session.LiveSessionRepository.delete_live_session",
        side_effect=ScheduleManagerError(error_message),
    ) as mocked_function:
        actual_value = await schema.execute(
            MUTATION,
            variable_values=VARIABLES,
            context_value=mock_client_context,
        )

        assert actual_value.data["deleteLiveSession"]["success"] is False
        assert actual_value.data["deleteLiveSession"]["error"] == error_message
        mocked_function.assert_called_once()


@pytest.mark.asyncio
async def test_delete_session_with_ls_error(mock_client_context):
    """
    delete_live_session should return an unsuccessful response with an error
    when an exception is raised.
    """

    error_message = "ls error"
    with patch(
        "src.webinar.mutations.delete_live_session.LiveSessionRepository.delete_live_session",
        side_effect=LiveSessionError(error_message),
    ) as mocked_function:
        actual_value = await schema.execute(
            MUTATION,
            variable_values=VARIABLES,
            context_value=mock_client_context,
        )

        assert actual_value.data["deleteLiveSession"]["success"] is False
        assert actual_value.data["deleteLiveSession"]["error"] == error_message
        mocked_function.assert_called_once()


@pytest.mark.asyncio
async def test_delete_session_with_generic_error(mock_client_context):
    """
    delete_live_session should return an unsuccessful response with an error
    when an exception is raised.
    """

    error_message = "ls error"
    with patch(
        "src.webinar.mutations.delete_live_session.LiveSessionRepository.delete_live_session",
        side_effect=Exception(error_message),
    ) as mocked_function:
        actual_value = await schema.execute(
            MUTATION,
            variable_values=VARIABLES,
            context_value=mock_client_context,
        )

        assert actual_value.data["deleteLiveSession"]["success"] is False
        assert (
            actual_value.data["deleteLiveSession"]["error"] == UNEXPECTED_ERROR
        )
        mocked_function.assert_called_once()


@pytest.mark.asyncio
async def test_delete_webinar_success(mock_client_context):
    """
    delete_live_session should return a successful response.
    """
    with patch(
        "src.webinar.mutations.delete_live_session.LiveSessionRepository.delete_live_session",
    ) as mocked_function:
        actual_value = await schema.execute(
            MUTATION,
            variable_values=VARIABLES,
            context_value=mock_client_context,
        )

        assert actual_value.data["deleteLiveSession"]["success"] is True
        mocked_function.assert_called_once()
