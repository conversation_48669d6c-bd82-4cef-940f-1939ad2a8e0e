from unittest.mock import AsyncMock, Mock, patch
from uuid import UUID

import pytest

from ciba_participant.schedule_manager.types import ScheduleManagerError
from src.schema import schema

mutation = """
    mutation Mutation($classId: UUID!, $userId: UUID!) {
      deleteWebinar(classId: $classId, userId: $userId) {
        webinarId
        success
        error
      }
    }
"""
variables = {
    "classId": "6dcaff71-e7d0-4295-8397-739ca56bc768",
    "userId": "803f035e-e9de-4bd9-830e-390f589d0c16",
}


@pytest.mark.asyncio
async def test_delete_webinar_with_errors(mock_client_context):
    """
    delete_class should return an unsuccessful response with an error
    when an exception is raised.
    """
    test_error = "Schedule not found"
    mocked_function = Mock()
    mocked_function.side_effect = ScheduleManagerError(test_error)

    with patch(
        "src.webinar.mutations.WebinarRepository.delete_webinar",
        mocked_function,
    ):
        actual_value = await schema.execute(
            mutation,
            variable_values=variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["deleteWebinar"]["error"] == test_error
        assert actual_value.data["deleteWebinar"]["success"] is False
        assert (
            actual_value.data["deleteWebinar"]["webinarId"]
            == variables["classId"]
        )
        mocked_function.assert_called_once_with(
            webinar_id=UUID(variables["classId"]),
            user_id=UUID(variables["userId"]),
        )


@pytest.mark.asyncio
async def test_delete_webinar_success(mock_client_context):
    """
    delete_class should return a successful response.
    """
    mocked_function = AsyncMock()

    with patch(
        "src.webinar.mutations.WebinarRepository.delete_webinar",
        mocked_function,
    ):
        actual_value = await schema.execute(
            mutation,
            variable_values=variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data["deleteWebinar"]["error"] is None
        assert actual_value.data["deleteWebinar"]["success"] is True
        assert (
            actual_value.data["deleteWebinar"]["webinarId"]
            == variables["classId"]
        )
        mocked_function.assert_called_once_with(
            webinar_id=UUID(variables["classId"]),
            user_id=UUID(variables["userId"]),
        )
