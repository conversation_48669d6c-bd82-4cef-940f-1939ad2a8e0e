from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from uuid import uuid4

import pytest
from tortoise.exceptions import BaseORMException

from src.content_library.messages import DB_READ_ERROR
from src.schema import schema

test_id = uuid4()
test_query = """
    query GetClassesCreators {
      getClassesCreators {
        success
        creators {
          id
          fullName
          isAdmin
        }
        error
      }
    }
"""


@pytest.mark.asyncio
async def test_get_classes_creators_with_errors(mock_client_context):
    """
    get_classes_creators should return an empty list with unsuccessful status
    when a database error occurs.
    """
    with patch(
        "ciba_participant.classes.crud.WebinarRepository.get_creators",
        side_effect=BaseORMException(),
    ):
        actual_value = await schema.execute(
            test_query, context_value=mock_client_context
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getClassesCreators"] == {
            "success": False,
            "error": DB_READ_ERROR,
            "creators": [],
        }


@pytest.mark.asyncio
async def test_get_classes_creators_success(mock_client_context):
    """
    get_classes_creators should return a list of users.
    """
    test_author = MagicMock()
    test_author.id = test_id
    test_author.full_name = <PERSON>ck(return_value="<PERSON> Doe")
    test_author.first_name = "John"
    test_author.last_name = "Doe"
    test_author.email = "<EMAIL>"
    test_author.chat_identity = "randomId"
    test_author.role = "health_coach"

    with patch(
        "ciba_participant.classes.crud.WebinarRepository.get_creators",
        return_value=[test_author],
    ):
        actual_value = await schema.execute(
            test_query, context_value=mock_client_context
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getClassesCreators"] == {
            "success": True,
            "error": None,
            "creators": [
                {"id": str(test_id), "fullName": "John Doe", "isAdmin": False}
            ],
        }
