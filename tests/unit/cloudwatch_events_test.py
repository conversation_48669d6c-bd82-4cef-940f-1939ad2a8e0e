import json
from unittest.mock import patch

import pendulum
import pytest

from email_notifications.app import async_lambda_handler
from tests.unit.conftest import create_cloudwatch_event


@pytest.mark.asyncio
async def test_async_lambda_handler_scales_event(
    lambda_context,
    mock_settings,
    mock_db_functions,
    mock_email_handler,
    mock_presigned_url,
    mock_publish_to_sns,
):
    """Test async_lambda_handler function with Scales event"""
    # Arrange
    event = create_cloudwatch_event("Scales")

    # Act
    with patch("email_notifications.app.pendulum.now") as mock_now:
        mock_now.return_value = pendulum.parse("2023-01-01T12:00:00Z")
        result = await async_lambda_handler(event, lambda_context)

    # Assert
    mock_email_handler.send_new_participant_email.assert_called_once()
    mock_presigned_url.assert_called_once_with(
        bucket_name=mock_settings.AWS_BUCKET_NAME,
        object_name="test/path/to/file.csv",
        expiration=604799,
    )

    # Check SlackNotification structure
    assert result["environment"] == mock_settings.ENV
    assert result["is_test"] == bool(mock_settings.DEBUG)
    assert result["source"] == lambda_context.function_name
    assert result["title"] == "Enrolled participants for last 24 hours"
    assert result["url"] == "https://test-bucket.s3.amazonaws.com/test/path/to/file.csv"
    assert result["type"] == "OnboardedParticipants"
    assert "Date range: 2022-12-31 - 2023-01-01" in result["details"]
    assert result["additional_info"] == "Download participants list by url."


@pytest.mark.asyncio
async def test_async_lambda_handler_new_module_event(
    lambda_context, mock_settings, mock_db_functions, mock_email_handler
):
    """Test async_lambda_handler function with NewModule event"""
    # Arrange
    event = create_cloudwatch_event("NewModule")

    # Act
    result = await async_lambda_handler(event, lambda_context)

    # Assert
    mock_email_handler.send_new_module_starting_email.assert_called_once()
    assert result == {}


@pytest.mark.asyncio
async def test_async_lambda_handler_unsupported_rule(
    lambda_context, mock_settings, mock_db_functions, mock_email_handler
):
    """Test async_lambda_handler function with unsupported rule"""
    # Arrange
    event = create_cloudwatch_event("UnsupportedRule")

    # Act and Assert
    with pytest.raises(ValueError, match="Unsupported rule name: UnsupportedRule"):
        await async_lambda_handler(event, lambda_context)


@pytest.mark.asyncio
async def test_async_lambda_handler_scales_event_with_sns_publishing(
    lambda_context,
    mock_settings,
    mock_db_functions,
    mock_email_handler,
    mock_presigned_url,
    mock_publish_to_sns,
):
    """Test async_lambda_handler function with Scales event and SNS publishing"""
    # Arrange
    event = create_cloudwatch_event("Scales")

    # Act
    result = await async_lambda_handler(event, lambda_context)

    # Assert
    # Check that publish_to_sns was called
    mock_publish_to_sns.assert_called_once()

    # Check the arguments
    call_args = mock_publish_to_sns.call_args[1]
    assert call_args["sns_topic_arn"] == mock_settings.SLACK_SNS_TOPIC_ARN
    assert json.loads(call_args["message"]) == result


@pytest.mark.asyncio
async def test_async_lambda_handler_scales_event_exception_handling(
    lambda_context,
    mock_settings,
    mock_db_functions,
    mock_email_handler,
    mock_presigned_url,
    mock_publish_to_sns,
):
    """Test async_lambda_handler function with Scales event and exception handling"""
    # Arrange
    event = create_cloudwatch_event("Scales")
    mock_email_handler.send_new_participant_email.side_effect = Exception(
        "Test exception"
    )

    # Act and Assert
    with pytest.raises(Exception, match="Test exception"):
        await async_lambda_handler(event, lambda_context)

    # Verify SNS is called with error details
    mock_publish_to_sns.assert_called_once()

    # Check the error message in the SNS message
    call_args = mock_publish_to_sns.call_args[1]
    message_dict = json.loads(call_args["message"])
    assert message_dict["type"] == "Error"
    assert "An error occurred: Test exception" in message_dict["details"]
