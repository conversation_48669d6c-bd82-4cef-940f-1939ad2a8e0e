# Use the official AWS Lambda Python runtime as the base image
FROM public.ecr.aws/lambda/python:3.12

RUN dnf install -y openssh-clients git

COPY * ./

RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts

RUN --mount=type=ssh pip install -r requirements.txt

CMD ["app.lambda_handler"]
#ENV DD_LAMBDA_HANDLER="app.lambda_handler"
#ENV DD_TRACE_ENABLED=true
#
#CMD ["datadog_lambda.handler.handler"]
