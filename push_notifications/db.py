import asyncpg
from aws_lambda_powertools import Logger
from ciba_participant import get_settings

from boto3_connections import get_parameter

logger = Logger()
settings = get_settings()


async def query_db(query: str) -> list:
    """
    Executes an asynchronous query and fetches all results.
    Handles connection management automatically.

    Args:
        query (str): SQL query to execute.

    Returns:
        list: A list of dictionaries representing the query result rows.
    """
    # Adjust prefix for new env
    secrets_prefix = "" if settings.IS_NEW_ENV else f"/{settings.ENV}"
    prefix = f"{secrets_prefix}/chat"

    db_config = {
        "database": get_parameter(f"{prefix}/POSTGRES_DB"),
        "user": get_parameter(f"{prefix}/POSTGRES_USER"),
        "password": get_parameter(f"{prefix}/POSTGRES_PASSWORD"),
        "host": get_parameter(f"{prefix}/POSTGRES_HOST"),
        "port": 5432,
    }
    # Connect to the database
    conn = await asyncpg.connect(**db_config)
    try:
        # Execute query and fetch all rows
        rows = await conn.fetch(query)
        return [dict(row) for row in rows]
    finally:
        # Ensure the connection is closed
        await conn.close()
