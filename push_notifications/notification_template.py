import copy


class NotificationTemplate:
    """
    This class is used to create notification messages for different types of events.
    """

    def __init__(self):
        self.template = {
            "version": 1,
            "type": "",
            "body": {"email": "", "title": "", "message": ""},
            "source": "ciba_usa_api",
        }

    def notification_message(self, participant: dict, type: str) -> dict | None:
        """
        Create a notification message based on the type and participant information.

        Args:
            participant (dict): A dictionary containing participant information.
            type (str): The type of notification to create.

        Returns:
            dict | None: A dictionary containing the notification message,
             or None if the type is not recognized.
        """
        message_template = copy.deepcopy(self.template)

        email = participant["email"]
        full_name = f"{participant['first_name'].capitalize()} {participant['last_name'].capitalize()}"

        is_weight = participant.get("is_weight", False)

        match type:
            case "new_material_available":
                message_template["type"] = type
                message_template["body"] = {
                    "email": email,
                    "title": "You have a new module available.",
                    "message": f"Hi, {full_name}, you have new materials available for your program!",
                }
            case "weight_needed":
                message_template["type"] = type
                if is_weight:
                    message_template["body"] = {
                        "email": email,
                        "title": "Time to weigh yourself!",
                        "message": f"Hi, {full_name}, please record your weight for the week by stepping on the scale.",
                    }
                else:
                    message_template["body"] = {
                        "email": email,
                        "title": "Time to weigh yourself!",
                        "message": f"Hi, {full_name}, please enter your weight for the week while you are waiting for your scales to arrive.",
                    }
            case "physical_activity_needed":
                message_template["type"] = type
                message_template["body"] = {
                    "email": email,
                    "title": "Time to move",
                    "message": f"Hi, {full_name}, let's see how much time you dedicated to your physical health this week!",
                }
            case "no_weight_input":
                message_template["type"] = type
                message_template["body"] = {
                    "email": email,
                    "title": "Reminder: weigh yourself weekly",
                    "message": f"Hi, {full_name}, it appears you haven’t weighed yourself this week, please take time to step on the scale.",
                }
            case "new_chat_messages":
                message_template["type"] = type
                message_template["body"] = {
                    "email": email,
                    "title": "Group Chat Update!",
                    "message": "Your group just got a new message from a health coach. Join in and stay connected!",
                }
            case "before_live_session_24_hours":
                message_template["type"] = type
                message_template["body"] = {
                    "email": email,
                    "title": "Your Class is 24 Hours Away!",
                    "message": "Your next class starts tomorrow. Add it to your calendar and gear up to unlock better health!",
                }
            case "before_live_session_1_hour":
                message_template["type"] = type
                message_template["body"] = {
                    "email": email,
                    "title": "Your Class Begins in 1 Hour",
                    "message": 'Get ready—your class is about to start. Log in to your Ciba Health platform and click "Join." See you soon!',
                }
            case _:
                return None

        return message_template
