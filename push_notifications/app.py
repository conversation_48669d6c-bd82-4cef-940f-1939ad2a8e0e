import asyncio
import base64
import json
from typing import List, Dict, Any
from urllib.parse import parse_qs

import boto3
from aws_lambda_powertools import Logger
from ciba_participant.common.aws_handler import SlackNotification, publish_to_sns
from ciba_participant.common.db import close_db, init_db
from ciba_participant.notifications.push.queries import (
    get_chat_query,
    get_participants,
    get_participants_no_weight_activity,
    get_start_module_participants,
    get_participants_with_class_tomorrow,
    get_participants_with_class_in_next_hour,
    ParticipantPushOutput,
)
from ciba_participant.settings import get_settings

from boto3_connections import get_parameter, get_sqs_client
from db import query_db
from notification_template import NotificationTemplate

logger = Logger()

# remove duplicate logs from being sent to lambda
logger._logger.propagate = False

settings = get_settings()

sns_client = boto3.client("sns", region_name=settings.AWS_REGION)

DB_INITIALIZED = False

BATCH_SIZE = 10  # Maximum number of messages per batch
MAX_RETRIES = 3


async def send_batch_messages(
    sqs_client, queue_url: str, messages: List[Dict[str, Any]], retry_count: int = 0
) -> tuple[int, List[Dict[str, Any]]]:
    """
    Send messages in batches to SQS.

    Args:
        sqs_client: The SQS client
        queue_url: The URL of the SQS queue
        messages: List of message dictionaries to send
        retry_count: Current retry count (for internal use in recursion)

    Returns:
        tuple: (Number of successfully sent messages, List of failed messages)
    """
    if not messages:
        return 0, []

    try:
        emails = [
            message.get("body", {}).get("email")
            for message in messages
            if message.get("body", {}).get("email")
        ]
        logger.info(f"Sending batch messages to these emails: {emails}")

        entries = [
            {"Id": str(idx), "MessageBody": json.dumps(message)}
            for idx, message in enumerate(messages[:BATCH_SIZE])
        ]

        response = sqs_client.send_message_batch(QueueUrl=queue_url, Entries=entries)

        successful = len(response.get("Successful", []))

        failed_indices = [int(failed["Id"]) for failed in response.get("Failed", [])]
        failed_messages = [messages[idx] for idx in failed_indices]

        remaining_count = 0
        remaining_failed = []
        if len(messages) > BATCH_SIZE:
            remaining_count, remaining_failed = await send_batch_messages(
                sqs_client, queue_url, messages[BATCH_SIZE:], retry_count
            )

        # Combine failed messages from current batch and remaining messages
        all_failed = failed_messages + remaining_failed

        # Retry failed messages if we haven't exceeded max retries
        if all_failed and retry_count < MAX_RETRIES:
            logger.info(
                f"Retrying {len(all_failed)} failed messages. Attempt {retry_count + 1}"
            )
            retry_success, retry_failed = await send_batch_messages(
                sqs_client, queue_url, all_failed, retry_count + 1
            )
            return successful + remaining_count + retry_success, retry_failed

        return successful + remaining_count, all_failed

    except Exception as e:
        logger.error(f"Error in batch send: {e}")
        if retry_count < MAX_RETRIES:
            logger.info(f"Retrying batch send. Attempt {retry_count + 1}")
            await asyncio.sleep(0.5 * (retry_count + 1))  # Exponential backoff
            return await send_batch_messages(
                sqs_client, queue_url, messages, retry_count + 1
            )

        raise e


async def send_daily_push(
    notification_template: NotificationTemplate,
    sqs_region: str,
    sqs_queue: str,
):
    """
    Sends daily push notifications to participants based on specific criteria.

    This function retrieves participants who need notifications about
    weight input or new module availability and sends corresponding
    push notifications using AWS SQS.

    Args:
        notification_template (NotificationTemplate): An object that generates
            notification messages based on participant data and notification type.
        sqs_region (str): The AWS region of the SQS queue.
        sqs_queue (str): The URL of the SQS queue to send notifications to.
    """
    start_module_users = await get_start_module_participants()
    no_weight_input_users = await get_participants_no_weight_activity()
    sqs_client = get_sqs_client(sqs_region)

    total_sent = 0
    total_failed = 0

    # Process weight input notifications
    if no_weight_input_users:
        weight_messages = []
        for user in no_weight_input_users:
            user = user.model_dump()
            notification_type = "no_weight_input"
            notification_message = notification_template.notification_message(
                participant=user,
                type=notification_type,
            )
            weight_messages.append(notification_message)

        sent, failed = await send_batch_messages(sqs_client, sqs_queue, weight_messages)

        total_sent += sent
        total_failed += len(failed)
        logger.info(f"Weight input notifications: {sent} sent, {len(failed)} failed")

    # Process module notifications
    if start_module_users:
        module_messages = []
        for user in start_module_users:
            user = user.model_dump()
            notification_types = [
                "new_material_available",
                "weight_needed",
                "physical_activity_needed",
            ]

            for notification_type in notification_types:
                notification_message = notification_template.notification_message(
                    participant=user,
                    type=notification_type,
                )
                if notification_message:
                    module_messages.append(notification_message)

        sent, failed = await send_batch_messages(sqs_client, sqs_queue, module_messages)

        total_sent += sent
        total_failed += len(failed)
        logger.info(f"Module notifications: {sent} sent, {len(failed)} failed")
    else:
        logger.info("No users for scheduled notifications")

    logger.info(f"Total daily notifications sent: {total_sent}, failed: {total_failed}")


async def send_chat_push(
    event,
    notification_template: NotificationTemplate,
    sqs_region: str,
    sqs_queue: str,
):
    """
    Sends push notifications for new chat messages.

    Args:
        event (dict): The event object received by the AWS Lambda function.
        notification_template (NotificationTemplate): An object that generates
            notification messages based on participant data and notification type.
        sqs_region (str): The AWS region of the SQS queue.
        sqs_queue (str): The URL of the SQS queue to send notifications to.
    """
    if event["isBase64Encoded"]:
        decoded_string = base64.b64decode(event["body"])
        decoded_string = decoded_string.decode("utf-8")  # Convert bytes to string
        params = parse_qs(decoded_string)
    else:
        params = parse_qs(event["body"])

    client_identity = params.get("From", [None])[0] or ""
    chat_sid = params.get("ChannelSid", [None])[0] or ""
    logger.info(
        f"Message written from: Chat identity: {client_identity}, Chat SID: {chat_sid}"
    )

    chat_query = get_chat_query(client_identity, chat_sid)
    external_ids = await query_db(chat_query)

    if external_ids:
        external_ids_list = [i["external_id"] for i in external_ids]
        logger.info(f"External IDs found: {len(external_ids_list)}")

        participants = await get_participants(external_ids_list)

        if participants:
            logger.info(f"Participants found: {len(participants)}")

            sqs_client = get_sqs_client(sqs_region)
            chat_messages = []

            for participant in participants:
                participant = participant.model_dump()
                notification_type = "new_chat_messages"
                notification_message = notification_template.notification_message(
                    participant=participant, type=notification_type
                )
                if notification_message:
                    chat_messages.append(notification_message)

            sent, failed = await send_batch_messages(
                sqs_client, sqs_queue, chat_messages
            )

            logger.info(f"Chat notifications: {sent} sent, {len(failed)} failed")
        else:
            logger.info("No participants in the participant DB")
    else:
        logger.info("Message not from a provider or admin")


async def send_class_reminder(
    notification_template: NotificationTemplate,
    sqs_region: str,
    sqs_queue: str,
    participants: list[ParticipantPushOutput],
    reminder_type: str,
) -> tuple[int, int]:
    """
    Generic function to send class reminders to participants.

    Args:
        notification_template: Template generator for notifications
        sqs_region: AWS region for SQS
        sqs_queue: SQS queue URL
        participants: List of participants to send reminders to
        reminder_type: Type of reminder being sent

    Returns:
        tuple: (Total sent, Total failed)
    """
    if not participants:
        logger.info(f"No users for {reminder_type} notifications")
        return 0, 0

    sqs_client = get_sqs_client(sqs_region)
    participants_data = [participant.model_dump() for participant in participants]

    reminder_messages = []
    for participant in participants_data:
        notification_message = notification_template.notification_message(
            participant=participant,
            type=reminder_type,
        )
        if notification_message:
            reminder_messages.append(notification_message)

    sent, failed = await send_batch_messages(sqs_client, sqs_queue, reminder_messages)

    logger.info(
        f"Total {reminder_type} notifications: {sent} sent, {len(failed)} failed"
    )
    return sent, len(failed)


async def send_daily_class_reminder(
    notification_template: NotificationTemplate,
    sqs_region: str,
    sqs_queue: str,
) -> tuple[int, int]:
    """
    Sends daily class reminders to participants.

    Returns:
        tuple: (Total sent, Total failed)
    """
    participants = await get_participants_with_class_tomorrow(
        timezone="America/Los_Angeles"
    )

    return await send_class_reminder(
        notification_template=notification_template,
        sqs_region=sqs_region,
        sqs_queue=sqs_queue,
        participants=participants,
        reminder_type="before_live_session_24_hours",
    )


async def send_hourly_class_reminder(
    notification_template: NotificationTemplate,
    sqs_region: str,
    sqs_queue: str,
) -> tuple[int, int]:
    """
    Sends hourly class reminders to participants.

    Returns:
        tuple: (Total sent, Total failed)
    """
    participants = await get_participants_with_class_in_next_hour()

    return await send_class_reminder(
        notification_template=notification_template,
        sqs_region=sqs_region,
        sqs_queue=sqs_queue,
        participants=participants,
        reminder_type="before_live_session_1_hour",
    )


async def ensure_db_initialized():
    """
    Ensure the database is initialized
    to correctly handle aws db connection
    """
    global DB_INITIALIZED  # pylint: disable=global-statement
    if not DB_INITIALIZED:
        await init_db()
        DB_INITIALIZED = True


async def async_lambda_handler(event, context):
    """
    Asynchronous Lambda handler for processing push notifications.
    """
    try:
        await ensure_db_initialized()

        notification_template = NotificationTemplate()

        # Adjust prefix for new env
        secrets_prefix = "" if settings.IS_NEW_ENV else f"/{settings.ENV}"
        prefix = f"{secrets_prefix}/push-notifications"

        sqs_region = get_parameter(f"{prefix}/SQS_REGION")
        sqs_queue = get_parameter(f"{prefix}/SQS_URL")

        response = {"statusCode": 200}

        if event.get("source") == "aws.events":
            rule_arn = event.get("resources", [None])[0]
            logger.info(f"Lambda triggered by scheduled event: {rule_arn}")

            if rule_arn:
                if "DailyParticipantPushNotifications" in rule_arn:
                    await send_daily_push(
                        notification_template=notification_template,
                        sqs_region=sqs_region,
                        sqs_queue=sqs_queue,
                    )

                elif "DailyClassReminder" in rule_arn:
                    sent, failed = await send_daily_class_reminder(
                        notification_template=notification_template,
                        sqs_region=sqs_region,
                        sqs_queue=sqs_queue,
                    )
                    response["body"] = {"sent": sent, "failed": failed}

                elif "HourlyClassReminder" in rule_arn:
                    sent, failed = await send_hourly_class_reminder(
                        notification_template=notification_template,
                        sqs_region=sqs_region,
                        sqs_queue=sqs_queue,
                    )
                    response["body"] = {"sent": sent, "failed": failed}

        # Check for trigger source
        elif "requestContext" in event and "apiId" in event["requestContext"]:
            logger.info(
                f"Lambda triggered by API Gateway with ID {event['requestContext']['apiId']}"
            )

            await send_chat_push(
                event=event,
                notification_template=notification_template,
                sqs_region=sqs_region,
                sqs_queue=sqs_queue,
            )

        else:
            logger.info(
                f"Lambda triggered by unknown source\nEvent: {event}\nContext: {context}"
            )

        return response

    except Exception as e:
        slc = SlackNotification(
            environment=settings.ENV,
            is_test=bool(settings.IS_NEW_ENV),
            source=context.function_name,
        )

        slc.title = "Error processing push notification"
        slc.details = f"Error processing push notification: {e}"

        logger.exception(f"Error processing push notification: {e}")

        response = {
            "statusCode": 500,
            "type": "Error",
            "body": {"error": f"Error processing data: {e}"},
        }

        publish_to_sns(
            sns_client=sns_client,
            sns_topic_arn=settings.SLACK_SNS_TOPIC_ARN,
            message=slc.model_dump_json(),
        )

        return response

    finally:
        try:
            await close_db()
            global DB_INITIALIZED  # pylint: disable=global-statement
            DB_INITIALIZED = False
        except Exception as db_error:
            logger.error(f"Error closing database connection: {db_error}")


@logger.inject_lambda_context
def lambda_handler(event, context):
    """
    Synchronous Lambda handler.
    """
    try:
        result = asyncio.run(async_lambda_handler(event, context))
        return json.dumps(result)
    except Exception as e:
        logger.exception(f"Unhandled exception in lambda_handler: {e}")
        error_response = {
            "statusCode": 500,
            "type": "Error",
            "body": {"error": f"Unhandled exception: {str(e)}"},
        }
        return json.dumps(error_response)
