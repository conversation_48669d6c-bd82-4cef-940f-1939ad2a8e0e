version: '3'
services:
  app:
    image: ${IMAGE_PATH}
    container_name: rpm_registration_api
    restart: unless-stopped
    tty: true
    environment:
      POSTGRES_HOST: db
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      ENV: local
    ports:
      - "8001:8000"
    depends_on:
      - db
    volumes:
      - .:/app/
  db:
    image: postgres:14
    container_name: rpm_registration_api_db
    restart: unless-stopped
    tty: true
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - rpm_registration_db:/var/lib/postgresql

volumes:
  rpm_registration_db:
    driver: local
