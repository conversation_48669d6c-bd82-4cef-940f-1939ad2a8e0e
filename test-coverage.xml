<?xml version="1.0" ?>
<coverage version="7.8.0" timestamp="1747330659717" lines-valid="108" lines-covered="105" line-rate="0.9722" branches-valid="24" branches-covered="22" branch-rate="0.9167" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.8.0 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>email_notifications</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9722" branch-rate="0.9167" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="app.py" filename="app.py" complexity="0" line-rate="0.9722" branch-rate="0.9167">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="45" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="51" hits="1"/>
						<line number="58" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="107"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="104"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="107" hits="0"/>
						<line number="110" hits="1"/>
						<line number="114" hits="1"/>
						<line number="119" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="135" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="164" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="173" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="174" hits="1"/>
						<line number="179" hits="1"/>
						<line number="181" hits="1"/>
						<line number="184" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
