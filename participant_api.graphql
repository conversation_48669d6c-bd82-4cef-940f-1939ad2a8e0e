type ActivityType {
  type: String!
}

type ActivityTypeProgressMetadataType {
  startDate: String!
  endDate: String
  value: String
}

type AddActivityResultType {
  success: Boolean!
}

type AlreadyEnrolled {
  message: String!
}

input BookingFilter {
  search: String = null
  topic: TopicEnum = null
  healthCoach: UUID = null
  timeOfDay: TimeOfDayEnum = null
  startDate: DateTime = null
  endDate: DateTime = null
  withRecording: Boolean = null
  bookingStatus: [BookingStatusEnum!] = []
}

enum BookingStatusEnum {
  BOOKED
  ATTENDED
  WATCHED_RECORDING
  CANCELED
}

type ClassElement {
  id: UUID!
  topic: TopicEnum!
  title: String!
  description: String!
  startedAt: DateTime!
  status: ClassStatus!
  presenter: ProviderType
  joinLink: String
  recordingLink: String
}

type ClassElementConnection {
  pageInfo: PageInfo!
  items: [ClassElement!]!
  success: Boolean!
  error: String
}

input ClassFilter {
  search: String = null
  topic: TopicEnum = null
  healthCoach: UUID = null
  timeOfDay: TimeOfDayEnum = null
  startDate: DateTime = null
  endDate: DateTime = null
}

enum ClassStatus {
  AVAILABLE_TO_BOOK
  FULLY_BOOKED
  BOOKED
  AVAILABLE_TO_JOIN
  PAST
}

type CoachingCallProgressMetadataType {
  startDate: String!
  endDate: String
}

type CoachingCallType {
  url: String!
  startedAt: String
  recordingUrl: String
  type: String!
}

type CohortsData {
  cohorts: [ProgramGroupType!]!
}

union CohortsDataProgramGroupCourseParticipantDneErrorParticipantDuplicatedErrorNoCompanyDataRegistrationFailedAlreadyEnrolled = CohortsData | ProgramGroupCourse | ParticipantDneError | ParticipantDuplicatedError | NoCompanyData | RegistrationFailed | AlreadyEnrolled

type CompanyType {
  id: UUID!
  name: String!
}

type ContentMaterialElement {
  id: UUID!
  addedAt: DateTime!
  mimeType: String!
  title: String!
  description: String!
  status: ContentMaterialStatus!
  tags: [MaterialTag!]!
  activityTypes: [ParticipantActivityEnum!]!
  link: String
  linkExpiration: DateTime
  isFavorite: Boolean
}

type ContentMaterialElementConnection {
  pageInfo: PageInfo!
  items: [ContentMaterialElement!]!
  success: Boolean!
  error: String
}

enum ContentMaterialStatus {
  ACTIVE
  DELETED
  ARCHIVED
}

type CurriculumProgressMetadataType {
  startDate: String!
  endDate: String
}

type CurriculumType {
  url: String!
  type: String!
  isIntro: Boolean!
  signedUrl: String!
}

"""Date with time (isoformat)"""
scalar DateTime

type DietitianCallProgressMetadataType {
  startDate: String!
  endDate: String
}

union DietitianCallProgressMetadataTypeCoachingCallProgressMetadataTypeCurriculumProgressMetadataTypePersonalSuccessProgressMetadataTypeWeightTypeProgressMetadataTypeFoodTypeProgressMetadataTypeVideoTypeProgressMetadataTypeQuizTypeProgressMetadataTypeRecipeTypeProgressMetadataTypeActivityTypeProgressMetadataTypeNotMetadataType = DietitianCallProgressMetadataType | CoachingCallProgressMetadataType | CurriculumProgressMetadataType | PersonalSuccessProgressMetadataType | WeightTypeProgressMetadataType | FoodTypeProgressMetadataType | VideoTypeProgressMetadataType | QuizTypeProgressMetadataType | RecipeTypeProgressMetadataType | ActivityTypeProgressMetadataType | NotMetadataType

type DietitianCallType {
  url: String!
  startedAt: String
  recordingUrl: String
  type: String!
}

type DoesNotBelongToCourse {
  message: String!
}

input FavoriteFilters {
  tags: [MaterialTag!] = null
  search: String = null
}

type FoodType {
  type: String!
}

type FoodTypeProgressMetadataType {
  startDate: String!
  endDate: String
  value: String
}

type InvalidVerifyChangePasswordToken {
  message: String!
}

input MaterialFilters {
  tags: [MaterialTag!] = null
  search: String = null
  startDate: DateTime = null
  endDate: DateTime = null
}

enum MaterialTag {
  ACTIVITY
  GUT_HEALTH
  HEALTHY_EATING
  MINDSETS
  MOTIVATION
  RECIPES
  SLEEP
  STRESS
  SUPPORT
}

type MilestoneProgressDataType {
  completed: Boolean!
  activityDate: DateTime
  title: String
}

enum ModuleDataTypes {
  coaching_call
  dietitian_call
  personal_success
  curriculum
  weight_type
  food_type
  video_type
  quiz_type
  recipe_type
  activity_type
}

type ModuleProgressType {
  id: String!
  completed: Boolean!
  programWeightTrend: TrendEnum
  previousModuleWeightTrend: TrendEnum
  sections: [ProgressDataType!]
  classActivities: [MilestoneProgressDataType!]
  chatActivities: [MilestoneProgressDataType!]
}

union ModuleProgressTypeDoesNotBelongToCourse = ModuleProgressType | DoesNotBelongToCourse

type Mutation {
  signUpParticipant(data: SignUpInput!): ParticipantTypeParticipantDuplicatedError!
  updateStatus(participantId: UUID!, status: ParticipantStatus!): ParticipantTypeParticipantDneError!
  resendConfirmationLink(email: String!): Boolean!
  resendConfirmationLinkByToken(token: String!): Boolean!
  verifyChangePassword(token: String!): VerifyChangePasswordTokenTypeInvalidVerifyChangePasswordTokenVerifyChangePasswordTokenExpired!
  setNewPassword(cohortId: String!, newPassword: String!, startingWeight: Float!, targetWeight: Float!, sessionId: UUID!): ParticipantEmail!
  addWeightData(value: Float!, activityDevice: String!): AddActivityResultType!
  addChatActivity(participantId: String!): Boolean!
  makeProgress(sectionId: UUID!, programModuleId: UUID = null, data: PersonalSuccessInput = null): ModuleProgressTypeDoesNotBelongToCourse
  landParticipant(lookUpKey: String!): CohortsDataProgramGroupCourseParticipantDneErrorParticipantDuplicatedErrorNoCompanyDataRegistrationFailedAlreadyEnrolled
  updateBookingStatus(liveSessionId: UUID!, status: BookingStatusEnum!): UpdateBookingResponseType!
  createContentInteraction(contentMaterialId: UUID!, download: Boolean! = false): SimpleResponseType!
  toggleFavoriteStatus(contentMaterialId: UUID!): SimpleResponseType!
}

type NoCompanyData {
  message: String!
}

type NotMetadataType {
  value: Boolean!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  currentPage: Int!
  perPage: Int!
  lastPage: Int!
  total: Int!
}

enum ParticipantActivityEnum {
  ENROLL
  WEIGHT
  PLAY
  COACH
  RECIPES
  QUIZ
  ACTIVITY
  ARTICLE
  GROUP
}

type ParticipantActivityType {
  id: UUID!
  participantId: UUID!
  value: String!
  activityType: String!
  unit: String!
  activityDevice: String!
  createdAt: String!
}

type ParticipantDneError {
  message: String!
}

type ParticipantDuplicatedError {
  message: String!
}

type ParticipantEmail {
  email: String!
}

enum ParticipantStatus {
  ACTIVE
  COMPLETED
  PENDING
  REJECTED
  DELETED
}

type ParticipantType {
  id: UUID!
  email: String!
  firstName: String!
  lastName: String!
  chatIdentity: String!
  groupId: UUID!
  memberId: UUID!
  status: ParticipantStatus!
  cohortEndDate: DateTime
  cognitoSub: UUID
  medicalRecord: String!
  headsUpToken: UUID
  soleraId: UUID
  soleraProgramId: String
  isWeight: Boolean!
  programGroupId: UUID
  disenrollmentReason: String
  disenrollmentDate: DateTime
}

type ParticipantTypeConnection {
  pageInfo: PageInfo!
  items: [ParticipantType!]!
  success: Boolean!
  error: String
}

union ParticipantTypeParticipantDneError = ParticipantType | ParticipantDneError

union ParticipantTypeParticipantDuplicatedError = ParticipantType | ParticipantDuplicatedError

type ParticipantWeightsType {
  id: UUID!
  participantId: UUID!
  value: Float!
  activityType: String!
  unit: String!
  activityDevice: String!
  createdAt: String!
}

input ParticipantsFilterType {
  ids: [UUID!] = null
  emails: [String!] = null
  groupId: UUID = null
}

input PersonalSuccessInput {
  responseId: String = null
  value: String = null
}

type PersonalSuccessProgressMetadataType {
  startDate: String!
  endDate: String
  responseId: String
}

type PersonalSuccessType {
  url: String
  formId: String!
  type: String!
}

union PersonalSuccessTypeCurriculumTypeCoachingCallTypeDietitianCallTypeWeightTypeFoodTypeVideoTypeActivityType = PersonalSuccessType | CurriculumType | CoachingCallType | DietitianCallType | WeightType | FoodType | VideoType | ActivityType

union PersonalSuccessTypeCurriculumTypeCoachingCallTypeDietitianCallTypeWeightTypeFoodTypeVideoTypeQuizTypeRecipeTypeActivityType = PersonalSuccessType | CurriculumType | CoachingCallType | DietitianCallType | WeightType | FoodType | VideoType | QuizType | RecipeType | ActivityType

type ProgramCourseType {
  program: ProgramType!
  createdAt: DateTime
  updatedAt: DateTime
  id: UUID!
  title: String!
  description: String!
}

type ProgramGroupCourse {
  message: String!
}

type ProgramGroupType {
  id: String!
  name: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  startedAt: DateTime!
  limit: Int!
  programCourseId: UUID @deprecated(reason: "Program course is out of logic")
  participants: [ParticipantType!]!
}

type ProgramModuleSectionType {
  id: String!
  title: String!
  type: ModuleDataTypes!
  description: String
  metadata: PersonalSuccessTypeCurriculumTypeCoachingCallTypeDietitianCallTypeWeightTypeFoodTypeVideoTypeActivityType!
  data: PersonalSuccessTypeCurriculumTypeCoachingCallTypeDietitianCallTypeWeightTypeFoodTypeVideoTypeQuizTypeRecipeTypeActivityType!
}

type ProgramModuleType {
  id: String!
  shortTitle: String!
  title: String!
  startedAt: DateTime!
  endedAt: DateTime!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
  sections: [ProgramModuleSectionType!]!
  programGroup: ProgramGroupType!
  current: Boolean!
}

type ProgramType {
  createdAt: DateTime
  updatedAt: DateTime
  id: UUID!
  title: String!
  description: String!
}

type ProgressDataType {
  sectionId: String!
  sectionType: String!
  metadata: DietitianCallProgressMetadataTypeCoachingCallProgressMetadataTypeCurriculumProgressMetadataTypePersonalSuccessProgressMetadataTypeWeightTypeProgressMetadataTypeFoodTypeProgressMetadataTypeVideoTypeProgressMetadataTypeQuizTypeProgressMetadataTypeRecipeTypeProgressMetadataTypeActivityTypeProgressMetadataTypeNotMetadataType!
  values: [WeightTypeProgressMetadataTypeActivityTypeProgressMetadataType!]
  completed: Boolean!
}

type ProviderType {
  id: UUID!
  fullName: String!
  firstName: String!
  lastName: String!
  email: String!
  chatIdentity: String!
  description: String
  avatarUrl: String
  isAdmin: Boolean
  oldChatIdentity: String
}

type Query {
  getProviders(ids: [UUID!] = null): [ProviderType!]!
  getClassesCreators: [ProviderType!]!
  getParticipant(participantId: UUID!): ParticipantType
  getParticipants(page: Int! = 1, perPage: Int! = 10, filters: ParticipantsFilterType = null): ParticipantTypeConnection!
  me: ParticipantType
  getParticipantActivity: [ParticipantActivityType!]
  getParticipantWeights(startDate: String!, endDate: String!): [ParticipantWeightsType!]
  getParticipantZendeskJwtToken: String!
  getParticipantProgramModule(participantId: UUID!, programModuleId: UUID!): ProgramModuleType!
  getParticipantProgramModules(participantId: UUID!): [ProgramModuleType!]!
  getParticipantProgramCourses(participantId: UUID!): [ProgramCourseType!]!
  getModuleProgress(programModuleId: UUID!, participantId: UUID!): ModuleProgressType
  getCompanies: [CompanyType!]!
  getUpcomingClasses(participantId: UUID!, page: Int! = 1, perPage: Int! = 10, filters: ClassFilter = null): ClassElementConnection!
  getBookingsByParticipant(participantId: UUID!, page: Int! = 1, perPage: Int! = 10, filters: BookingFilter = null): ClassElementConnection!
  getIntroSessionsByCohort(cohortId: UUID!): [ClassElement!]!
  getContentMaterial(page: Int! = 1, perPage: Int! = 10, filters: MaterialFilters = null): ContentMaterialElementConnection!
  getFavoriteMaterials(page: Int! = 1, perPage: Int! = 10, filters: FavoriteFilters = null): ContentMaterialElementConnection!
}

type QuizType {
  formId: String!
  type: String!
}

type QuizTypeProgressMetadataType {
  startDate: String!
  endDate: String
  responseId: String
}

type RecipeType {
  url: String!
  type: String!
  signedUrl: String!
}

type RecipeTypeProgressMetadataType {
  startDate: String!
  endDate: String
}

type RegistrationFailed {
  message: String!
}

input SignUpInput {
  email: String!
  firstName: String!
  lastName: String!
  companyId: UUID!
  groupId: String!
  memberId: String!
}

type SimpleResponseType {
  success: Boolean!
  error: String
}

enum TimeOfDayEnum {
  MORNING
  AFTERNOON
  EVENING
}

enum TopicEnum {
  FOOD
  EDUCATIONAL
  ACTIVITY
  HEALTH_AND_WELLNESS
  MENTAL_HEALTH
  INTRO_SESSION
}

enum TrendEnum {
  UP
  DOWN
  FLAT
}

scalar UUID

type UpdateBookingResponseType {
  bookingId: UUID
  success: Boolean!
  error: String
}

type VerifyChangePasswordTokenExpired {
  message: String!
}

type VerifyChangePasswordTokenType {
  email: String!
  tmpPassword: String!
}

union VerifyChangePasswordTokenTypeInvalidVerifyChangePasswordTokenVerifyChangePasswordTokenExpired = VerifyChangePasswordTokenType | InvalidVerifyChangePasswordToken | VerifyChangePasswordTokenExpired

type VideoType {
  url: String!
  type: String!
  isIntro: Boolean!
  signedUrl: String!
}

type VideoTypeProgressMetadataType {
  startDate: String!
  endDate: String
  value: String
}

type WeightType {
  type: String!
}

type WeightTypeProgressMetadataType {
  startDate: String!
  endDate: String
  value: String
}

union WeightTypeProgressMetadataTypeActivityTypeProgressMetadataType = WeightTypeProgressMetadataType | ActivityTypeProgressMetadataType
