import pendulum

from app.pydantic_model.measures_api import Device


def map_withings_device(device: dict) -> Device:
    """
    Function to map a Withings device element into a Device model.

    Args:
        device (dict): Withings response device element
    Returns:
        Device: Mapped device object
    """
    return Device(
        id=device["deviceid"],
        device_type=device["type"],
        last_synced_at=pendulum.from_timestamp(device["last_session_date"]),
    )
