from typing import List

import pendulum
from ciba_iot_etl.extract.withings_api.common import MeasureGetMeasGroup
from ciba_iot_etl.helpers.unit_transformation import parse_kg_to_lb

from app.pydantic_model.measures_api import Measure

WEIGHT_DECIMAL_POSITIONS = 1
WEIGHT_UNIT = "lbs"


def map_withings_weight_group(
    measure_group: MeasureGetMeasGroup,
) -> List[Measure]:
    """
    Function to map withings weight group into a list of measures.

    Args:
        measure_group (MeasureGetMeasGroup): Withings API response weight group
    Returns:
        List[Measure]: Mapped weight measures
    """
    return [
        Measure(
            created_at=pendulum.from_timestamp(measure_group.created),
            unit=WEIGHT_UNIT,
            value=float(
                parse_kg_to_lb(
                    measure.value * pow(10, measure.unit),
                    WEIGHT_DECIMAL_POSITIONS,
                )
            ),
        )
        for measure in measure_group.measures
    ]
