from json import dumps
from typing import Dict, Any
import pendulum

from app.pydantic_model.transtek import (
    TranstekStatusMessage,
    TranstekStatusTestMessage,
    TranstekTelemetryMessage,
    TranstekTelemetryTestMessage,
    CarrierListResponse,
    UpdateTrackingDataResponse,
)
from fastapi import (
    APIRouter,
    Request,
    Response,
    status,
    HTTPException,
    Depends,
)

from app.settings import get_settings
from app.log.logging import logger
from app.routers.requests.api_request import (
    PairDeviceRequest,
    UpdateTrackingDataRequest,
)

from ciba_iot_etl.extract.transtek_api.common import MioConnectError
from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.pydantic.common import TRACKING_URLS
from ciba_iot_etl.models.pydantic.devices import MemberDeviceData
from ciba_iot_etl.repositories.devices_repository import (
    MemberDevicesRepository,
)
from ciba_iot_etl.repositories.transtek_repository import TranstekRepository

router = APIRouter(prefix="/transtek", tags=["transtek-router"])
settings = get_settings()

HEADER_NAME = "x-ciba-transtek-api-key"
TRANSTEK_CIBA_KEY = settings.TRANSTEK_CIBA_KEY


def get_mio_connect_client() -> MioConnectClient:
    settings = get_settings()
    return MioConnectClient(api_key=settings.TRANSTEK_API_KEY)


async def get_member_by_request(request_data: PairDeviceRequest) -> Member:
    member = await Member.get_by_platform(
        platform_type=request_data.member_type.value,
        platform_id=request_data.member_id,
    )
    if not member:
        logger.warning(
            f"Member not found: platform_type={request_data.member_type.value}, "
            f"platform_id={request_data.member_id}"
        )
        raise HTTPException(status_code=404, detail="Member not found")

    logger.info(f"Found member: {member.id}")
    return member


async def get_device_info(
    request_data: PairDeviceRequest, client: MioConnectClient
) -> Dict[str, Any]:
    try:
        if request_data.imei:
            logger.info(f"Retrieving device by IMEI: {request_data.imei}")
            device = await client.get_device_by_imei(request_data.imei)
        elif request_data.serial_number:
            logger.info(
                f"Retrieving device by serial number: {request_data.serial_number}"
            )
            device = await client.get_device(request_data.serial_number)
        else:
            raise HTTPException(
                status_code=400,
                detail="Either IMEI or serial number must be provided",
            )

        if not device or "deviceId" not in device:
            raise HTTPException(
                status_code=404,
                detail="Device not found or invalid device data",
            )

        logger.info(f"Retrieved device: {device.get('deviceId')}")
        return device

    except MioConnectError as e:
        logger.error(f"Failed to retrieve device: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to retrieve device information: {str(e)}",
        )


async def activate_device(device_id: str, client: MioConnectClient) -> None:
    try:
        logger.info(f"Activating device: {device_id}")
        await client.activate_device(device_id=device_id)
        logger.info(f"Device activated successfully: {device_id}")
    except MioConnectError as e:
        logger.error(f"Device activation failed for {device_id}: {e}")
        raise HTTPException(
            status_code=400, detail=f"Device activation failed: {str(e)}"
        )


async def save_device_pairing(member: Member, device: Dict[str, Any]) -> None:
    try:
        device_data = MemberDeviceData(
            last_synced_at=pendulum.now(),
            external_id=device["deviceId"],
            device_type=device.get("modelNumber", "Unknown"),
            vendor=DeviceType.TRANSTEK,
        )

        await MemberDevicesRepository.add_device(
            member_id=member.id, device_data=device_data
        )

        logger.info(
            f"Device pairing saved: member_id={member.id}, "
            f"device_id={device['deviceId']}"
        )

    except Exception as e:
        logger.error(f"Failed to save device pairing: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to save device pairing to database"
        )


@router.post("/pair_device")
async def pair_device(
    request_data: PairDeviceRequest,
    client: MioConnectClient = Depends(get_mio_connect_client),
):
    """Pair a device to a member"""
    logger.info(
        f"Starting device pairing process for member: {request_data.member_id}"
    )

    try:
        member = await get_member_by_request(request_data)
        device = await get_device_info(request_data, client)
        await activate_device(device["deviceId"], client)
        await save_device_pairing(member, device)

        logger.info(
            f"Device pairing completed successfully: "
            f"member_id={member.id}, device_id={device['deviceId']}"
        )

        return {
            "paired": True,
            "device_id": device["deviceId"],
            "member_id": str(member.id),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during device pairing: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during device pairing",
        )


@router.post("/forwardtelemetry", status_code=status.HTTP_201_CREATED)
async def forward_telemetry(
    request: Request,
    message: (
        TranstekStatusMessage
        | TranstekTelemetryMessage
        | TranstekStatusTestMessage
        | TranstekTelemetryTestMessage
    ),
    response: Response,
):
    if (
        HEADER_NAME not in request.headers
        or request.headers[HEADER_NAME] != TRANSTEK_CIBA_KEY
    ):
        json_body = await request.json()
        body = dumps(
            json_body,
            indent=4,
            sort_keys=True,
        )
        logger.error(f"Unauthorized request to Transtek Endpoint: {body}")
        response.status_code = status.HTTP_401_UNAUTHORIZED
        return

    json_message = dumps(
        message, default=lambda x: x.__dict__, indent=4, sort_keys=True
    )
    logger.info(json_message)

    return


@router.post(
    "/tracking-data",
    response_model=UpdateTrackingDataResponse,
    status_code=status.HTTP_200_OK,
)
async def update_tracking_data(
    request_data: UpdateTrackingDataRequest,
) -> UpdateTrackingDataResponse:
    """Update tracking data for a Transtek device."""
    try:
        tracking_url = await TranstekRepository.update_tracking_data(
            device_id=request_data.serial_number,
            imei=request_data.imei,
            tracking_number=request_data.tracking_number,
            carrier=request_data.carrier,
        )

        return UpdateTrackingDataResponse(tracking_url=tracking_url)

    except ValueError as e:
        logger.error(f"Validation error updating tracking data: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error updating tracking data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tracking data",
        )


@router.get(
    "/carriers",
    response_model=CarrierListResponse,
    status_code=status.HTTP_200_OK,
)
async def get_carriers() -> CarrierListResponse:
    """
    Returns a list of supported carriers and their tracking link templates.
    """
    try:
        return CarrierListResponse(
            carriers=TRACKING_URLS,
        )
    except Exception as e:
        logger.error(f"Error retrieving carrier list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve carrier list",
        )
