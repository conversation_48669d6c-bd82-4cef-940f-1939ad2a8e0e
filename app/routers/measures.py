import asyncio
from datetime import date
from http import HTTPStatus
from typing import Optional

from fastapi import APIRouter, Security
from fastapi.exceptions import HTTPException
from fastapi.security import API<PERSON>eyHeader

from app.auth.constants import X_AUTH_KEY
from ciba_iot_etl.models.db.member import Member

from app.common.exceptions import MeasuresException
from app.pydantic_model.measures_api import (
    DashboardSummary,
    GlucoseMeasuresResponse,
    GroupOption,
    MeasuresResponse,
    MeasureType,
    WeightUnit,
    DistanceUnit,
)
from app.services.measures.blood_pressure import BloodPressureService
from app.services.measures.glucose_level import GlucoseLevelService
from app.services.measures.heart_rate import HeartRateService
from app.services.measures.weight import WeightService
from app.services.measures.activity import ActivityService
from app.services.measures.sleep import SleepService

router = APIRouter(
    prefix="/measures",
    dependencies=[
        Security(APIKeyHeader(name=X_AUTH_KEY)),
    ],
)


@router.get("/summary")
async def get_summary(
    member_id: str,
    member_type: str,
    start_date: date,
    end_date: date,
    weight_unit: Optional[WeightUnit] = WeightUnit.KG,
    distance_unit: Optional[DistanceUnit] = DistanceUnit.MI,
) -> DashboardSummary:
    """
    Endpoint to get a measures summary in the provided date range.
    """
    if start_date > end_date:
        raise HTTPException(HTTPStatus.BAD_REQUEST, "Start date must be before end date.")

    member = await Member.get_by_platform(
        platform_type=member_type,
        platform_id=member_id,
    )

    if not member:
        raise HTTPException(HTTPStatus.NOT_FOUND, "Member not found")

    try:
        weight, heart_rate, blood_pressure, activity, sleep, glucose = await asyncio.gather(
            WeightService.get_dashboard_summary(
                member.id, start_date, end_date, weight_unit
            ),
            HeartRateService.get_dashboard_summary(
                member.id, start_date, end_date
            ),
            BloodPressureService.get_dashboard_summary(
                member.id, start_date, end_date
            ),
            ActivityService.get_dashboard_summary(
                member.id, start_date, end_date, distance_unit
            ),
            SleepService.get_dashboard_summary(
                member.id, start_date, end_date
            ),
            GlucoseLevelService.get_dashboard_summary(
                member.id, start_date, end_date
            ),
        )

        return DashboardSummary(
            bloodPressure=blood_pressure,
            heartRate=heart_rate,
            weight=weight,
            activity=activity,
            sleep=sleep,
            glucoseLevel=glucose,
        )
    except MeasuresException as error:
        raise HTTPException(HTTPStatus.INTERNAL_SERVER_ERROR, str(error))


@router.get("/types/{measure_type}")
async def get_measures(  # pylint: disable=too-many-arguments
    measure_type: MeasureType,
    member_id: str,
    member_type: str,
    start_date: date,
    end_date: date,
    unit: Optional[WeightUnit] = WeightUnit.KG,
    distance_unit: Optional[DistanceUnit] = DistanceUnit.MI,
    group_by: Optional[GroupOption] = None,
) -> MeasuresResponse | GlucoseMeasuresResponse:
    """
    Endpoint to get a member_id measures in the provided date range.
    """
    if start_date > end_date:
        raise HTTPException(HTTPStatus.BAD_REQUEST, "Start date must be before end date.")

    member = await Member.get_by_platform(
        platform_type=member_type,
        platform_id=member_id,
    )

    if not member:
        raise HTTPException(HTTPStatus.NOT_FOUND, "Member not found")

    try:
        match measure_type:
            case MeasureType.WEIGHT:
                return await WeightService.get_measures_data(
                    member.id, start_date, end_date, unit, group_by
                )
            case MeasureType.BLOOD_PRESSURE:
                return await BloodPressureService.get_measures_data(
                    member.id, start_date, end_date, group_by
                )
            case MeasureType.HEART_RATE:
                return await HeartRateService.get_measures_data(
                    member.id, start_date, end_date, group_by
                )
            case MeasureType.ACTIVITY:
                return await ActivityService.get_measures_data(
                    member.id, start_date, end_date, distance_unit, group_by
                )
            case MeasureType.SLEEP:
                return await SleepService.get_measures_data(
                    member.id, start_date, end_date
                )
            case MeasureType.GLUCOSE_LEVEL:
                return await GlucoseLevelService.get_measures_data(
                    member.id, start_date, end_date, group_by
                )
            case _:
                raise HTTPException(HTTPStatus.BAD_REQUEST, "Invalid measure type")
    except MeasuresException as error:
        raise HTTPException(HTTPStatus.INTERNAL_SERVER_ERROR, str(error))
