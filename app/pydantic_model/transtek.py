from enum import Enum
from typing import Literal, Optional, Union, Dict
from pydantic import BaseModel, Field


class TranstekMessageType(str, Enum):
    TELEMETRY = "telemetry"
    STATUS = "status"


class TranstekModelNumber(str, Enum):
    SCALE_GEN_2 = "GBS-2104-G"
    BPM_GEN_2 = "TMB-2092-G"
    TEST_TELEMETRY = "forward-telemetry-model-number"
    TEST_STATUS = "forward-status-model-number"


class TranstekBaseError(BaseModel):
    imei: Optional[str] = None
    uid: Optional[str] = None
    err_ts: int = Field(description="Error timestamp")
    err_c: int = Field(description="Error code")


class TranstekScaleError(TranstekBaseError):
    data_type: Literal["scale_gen2_err"] = Field(description="Message type")


class TranstekBPMError(TranstekBaseError):
    data_type: Literal["bpm_gen2_err"] = Field(description="Message type")


class TestStatus(BaseModel):
    mfr: str = Field(description="Manufacturer")
    hv: str = Field(description="Hardware Version")
    fv: str = Field(description="Modem Version")
    uid: str = Field(description="Device ID (same as serial number)")
    imei: str = Field(
        description="15-digit IMEI number of the product, same as the IMEI number of the communication module."
    )
    imsi: str = Field(description="Mobile Identity Number")
    did: str
    bat: int = Field(description="Battery")
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )


class TestTelemetry(BaseModel):
    wt: int = Field(description="Test value from Transtek")
    bmi: int = Field(description="Test value from Transtek")
    fat: int = Field(description="Test value from Transtek")
    bm: int = Field(description="Test value from Transtek")
    mus: int = Field(description="Test value from Transtek")
    ts: int = Field(description="Test value from Transtek")


class BaseData(BaseModel):
    imei: str = Field(
        description="15-digit IMEI number of the product, same as the IMEI number of the communication module."
    )


class ScaleStatus(BaseData):
    data_type: Literal["scale_gen2_status"] = Field(description="Message type")
    uid: str = Field(description="Device ID (same as Serial Number)")
    iccid: str = Field(description="ICCID - SIM Card Number")
    imsi: str = Field(description="Mobile Identity Number")
    bat: int = Field(description="Battery")
    tz: str = Field(description="Time Zone")
    ops: str = Field(description="Operation network")
    net: str = Field(description="Service")
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )
    mfr: str = Field(description="Manufacturer Lifesense")
    at_t: int = Field(description="Attach Time")
    hv: str = Field(description="Hardware Version")
    fv: str = Field(description="Modem Version")
    appv: str = Field(description="APP Version")
    mcuv: str = Field(description="MCU Version")


class ScaleTelemetry(BaseData):
    data_type: Literal["scale_gen2_measure"] = Field(
        description="Message type"
    )
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )
    uid: str = Field(description="DeviceId")
    iccid: str = Field(description="ICCID - SIM Card Number")
    bat: int = Field(description="Battery")
    tz: str = Field(description="Time Zone")
    wet: int = Field(description="Weight Stable Time(uuit:s)")
    lts: int = Field(description="Weight Lock Count(0~255)")
    wt: int = Field(description="Measured weight, in grams")
    ts: int = Field(description="Timestamp to make measurement, in seconds")


class BPMStatus(BaseData):
    data_type: Literal["bpm_gen2_status"] = Field(description="Message type")
    bat: int = Field(description="Battery")
    tz: str = Field(description="Time Zone")
    ops: str = Field(description="Operation network")
    net: str = Field(description="Service")
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )
    tp: int = Field(description="Soc temperature (unit:0.1C)")
    at_t: int = Field(description="Attach Time")


class BPMAnalysis(BaseData):
    data_type: Literal["bpm_gen2_analysis"] = Field(description="Message type")
    op: int
    it_100: int = Field(alias="100it")
    it_50: int = Field(alias="50it")
    zp: int
    ovip: int
    ovit: int
    wp: int
    pn: int


class BPMInfo(BaseData):
    data_type: Literal["bpm_gen2_status"] = Field(description="Message type")
    bat: int = Field(description="Battery")
    tz: str = Field(description="Time Zone")
    ops: str = Field(description="Operation network")
    net: str = Field(description="Service")
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )
    tp: int = Field(description="Soc temperature(unit:0.1C)")
    at_t: int = Field(description="Attach Time")


class BPMHeartbeat(BaseData):
    data_type: Literal["bpm_gen2_log"] = Field(description="Message type")
    iccid: str = Field(description="ICCID - SIM Card Number")
    bat: int = Field(description="Battery")
    mcuv: str = Field(description="MCU Version")
    appv: str = Field(description="APP Version")
    fv: str = Field(description="Modem Version")
    algv: str = Field(description="Algorithm Version")
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )


class BPMTelemetry(BaseData):
    data_type: Literal["bpm_gen2_measure"] = Field(description="Message type")
    ihb: bool = Field(description="Irregular Heartbeat")
    pul: int = Field(description="Pulse (40-99) beat/min")
    tz: str = Field(description="Time Zone")
    tri: bool = Field(
        description="The flag of avg of triple/single measurement result"
    )
    sys: int = Field(description="Systolic blood pressure 60 mmHg - 230 mmHg")
    sig: int = Field(
        description="Signal (0-31) - 0-15=Weak, 16-20=Medium, 21-31=Strong"
    )
    iccid: str = Field(description="ICCID - SIM Card Number")
    bat: int = Field(description="Battery")
    sn: str = Field(
        description="12-digit serial number, generated by Transtek factory SN management system."
    )
    user: int = Field(description="User ID of the device")
    dia: int = Field(description="Systolic blood pressure 40 mmHg - 130 mmHg")
    hand: bool = Field(description="Hand shaking")
    ts: int = Field(description="Measurement timestamp")


class TranstekBaseMessage(BaseModel):
    deviceId: str = Field(description="Device ID, globally unique")
    createdAt: int = Field(
        description="Time for the record to be created on MioConnect server. It could be different than creating time from device side, since device may not be able to send data in time."
    )
    isTest: bool = Field(description="If this request is for testing purpose")
    modelNumber: TranstekModelNumber = Field(description="Device Model Number")
    messageType: Optional[TranstekMessageType]


class TranstekStatusMessage(TranstekBaseMessage):
    status: Union[
        ScaleStatus,
        BPMStatus,
        BPMHeartbeat,
        BPMAnalysis,
        TranstekScaleError,
        TranstekBPMError,
    ] = Field(discriminator="data_type")


class TranstekTelemetryMessage(TranstekBaseMessage):
    data: Union[BPMTelemetry, ScaleTelemetry] = Field(
        discriminator="data_type"
    )


class TranstekStatusTestMessage(TranstekBaseMessage):
    status: TestStatus


class TranstekTelemetryTestMessage(TranstekBaseMessage):
    data: TestTelemetry


class UpdateTrackingDataResponse(BaseModel):
    tracking_url: str
    message: str = "Tracking data updated successfully"


class CarrierListResponse(BaseModel):
    carriers: Dict
