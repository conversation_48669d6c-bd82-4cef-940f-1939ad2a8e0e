from typing import Optional
from uuid import UUID
from enum import StrEnum, auto

from pydantic import BaseModel
from ciba_iot_etl.models.pydantic.common import ActivityDevice


class ConnectionToken(BaseModel):
    token: UUID
    healthy: Optional[bool] = None
    account_id: Optional[str] = None


class Subscription(BaseModel):
    expires_in: int


class ConnectionStatus(ConnectionToken):
    healthy: Optional[bool] = None
    account_id: Optional[str] = None
    subscription: Optional[Subscription] = None


class DeviceStatusEnum(StrEnum):
    NOT_CONNECTED = auto()
    CONNECTED = auto()
    RECONNECT = auto()


class DeviceStatus(BaseModel):
    """Device status, useful when querying multiple devices"""
    device: ActivityDevice
    status: DeviceStatusEnum

class MemberDeviceStatus(BaseModel):
    member_id: str
    devices: list[DeviceStatus]