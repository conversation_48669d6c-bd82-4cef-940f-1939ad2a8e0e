from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from app.graphql_api.participant.enums import ParticipantStatusEnum


@strawberry.type
class ParticipantType:
    """Participant model based graphql type."""

    id: UUID
    email: str
    first_name: str
    last_name: str
    chat_identity: str
    group_id: UUID
    member_id: UUID
    status: ParticipantStatusEnum
    cohort_end_date: Optional[datetime] = None
    cognito_sub: UUID | None
    medical_record: str
    heads_up_token: UUID | None
    solera_id: UUID | None
    solera_program_id: str | None
    is_weight: bool
    program_group_id: UUID | None
    disenrollment_reason: str | None = None
    disenrollment_date: datetime | None = None


@strawberry.type
class VerifyChangePasswordTokenType:
    email: str
    tmp_password: str


@strawberry.type
class TimeZonesType:
    """Time zone types."""

    id: str
    label: str


@strawberry.type
class HangoutUrlType:
    """Link to hangout url."""

    url: str


@strawberry.type
class HangoutUrl:
    """Link to hangout url."""

    url: str


@strawberry.type
class ParticipantEmail:
    """Email of the verified user"""

    email: str


@strawberry.type
class ParticipantActivityType:
    """Participant activity model based graphql type."""

    id: UUID
    participant_id: UUID
    value: str
    activity_type: str
    unit: str
    activity_device: str
    created_at: str


@strawberry.type
class WeightDataType:
    timestamp: str
    value: float


@strawberry.type
class ParticipantWeightsType:
    """Participant activity model based graphql type."""

    id: UUID
    participant_id: UUID
    value: float
    activity_type: str
    unit: str
    activity_device: str
    created_at: str


@strawberry.type
class AddActivityResultType:
    success: bool
