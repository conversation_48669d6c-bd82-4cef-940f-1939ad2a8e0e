# pylint: disable=duplicate-code
from uuid import UUID

import strawberry
from strawberry.types import Info
from tortoise.transactions import in_transaction
from ciba_participant.cohort.crud import CohortMembersRepository
from ciba_participant.chat_api.chat_api import assign_participant_to_chat
from ciba_participant.participant.models import Participant, ParticipantMeta
from ciba_participant.participant.crud import (
    ParticipantRepository,
    ParticipantMetaRepository,
    ParticipantMetaCreate,
    ParticipantMetaUpdate,
)
from ciba_participant.cohort.models import Cohort
from ciba_participant.participant.service import ParticipantService
from ciba_participant.settings import ENV
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
    ParticipantActivityDevice,
    ActivityUnit,
    ParticipantActivityCategory,
)
from ciba_participant.classes.models import BookingStatusEnum

from app.settings import get_settings
from app.log.logging import logger
from app.auth.permissions import IsAdmin, IsAuthenticated, IsSoleraUser
from app.graphql_api.classes.mutations.update_booking_status import (
    update_status_by_participant_id,
)
from app.graphql_api.participant.enums import ParticipantStatusEnum
from app.graphql_api.participant.inputs import SignUpInput
from app.graphql_api.participant.errors import (
    InvalidVerifyChangePasswordToken,
    ParticipantDneError,
    ParticipantDuplicatedError,
    VerifyChangePasswordTokenExpired,
)
from app.graphql_api.participant.types import (
    ParticipantEmail,
    ParticipantType,
    VerifyChangePasswordTokenType,
    AddActivityResultType,
)
import pendulum

settings = get_settings()


async def sign_up_participant(
    info: Info,
    data: SignUpInput,
) -> ParticipantType | ParticipantDuplicatedError:
    """Signup participant mutation."""
    data = ParticipantService(
        participant_id=None,
        email=data.email.lower(),
        first_name=data.first_name,
        last_name=data.last_name,
        group_id=UUID(data.group_id),
        member_id=UUID(data.member_id),
        status=ParticipantStatusEnum.PENDING.value,
        is_test=False,
    )
    participant = await ParticipantRepository.sign_up(data, info)
    # pylint: disable=duplicate-code
    if participant:
        return ParticipantType(
            id=participant.id,
            email=participant.email,
            first_name=participant.first_name,
            last_name=participant.last_name,
            chat_identity=participant.chat_identity,
            group_id=participant.group_id,
            member_id=participant.member_id,
            status=participant.status,
            cognito_sub=None,
            medical_record=participant.medical_record,
            heads_up_token=None,
            solera_id=None,
            solera_program_id=None,
            is_weight=False,
        )
    return ParticipantDuplicatedError()


async def update_status(
    info: Info,
    participant_id: UUID,
    status: ParticipantStatusEnum,
) -> ParticipantType | ParticipantDneError:
    """Update participant status."""

    participant = await ParticipantRepository.update_status(
        participant_id=participant_id, status=status.value, info=info
    )
    if participant:
        return ParticipantType(
            id=participant.id,
            email=participant.email,
            first_name=participant.first_name,
            last_name=participant.last_name,
            chat_identity=participant.chat_identity,
            group_id=participant.group_id,
            member_id=participant.member_id,
            status=participant.status,
            cognito_sub=None,
            medical_record=participant.medical_record,
            heads_up_token=None,
            solera_id=None,
            solera_program_id=None,
            is_weight=False,
        )
    return ParticipantDneError()


async def forgot_password(
    info: Info,
    email: str,
) -> bool:
    """Forgot password flow."""
    email = email.lower()
    await ParticipantRepository.resend_confirmation_link(
        info=info, email=email
    )
    return await ParticipantRepository.forgot_password(email)


async def verify_change_password(
    info: Info,
    token: str,
) -> (
    VerifyChangePasswordTokenType
    | InvalidVerifyChangePasswordToken
    | VerifyChangePasswordTokenExpired
):
    """Verify change password."""
    resp = await ParticipantRepository.verify_change_password_token(
        token, info
    )
    if "InvalidVerifyChangePasswordToken" in resp.keys():
        return InvalidVerifyChangePasswordToken()
    if "VerifyChangePasswordTokenExpired" in resp.keys():
        return VerifyChangePasswordTokenExpired()
    return VerifyChangePasswordTokenType(**resp)


async def resend_confirmation_link_by_token(
    info: Info,
    token: str,
) -> bool:
    """Resend participant confirmation link by token."""
    return await ParticipantRepository.resend_confirmation_link(
        token=token, info=info
    )


async def resend_confirmation_link(
    info: Info,
    email: str,
) -> bool:
    """Resend participant confirmation link by email."""
    email = email.lower()
    return await ParticipantRepository.resend_confirmation_link(
        info=info, email=email
    )


async def set_new_password(
    info: Info,
    cohort_id: str,
    new_password: str,
    starting_weight: float,
    target_weight: float,
    session_id: UUID,
) -> ParticipantEmail:
    """Set new password for participant."""
    correlation_id = info.context.request.headers.get(
        "X-Request-ID", pendulum.now().int_timestamp
    )

    email = info.context.user.email.lower()
    participant = await ParticipantRepository.set_new_password(
        info=info,
        email=email,
        new_password=new_password,
        correlation_id=correlation_id,
    )

    metas: list[ParticipantMeta] = participant.participant_meta
    meta: ParticipantMeta = metas[0] if len(metas) >= 1 else None

    if meta is None:
        meta = await ParticipantMetaRepository.create_participant_meta(
            ParticipantMetaCreate(participant_id=participant.id, metadata={})
        )

    if meta.metadata is None:
        meta.metadata = {}

    meta.metadata["user_reported_weight"] = starting_weight
    meta.metadata["user_target_weight"] = target_weight

    cohort = await Cohort.filter(id=cohort_id).first()

    input_data = {
        "participant": participant,
        "value": starting_weight,
        "unit": ActivityUnit.LB.value,
        "activity_type": ParticipantActivityEnum.WEIGHT.value,
        "activity_device": ParticipantActivityDevice.MANUAL_INPUT.value,
        "activity_category": ParticipantActivityCategory.WEIGHT.value,
    }
    participant_activity = ParticipantActivity(**input_data)
    cohort_created_at = pendulum.instance(cohort.started_at).add(days=1)

    participant_activity.created_at = cohort_created_at
    participant_activity.updated_at = cohort_created_at
    await participant_activity.save()

    await ParticipantMetaRepository.update_participant_meta(
        ParticipantMetaUpdate(
            id=meta.id,
            participant_id=participant.id,
            metadata=meta.metadata,
        )
    )

    participant_type = ParticipantType(
        id=participant.id,
        email=participant.email,
        first_name=participant.first_name,
        last_name=participant.last_name,
        chat_identity=participant.chat_identity,
        group_id=participant.group_id,
        member_id=participant.member_id,
        status=participant.status,
        cognito_sub=participant.cognito_sub,
        medical_record=participant.medical_record,
        program_group_id=cohort_id,
        heads_up_token=None,
        solera_id=None,
        solera_program_id=None,
        is_weight=False,
    )

    participant_id = (
        info.context.user.id
        if settings.ENV not in [ENV.LOCAL, ENV.TEST]
        else info.context.user.sub
    )
    async with in_transaction():
        cohort = await Cohort.filter(id=cohort_id).get()
        await CohortMembersRepository.create_cohort_member(
            cohort_id=cohort.id,
            participant_id=participant_id,
        )

        await update_status_by_participant_id(
            participant_id, session_id, BookingStatusEnum.BOOKED
        )

        if settings.ENV not in [ENV.LOCAL, ENV.TEST]:
            if isinstance(participant_type, ParticipantType):
                await assign_participant_to_chat(
                    cohort.unique_name,
                    participant_id,
                    participant_type.chat_identity,
                )

    return ParticipantEmail(email=email)


async def add_weight_data(
    info: Info,
    value: float,
    activity_device: str,
) -> AddActivityResultType:
    """Add weight data to participant."""
    try:
        activity_device = ParticipantActivityDevice(activity_device).value
    except ValueError as e:
        logger.error("Invalid activity device: %s: %s", activity_device, e)
        return AddActivityResultType(success=False)

    participant = (
        await Participant().filter(id=info.context.user.sub).get_or_none()
    )
    if not participant:
        return AddActivityResultType(success=False)

    input_data = {
        "participant": participant,
        "value": value,
        "unit": ActivityUnit.LB.value,
        "activity_type": ParticipantActivityEnum.WEIGHT.value,
        "activity_device": activity_device,
        "activity_category": ParticipantActivityCategory.WEIGHT.value,
    }
    participant_activity = ParticipantActivity(**input_data)
    await participant_activity.save()
    return AddActivityResultType(success=True)


@strawberry.type()
class ParticipantMutation:
    """Participant mutations."""

    sign_up_participant = strawberry.field(resolver=sign_up_participant)
    update_status = strawberry.field(
        resolver=update_status, permission_classes=[IsAdmin]
    )
    resend_confirmation_link = strawberry.field(
        resolver=resend_confirmation_link
    )
    resend_confirmation_link_by_token = strawberry.field(
        resolver=resend_confirmation_link_by_token
    )
    verify_change_password = strawberry.field(resolver=verify_change_password)

    set_new_password = strawberry.field(
        resolver=set_new_password, permission_classes=[IsSoleraUser]
    )

    add_weight_data = strawberry.field(
        resolver=add_weight_data,
        permission_classes=[IsAuthenticated],
    )
