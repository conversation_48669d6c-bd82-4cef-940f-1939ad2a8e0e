from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from app.graphql_api.content_library.enums import (
    MaterialTagEnum,
    MaterialStatusEnum,
    MaterialActivityEnum,
)


@strawberry.type
class ContentMaterialElement:
    id: UUID
    added_at: datetime
    mime_type: str
    title: str
    description: str
    status: MaterialStatusEnum
    tags: list[MaterialTagEnum]
    activity_types: list[MaterialActivityEnum]
    link: Optional[str]
    link_expiration: Optional[datetime] = strawberry.field(default=None)
    is_favorite: Optional[bool] = strawberry.field(default=False)


@strawberry.type
class ContentMaterialList:
    success: bool
    error: Optional[str] = strawberry.field(default=None)
    items: list[ContentMaterialElement]
    total: int
    total_pages: int
