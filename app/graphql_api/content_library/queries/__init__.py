from typing import Optional

import strawberry
from strawberry.types import Info

from app.auth.permissions import IsAuthenticated
from app.graphql_api.content_library.exceptions import MaterialQueryException
from app.graphql_api.content_library.inputs import (
    MaterialFilters,
    FavoriteFilters,
)
from app.graphql_api.content_library.queries.common import (
    build_error_response,
    get_participant_program,
)
from app.graphql_api.content_library.queries.get_content import get_content
from app.graphql_api.content_library.queries.get_favorite_content import (
    get_favorite_content,
)
from app.graphql_api.content_library.types import ContentMaterialElement
from app.graphql_api.pagination import Connection
from app.log.logging import logger


async def get_content_material(
    info: Info,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[MaterialFilters] = None,
) -> Connection[ContentMaterialElement]:
    """
    Resolver to handle the content material retrieval.
    """
    participant_id = info.context.user.sub
    program = await get_participant_program(participant_id)

    logger.info(
        "Retrieving content material from program: %s, filters: %s",
        program.id if program else None,
        filters,
    )

    try:
        return await get_content(
            participant_id, program, page, per_page, filters
        )
    except (ValueError, MaterialQueryException) as error:
        return build_error_response(str(error), page, per_page)


async def get_favorite_materials(
    info: Info,
    page: int = 1,
    per_page: int = 10,
    filters: Optional[FavoriteFilters] = None,
) -> Connection[ContentMaterialElement]:
    """
    Resolver to handle the favorite content retrieval.
    """
    participant_id = info.context.user.sub
    program = await get_participant_program(participant_id)

    logger.info(
        "Retrieving favorite material for participant %s with filters: %s",
        participant_id,
        filters,
    )

    try:
        return await get_favorite_content(
            participant_id, program, page, per_page, filters
        )
    except (ValueError, MaterialQueryException) as error:
        return build_error_response(str(error), page, per_page)


@strawberry.type()
class ContentLibraryQuery:
    """Content library graphql queries."""

    get_content_material: Connection[ContentMaterialElement] = (
        strawberry.field(
            resolver=get_content_material, permission_classes=[IsAuthenticated]
        )
    )
    get_favorite_materials: Connection[ContentMaterialElement] = (
        strawberry.field(
            resolver=get_favorite_materials,
            permission_classes=[IsAuthenticated],
        )
    )
