from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from app.settings import get_settings

settings = get_settings()

CONNECTION_STRING = (
    "postgresql+psycopg2://"
    + settings.POSTGRES_USER
    + ":"
    + settings.POSTGRES_PASSWORD
    + "@"
    + settings.POSTGRES_HOST
    + ":"
    + str(settings.POSTGRES_PORT)
    + "/"
    + settings.POSTGRES_DB
)

engine = create_engine(CONNECTION_STRING)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


# Dependency to get the database session
def get_db() -> Generator[Session, None, None]:
    """Dependency to get the database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# async_connection_string = (
#         "postgresql+asyncpg://"
#         + settings.POSTGRES_USER
#         + ":"
#         + settings.POSTGRES_PASSWORD
#         + "@"
#         + settings.POSTGRES_HOST
#         + ":5432"
#         + "/"
#         + settings.POSTGRES_DB
# )
# async_engine = create_async_engine(async_connection_string, echo=True)
#
# async_session = sessionmaker(
#     async_engine, class_=AsyncSession, expire_on_commit=False
# )
#
#
# async def get_session() -> AsyncSession:
#     async with async_session() as session:
#         yield session
