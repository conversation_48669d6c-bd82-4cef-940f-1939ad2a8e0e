import sys

from app.log.logging import logger
from ciba_iot_etl.models.db.dexcom import Dexcom
from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_platform import MemberPlatform
from ciba_iot_etl.models.db.withings import Withings


class User:
    """User class for check some data fron user"""

    def __init__(self, email: str = "", device: str = ""):
        self.email = email
        self.device = device.title()

    async def map_to_platform(
        self, external_type: str, external_id: str
    ) -> Member:
        """Map RPM member with external platform"""
        member = await Member.get_by_platform(external_type, external_id)
        if member is None:
            logger.info("member for platform not exists")
            member, _ = await Member.get_or_create(email=self.email)
            await MemberPlatform(
                platform_type=external_type,
                platform_id=external_id,
                member=member,
            ).save()
            logger.info("member for platform created, id: %s", member.id)

        return member

    async def related_device(
        self,
    ) -> (Withings | Fitbit | Dexcom) | None:
        """
        Check if user exist in database
        :return: user id or None
        """

        cls = get_class(self.device)
        return await cls.filter(member__email=self.email).get_or_none()


def get_class(
    class_name: str,
) -> Withings | Fitbit | Dexcom:
    """Generate class from string"""
    return getattr(sys.modules[__name__], class_name)
