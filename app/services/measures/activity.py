import datetime
from datetime import date, timedelta
from typing import Optional
from uuid import UUID

import pendulum
from ciba_iot_etl.models.db.activity import Activity, ActivityCategory
from ciba_iot_etl.helpers.measurement import UnitOfMeasurement, get_date_from_measure
from tortoise.expressions import RawSQL
from tortoise.queryset import QuerySet

from app.helpers.dates import get_dates_range
from app.pydantic_model.measures_api import (
    ActivitySummary, ActivityDay, TodaysSummary, DistanceUnit,
    GroupOption, MeasuresResponse, MeasureType
)

EXERCISE_DAYS = 7


def get_base_query(
        member_id: UUID, start_date: date, end_date: date
) -> QuerySet:
    return Activity.filter(
        member_id=member_id,
        created_at__gte=start_date,
        created_at__lt=end_date + timedelta(days=1),
    )


class ActivityService:
    """
    Service class that handles activity measurements.
    """
    @staticmethod
    async def get_dashboard_summary(
            member_id: UUID,
            start_date: date,
            end_date: date,
            distance_unit: DistanceUnit = DistanceUnit.MI
    )-> Optional[ActivitySummary]:
        base_query = get_base_query(member_id, start_date, end_date)
        measures, today, last_update = await ActivityService.get_measures(
            base_query, get_dates_range(start_date, end_date), distance_unit
        )

        return ActivityService.get_summary(measures, today, end_date, last_update)

    @staticmethod
    async def get_measures(
            base_query: QuerySet,
            days_in_range: list[str],
            distance_unit: DistanceUnit = DistanceUnit.MI,
    ) -> (list[ActivityDay], TodaysSummary, Optional[datetime]):
        """
        Method to retrieve weight measures in plain form.
        """
        measures = await base_query.order_by("created_at").values('created_at', 'unit', 'value', 'activity_category')
        last_update = get_date_from_measure(measures[-1]) if measures else None
        measures, today = convert_to_activity_day(
            measures, distance_unit=distance_unit, days_in_range=days_in_range
        )

        return measures, today, last_update

    @staticmethod
    async def get_grouped_measures(
            base_query: QuerySet,
            group_by: GroupOption,
            days_in_range: list[str],
            distance_unit: DistanceUnit = DistanceUnit.MI
    ) -> (list[ActivityDay], TodaysSummary, Optional[datetime]):
        """
        Method to retrieve weight measures in plain form.
        """
        query = (
            base_query.annotate(
                group_date=RawSQL(
                    f"DATE_TRUNC('{group_by.value}', created_at)"
                ),
                value=RawSQL("ROUND(AVG(value), 2)"),
            )
            .group_by("group_date", "unit", "activity_category")
            .order_by("group_date")
        )
        raw_measures = await query.values("value", "unit", "group_date", "activity_category")
        last_update = get_date_from_measure(raw_measures[-1]) if raw_measures else None
        measures, today = convert_to_activity_day(raw_measures, distance_unit, days_in_range)

        return measures, today, last_update

    @staticmethod
    async def get_measures_data(
            member_id: UUID,
            start_date: date,
            end_date: date,
            distance_unit: DistanceUnit = DistanceUnit.MI,
            group_by: GroupOption = None
    ) -> MeasuresResponse:
        base_query = get_base_query(member_id, start_date, end_date)
        days_in_range = get_dates_range(start_date, end_date)
        measures, today, last_update = (
            await ActivityService.get_grouped_measures(base_query, group_by, days_in_range, distance_unit)
            if group_by
            else await ActivityService.get_measures(base_query, days_in_range, distance_unit)
        )

        return MeasuresResponse(
            type=MeasureType.ACTIVITY,
            unit=distance_unit.value,
            summary=ActivityService.get_summary(measures, today, end_date, last_update),
            values=measures,
        )

    @staticmethod
    def get_summary(
            measures: list[ActivityDay],
            today: TodaysSummary,
            end_date: date,
            last_update: Optional[datetime],
    ) -> Optional[ActivitySummary]:
        """
        Method to calculate the weight summary.
        """
        if not measures or not last_update:
            return None

        summary = ActivitySummary(
            today=today,
            exercise_days=calculate_exercise_days(measures, end_date),
            total_days=EXERCISE_DAYS,
            activity_details=None,
            lastUpdate=last_update,
        )

        return summary


def convert_to_activity_day(
        measures: list[dict],
        distance_unit: DistanceUnit,
        days_in_range: list[str]
) -> (list[ActivityDay], TodaysSummary):
    activity_days = {
        day: ActivityDay(
            exercised=False,
            minutes=0,
            date=day,
            steps=0,
        )
        for day in days_in_range
    }
    previous_activity_date = None
    for measure in measures:
        measure_date = get_date_from_measure(measure)
        measure_value = measure.get('value')
        measure_unit = measure.get('unit')
        if previous_activity_date and previous_activity_date == measure_date.date():
            activity_day = activity_days[previous_activity_date.strftime("%Y-%m-%d")]
            activity_category = measure.get('activity_category', '')
            if activity_category == ActivityCategory.EXERCISE:
                activity_day.exercised = True
                continue
            if activity_category == ActivityCategory.WALK:
                activity_day.steps = int(measure_value)

            if activity_category == ActivityCategory.GENERAL:
                activity_day.minutes += int(measure_value)

        else:
            activity_day = ActivityDay(
                exercised=False,
                minutes=int(measure_value) if measure_unit == UnitOfMeasurement.MINUTE else 0,
                date=measure_date.strftime("%Y-%m-%d"),
                steps=int(measure_value) if measure_unit == UnitOfMeasurement.STEP else 0
            )

        previous_activity_date = measure_date.date()
        activity_days[measure_date.date().strftime("%Y-%m-%d")] = activity_day

    today = get_today_summary(activity_days, distance_unit)

    return activity_days.values(), today


def get_today_summary(activity_days: dict, distance_unit: DistanceUnit) -> TodaysSummary:
    today_key = pendulum.now().date().strftime("%Y-%m-%d")
    if today_key in activity_days:
        today_activity = activity_days[today_key]
        return TodaysSummary(
            steps=today_activity.steps,
            active_minutes=today_activity.minutes,
            distance=calculate_distance(today_activity.steps, distance_unit),
            distance_unit=distance_unit,
        )

    return TodaysSummary(
        steps=0,
        active_minutes=0,
        distance=0,
        distance_unit=distance_unit,
    )


def calculate_distance(steps: int, distance_unit: DistanceUnit) -> float:
    """
    Calculate the distance in miles based on the number of steps and step length.

    Args:
        steps (int): The number of steps taken.
        distance_unit (enum): The distance unit to use.


    Returns:
        float: The distance in miles or km.
    """


    if distance_unit == DistanceUnit.MI:
        # Convert step length to inches
        step_length = 2.5 # Average step length in feet
        step_length_inches = step_length * 12  # 1 foot = 12 inches
        distance = (steps * step_length_inches) / 63360  # 1 mile = 63,360 inches
    elif distance_unit == DistanceUnit.KM:
        step_length = 76.2  # Average step length in cm
        step_length_meters = step_length * 0.3048 # 1 foot = 0.3048 meters
        distance = (steps * step_length_meters) / 10000  # 1 kilometer = 1000 meters
    else:
        raise ValueError("Invalid distance unit")
    return round(distance, 2)  # Round to 2 decimal places


def calculate_exercise_days(activity_list: list[ActivityDay], end_date: date) -> int:
    six_days_ago = end_date - timedelta(days=6)
    last_seven_days = get_dates_range(six_days_ago, end_date)

    return len([day for day in activity_list if day.exercised and day.date in last_seven_days])
