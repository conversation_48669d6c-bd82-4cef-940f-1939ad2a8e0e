from datetime import date, datetime, time, timedelta
from secrets import randbelow
from typing import Optional
from uuid import UUID

from ciba_iot_etl.helpers.measurement import get_date_from_measure
from ciba_iot_etl.models.db.glucose_level import GlucoseLevel
from tortoise.queryset import QuerySet

from app.helpers.glucose_level import get_glucose_events_count, group_measures, get_glucose_level_events
from app.pydantic_model.measures_api import (
    DashboardGlucoseLevelSummary,
    GlucoseLevelEntry,
    GlucoseLevelSummary,
    GlucoseMeasuresResponse,
    GroupOption,
    MeasureType,
)

UNIT = "mg/dL"
MEASUREMENT_CYCLE = 10


def get_base_query(member_id: UUID, start_date: date, end_date: date):
    """
    Method to get the Glucose Level base query
    """
    return GlucoseLevel.filter(
        member_id=member_id,
        created_at__gte=start_date,
        created_at__lt=end_date + timedelta(days=1),
    )


class GlucoseLevelService:
    @staticmethod
    async def get_measures(base_query: QuerySet, last_date: date, with_mocks: bool = False):
        """
        Method to get glucose level measures in plain form
        """
        if with_mocks:
            return GlucoseLevelService.get_fake_measures(last_date)

        return await base_query.order_by("created_at").values(
            "created_at", "value"
        )

    # TODO: remove this mock data when the Dexcom data sync is available
    @staticmethod
    def get_fake_measures(last_date: date):
        days_count = 6
        time_ranges = [
            time(hour=9, minute=15),
            time(hour=9, minute=45),
            time(hour=10),
            time(hour=10, minute=30),
            time(hour=11, minute=30),
            time(hour=11, minute=45),
            time(hour=12),
            time(hour=12, minute=45),
            time(hour=13, minute=15),
            time(hour=13, minute=30),
            time(hour=14, minute=45),
            time(hour=14, minute=45),
            time(hour=15, minute=45),
            time(hour=15, minute=45),
        ]

        fake_measures = []

        for index in reversed(range(days_count)):
            measure_date = last_date - timedelta(days=index)
            for time_range in time_ranges:
                record_date = datetime.combine(measure_date, time_range)
                fake_measures.append({
                    "created_at": record_date,
                    "value": 50 + randbelow(170),
                })

        return fake_measures

    @staticmethod
    def get_summary(measures: list[dict]) -> Optional[GlucoseLevelSummary]:
        """
        Method to get glucose level measures summary
        """
        if not measures:
            return None

        values = [measure["value"] for measure in measures]
        days_count = len(set(get_date_from_measure(measure).date() for measure in measures))

        return GlucoseLevelSummary(
            firstMeasurementDate=get_date_from_measure(measures[0]),
            lastUpdate=get_date_from_measure(measures[-1]),
            isDeviceConnected=True, # TODO: replace when this logic is implemented
            daysWithMeasurements=days_count,
            measurementCycle=MEASUREMENT_CYCLE,
            averageGlucoseLevel=round(sum(values) / len(values)),
        )

    @staticmethod
    async def get_dashboard_summary(
            member_id: UUID,
            start_date: date,
            end_date: date
    ) -> Optional[DashboardGlucoseLevelSummary]:
        """
        Method to get glucose level dashboard summary
        """
        base_query = get_base_query(member_id, start_date, end_date)
        measures = await GlucoseLevelService.get_measures(base_query, end_date, with_mocks=True)

        if not measures:
            return None

        hyperglycemia_count, hypoglycemia_count = get_glucose_events_count(measures)
        base_summary = GlucoseLevelService.get_summary(measures)

        return DashboardGlucoseLevelSummary(
            unit=UNIT,
            hyperglycemiaEventsCount=hyperglycemia_count,
            hypoglycemiaEventsCount=hypoglycemia_count,
            **base_summary.model_dump()
        )

    @staticmethod
    async def get_measures_data(
            member_id: UUID,
            start_date: date,
            end_date: date,
            grou_by: GroupOption = None
    ) -> GlucoseMeasuresResponse:
        """
        Method to get glucose levels in the provided date range
        """
        base_query = get_base_query(member_id, start_date, end_date)
        measures = await GlucoseLevelService.get_measures(base_query, end_date, with_mocks=True)
        events = get_glucose_level_events(measures)

        if grou_by:
            measures = group_measures(measures, grou_by)

        values = [
            GlucoseLevelEntry(
                createdAt=get_date_from_measure(measure),
                value=measure["value"],
            )
            for measure in measures
        ]

        return GlucoseMeasuresResponse(
            unit=UNIT,
            type=MeasureType.GLUCOSE_LEVEL,
            summary=GlucoseLevelService.get_summary(measures),
            values=values,
            hypoAndHyperEvents=events,
        )
