import time

from ciba_iot_etl.extract.fitbit_api.core import FitbitLoader
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_state import MemberState
from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.pydantic.common import (
    Platform,
    ActivityDevice,
    PullDataNotification,
)
from ciba_iot_etl.models.pydantic.fitbit import FitbitPayload
from app.services.queue import SendQueueException, get_queue
from app.log.logging import logger
from app.settings import get_settings
import pendulum


settings = get_settings()

class AuthorisationException(Exception):
    def __init__(
            self, state: str, message: str = "the token parameter is not valid:"
    ) -> None:
        self.message = f"{message}: {state}"
        super().__init__(self.message)


class InvalidStateException(Exception):
    def __init__(
            self, state: str, message: str = "The request state is not valid!"
    ) -> None:
        self.state = state
        self.message = message
        super().__init__(self.message)

class FitbitServiceException(Exception):
    pass


class MemberFitbit:

    def __init__(
            self,
            client: FitbitLoader,
            member: Member,
    ):
        self.client = client
        self.member = member


    async def disconnect_device(self) -> bool:
        """
        Disconnect the member from the Fitbit API
        """

        try:
            fitbit_id = await self.member.get_fitbit_id()
            fitbit_connection = await Fitbit.filter(id=fitbit_id).first()
            if not fitbit_connection:
                logger.error(f"User tokens not found for Fitbit user: {fitbit_id}")
                return True

            expires_at = pendulum.instance(fitbit_connection.updated_at).add(seconds=fitbit_connection.expires_in)
            access_token = fitbit_connection.access_token
            refresh_token = fitbit_connection.refresh_token
            if expires_at <= pendulum.now():
                user = await self.client.refresh_token(refresh_token)
                error = user.error
                if error:
                    await Fitbit.filter(id=fitbit_id).update(healthy=False)
                    logger.error(f"Error refreshing token: {error}")
                    raise Exception(f"Error fetching Fitbit token for fitbit_id {fitbit_id}: {error}. Terminating processing")
                access_token = user.access_token
                await Fitbit.filter(id=fitbit_id).update(
                    access_token=access_token,
                    refresh_token=user.refresh_token,
                    expires_in=user.expires_in,
                    updated_at=pendulum.now(),
                )
                logger.info(f"Token refreshed for fitbit_id {fitbit_id}")

            await self.client.delete_subscription(
                access_token=access_token
                )

        except FitbitServiceException as exc:
            logger.error(f"revoke notification failed: {exc}")


        await self.member.delete_fitbit()
        logger.info("fitbit device for member: %s disconnected", self.member.id)
        return True

    async def renew_credential(self):
        """Call refresh token and update access and refresh tokens"""
        logger.info("renewing fitbit token for member: %s", self.member.id)
        refresh_token = await self.member.get_fitbit_refresh_token()
        if refresh_token is None:
            raise FitbitServiceException("unauthorized, token is empty")

        token = await self.client.refresh_token(refresh_token)
        if token is None:
            raise FitbitServiceException("refreshing token failed")

        # Validate what userid == memeber.withings.user_id
        # if not await self.member.withings_user_valid(token.get("userid")):
        #     raise Exception("refreshing token failed, user id did not match")

        await self.member.update_fitbit_credentials(
            refresh_token=token.refresh_token,
            access_token=token.access_token,
        )
        logger.info("fitbit token for member: %s renewed", self.member.id)

    async def send_pull_notification(
            self,
            startdate: int,
            enddate: int,
            platforms: list[Platform],
            correlation_id: str,
    ) -> bool:
        """Build and send withings notification to the queue"""
        fitbit_id = await self.member.get_fitbit_id()
        if fitbit_id is None:
            logger.debug(
                "sending notification skipped, Withings device for member: %s not exists",
                self.member.id,
            )
            raise FitbitServiceException(
                "sending notification skipped, device not exists"
            )

        scope = [
            "sleep",
            "activities",
            "body",
            "spo2",
            "heart_rate",
        ]

        try:
            fitbit_payload = FitbitPayload(
                fitbit_id=fitbit_id,
                scope=scope,
                start_date=startdate,
                end_date=enddate,
            )

            notification_payload = PullDataNotification(
                member_id=str(self.member.id),
                activity_device=ActivityDevice.FITBIT,
                platforms=platforms,
                payload=fitbit_payload,
                corellation_id=correlation_id,
            )

            message = notification_payload.model_dump_json()
            logger.info(  # pylint: disable=logging-fstring-interpolation
                f"message for {notification_payload.member_id} ready to sending"
            )
            logger.debug(  # pylint: disable=logging-fstring-interpolation
                f"message: {message}"
            )  # pylint: disable=logging-fstring-interpolation
            queue = get_queue(settings.SQS_DATA_NOTIFICATION_QUEUE)

            await queue.send(message)

        except SendQueueException as exception:
            error_message = "sending notification to the queue failed"
            logger.exception(error_message, exc_info=exception)
            raise FitbitServiceException(error_message) from exception

        return True

    async def sync_device(self, correlation_id: str, start_date: int = 0) -> bool:
        """Subscribe member to notification and
        trigger Lambada to pull data from start_date until now"""
        if self.member is None:
            raise FitbitServiceException("member is None")

        # send message to we queue to pull last data from API
        platforms = await self.member.get_platforms()
        return await self.send_pull_notification(
            startdate=start_date, enddate=int(time.time()), platforms=platforms, correlation_id=correlation_id
        )

    async def handle_subscribe_to_account(
            self,
            state: str = "",
            code: str = "",
            email: str = "",
            site: str = "",
    ) -> dict:
        """Process withings user auth and init subscription to notify"""
        response = {}
        try:
            fitbit_id = await self.subcribe_to_user_devices(
                state=state,
                code=code,
                site=site,
                email=email,
            )
            subscribed = await self.subcribe_to_notification()
            if not subscribed:
                response["error"] = (
                    "account didn't subscribe to the notification"
                )

            response["token"] = fitbit_id
        except Exception as exc:  # pylint: disable=broad-exception-caught
            message = f"subscribe to fitbit account: {email} failed"
            logger.error(message, exc_info=exc)
            response["error"] = message

        return response


    async def __previous_state(self) -> str:
        """
        Returns last state for Member
        """
        member_state = (
            await MemberState.filter(member=self.member)
            .order_by("-created_at")
            .limit(1)
            .first()
        )
        if member_state is None:
            return ""
        return str(member_state.state)


    async def __check_request_state(self, state: str) -> bool:
        """
        Check if handshake state is valid
        """
        previous_state = await self.__previous_state()

        if previous_state != state:
            raise InvalidStateException(previous_state)

        return True

    async def subcribe_to_user_devices(
            self,
            state: str,
            code: str,
            email: str,
            site: str,
    ) -> str:
        """
        Returns a user devices and make event hook subscription.
        """
        logger.debug(  # pylint: disable=logging-fstring-interpolation
            f"Site is {site}"
        )  # pylint: disable=logging-fstring-interpolation

        await self.__check_request_state(state)

        code_verifier = await MemberState.filter(state=state).first().values(
            "code_verifier"
        )
        code_verifier = code_verifier.get("code_verifier")
        tokens = await self.client.get_token(
            code=code,
            state=state,
            site=site,
            code_verifier=code_verifier,
        )
        if "error" in tokens:
            logger.error(
                "fitbit get_token failed code: %s, uri: %s. error: %s",
                code,
                self.client.redirect_uri,
                tokens["error"],
            )
            raise AuthorisationException(
                f"fitbit get_token failed: {tokens['error']}"
            )


        access_token = str(tokens.get("access_token", ""))
        refresh_token = str(tokens.get("refresh_token", ""))
        user_id = tokens.get("user_id")
        expires_in = tokens.get("expires_in")

        try:
            stored_user_device = await Fitbit.filter(user_id=user_id).get_or_none()
            if stored_user_device:
                linked_member_id = stored_user_device.member_id
                if linked_member_id != self.member.id:
                    stored_user_device.member = self.member
                    stored_user_device.access_token = access_token
                    stored_user_device.refresh_token = refresh_token
                    stored_user_device.expires_in = expires_in
                    await stored_user_device.save()
                    return stored_user_device.id
                elif linked_member_id == self.member.id:
                    fitbit_id = await self.member.get_fitbit_id()
                    return fitbit_id
            else:
                fitbit_creds = Fitbit(
                    access_token=access_token,
                    user_id=user_id,
                    expires_in=expires_in,
                    member=self.member,
                    refresh_token=refresh_token,
                )
                await fitbit_creds.save()
                return fitbit_creds.id
        except Exception as exc:
            logger.error("fitbit database error", exc_info=exc)
            raise FitbitServiceException(
                f"store fitbit device for account: {email} failed"
            ) from exc

    async def subcribe_to_notification(self) -> bool:
        """
        Subscribe to fitbit notification
        """
        access_token = await self.member.get_fitbit_access_token()
        if access_token is None:
            raise FitbitServiceException("unauthorized, token is empty")

        try:
            await self.client.delete_subscription(
                access_token=access_token
                )
        except Exception as e:
            logger.info(f"Disconnect before connect device: {e}")
        try:
            await self.client.create_subscription(
                access_token=access_token,
                member_id=str(self.member.id),
                )
        except Exception as exc:
                logger.error(f"create notification failed: {exc}")
                return False

        return True
