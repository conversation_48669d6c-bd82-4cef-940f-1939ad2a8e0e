from uuid import UUID

from ciba_iot_etl.models.db.withings import Withings
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.common.messages import (
    DEVICE_VENDOR_NOT_SUPPORTED,
    DEVICE_CONNECTION_NOT_FOUND,
)
from app.services.data_fetch.withings import WithingsDataFetcher


def get_connection_model(device_vendor: ActivityDevice) -> type[Withings]:
    """
    Method that determines the device connection model to be used for data retrieval.

    Args:
        device_vendor (ActivityDevice): Device vendor
    Returns:
        type[WithingsDataFetcher]: Connection model class
    """
    if device_vendor == ActivityDevice.WITHINGS:
        return Withings

    raise ValueError(DEVICE_VENDOR_NOT_SUPPORTED)


def get_data_fetch_class(
    device_vendor: ActivityDevice,
) -> type[WithingsDataFetcher]:
    """
    Method that determines the data fetch class to be used for data retrieval.

    Args:
        device_vendor (ActivityDevice): Device vendor
    Returns:
        type[WithingsDataFetcher]: Data fetch class
    """
    if device_vendor == ActivityDevice.WITHINGS:
        return WithingsDataFetcher

    raise ValueError(DEVICE_VENDOR_NOT_SUPPORTED)


class DataFetcherFactory:
    """
    Factory class that creates data fetchers based on DataFetchBase
    for different devices vendors.
    """

    @classmethod
    async def create(cls, member_id: UUID, device_vendor: ActivityDevice):
        """
        Method that instantiates a data fetcher based on the provided data.

        Args:
            member_id (UUID): Member id whose data is to be fetched
            device_vendor (ActivityDevice): Device vendor
        """
        connection_model = get_connection_model(device_vendor)
        connection = await connection_model.filter(member_id=member_id).first()

        if not connection:
            raise RuntimeError(DEVICE_CONNECTION_NOT_FOUND)

        fetcher = get_data_fetch_class(ActivityDevice.WITHINGS)

        return fetcher(member_id, connection)
