<!-- templates/user_detail.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>User Detail - {{ user.email }}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
    }
    .device {
      border: 1px solid #ccc;
      padding: 10px;
      margin-bottom: 20px;
    }
    .buttons {
      margin-top: 10px;
    }
    .buttons button {
      margin-right: 10px;
    }
    .date-range {
      margin-top: 10px;
    }
    .date-range label {
      margin-right: 5px;
    }
    .date-range input {
      margin-right: 10px;
    }
    table {
      border-collapse: collapse;
      width: 80%;
      margin-top: 10px;
    }
    table, th, td {
      border: 1px solid #ccc;
    }
    th, td {
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    /* Collapsible CSS */
    .collapsible {
      background-color: #eee;
      color: #444;
      cursor: pointer;
      padding: 10px;
      width: 100%;
      border: none;
      text-align: left;
      outline: none;
      font-size: 16px;
      margin-top: 10px;
    }
    .active, .collapsible:hover {
      background-color: #ccc;
    }
    .content {
      padding: 0 10px;
      display: none;
      overflow: hidden;
    }
  </style>
</head>
<body>
  <h1>User Detail: {{ user.email }}</h1>
  <p><strong>ID:</strong> {{ user.id }}</p>
  <p>
    <a href="/manager/ui/users">Back to User List</a>
  </p>

  <!-- Device Data Section for Withings -->
  {% if user.withings %}
  <div class="device">
    <h2>Withings Device</h2>
    <p><strong>Device ID:</strong> {{ user.withings.id }}</p>
    <p><strong>Account ID:</strong> {{ user.withings.user_id }}</p>
    <p><strong>Updated At:</strong> {{ user.withings.updated_at }}</p>
    <p><strong>Healthy:</strong> {{ user.withings.healthy }}</p>
    <div class="buttons">
      <button onclick="syncDevice('{{ user.email }}', 'withings')">Sync Withings</button>
      <button onclick="disconnectDevice('{{ user.email }}', 'withings')">Disconnect Withings</button>
    </div>
    <div class="date-range">
      <label for="start_date_withings">Start Date:</label>
      <input type="date" id="start_date_withings">
      <label for="end_date_withings">End Date:</label>
      <input type="date" id="end_date_withings">
      <button onclick="getUserData('{{ user.email }}', 'withings')">Get Withings Data</button>
    </div>
    <!-- Container for Withings data table -->
    <div id="data_table_withings"></div>
  </div>
  {% endif %}
  
  <!-- Device Data Section for Fitbit -->
  {% if user.fitbit %}
  <div class="device">
    <h2>Fitbit Device</h2>
    <p><strong>Device ID:</strong> {{ user.fitbit.id }}</p>
    <p><strong>Account ID:</strong> {{ user.fitbit.user_id }}</p>
    <p><strong>Updated At:</strong> {{ user.fitbit.updated_at }}</p>
    <p><strong>Healthy:</strong> {{ user.fitbit.healthy }}</p>
    <div class="buttons">
      <button onclick="syncDevice('{{ user.email }}', 'fitbit')">Sync Fitbit</button>
      <button onclick="disconnectDevice('{{ user.email }}', 'fitbit')">Disconnect Fitbit</button>
    </div>
    <div class="date-range">
      <label for="start_date_fitbit">Start Date:</label>
      <input type="date" id="start_date_fitbit">
      <label for="end_date_fitbit">End Date:</label>
      <input type="date" id="end_date_fitbit">
      <button onclick="getUserData('{{ user.email }}', 'fitbit')">Get Fitbit Data</button>
    </div>
    <!-- Container for Fitbit data table -->
    <div id="data_table_fitbit"></div>
  </div>
  {% endif %}
  
  <!-- Connected Platforms Section -->
  <h2>Connected Platforms</h2>
  <ul>
    {% for platform in user.member_platforms %}
      <li>
        <strong>{{ platform.platform_type }}</strong> - {{ platform.platform_id }}<br>
        <small>Created at: {{ platform.created_at }} | Updated at: {{ platform.updated_at }}</small>
      </li>
    {% endfor %}
  </ul>
  
  <!-- Recent Activities Section -->
  {% if user.activities %}
  <h2>Recent Activities in RPM DB</h2>
  {% for activity_name, records in user.activities.items() %}
    <h3>{{ activity_name }}</h3>
    {% if activity_name == "BloodPressure" %}
      <table>
        <thead>
          <tr>
            <th>Device</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Systolic Value</th>
            <th>Diastolic Value</th>
          </tr>
        </thead>
        <tbody>
          {% for record in records %}
            <tr>
              <td>{{ record.device }}</td>
              <td>{{ record.created_at }}</td>
              <td>{{ record.updated_at }}</td>
              <td>{{ record.systolic_value }}</td>
              <td>{{ record.diastolic_value }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% elif activity_name == "Sleep" %}
      <table>
        <thead>
          <tr>
            <th>Device</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Duration (hours)</th>
            <th>Efficiency</th>
          </tr>
        </thead>
        <tbody>
          {% for record in records %}
            <tr>
              <td>{{ record.device }}</td>
              <td>{{ record.created_at }}</td>
              <td>{{ record.updated_at }}</td>
              <td>{{ (record.duration / 60) | round(2) }}</td>
              <td>{{ record.efficiency }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <table>
        <thead>
          <tr>
            <th>Device</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Unit</th>
            <th>Value</th>
          </tr>
        </thead>
        <tbody>
          {% for record in records %}
            <tr>
              <td>{{ record.device }}</td>
              <td>{{ record.created_at }}</td>
              <td>{{ record.updated_at }}</td>
              <td>{{ record.unit }}</td>
              <td>{{ record.value }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% endif %}
  {% endfor %}
  {% endif %}
  
  
  <script>
    async function syncDevice(email, device) {
      try {
        const response = await fetch(`/manager/ui/users/${email}/sync?device=${device}`, {
          method: 'GET'
        });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        alert(`Sync successful for ${device}: ${JSON.stringify(data)}`);
      } catch (error) {
        console.error('Error syncing device:', error);
        alert(`Error syncing ${device}: ${error}`);
      }
    }
    
    async function disconnectDevice(email, device) {
      try {
        const response = await fetch(`/manager/ui/users/${email}/disconnect?device=${device}`, {
          method: 'GET'
        });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        alert(`Disconnect successful for ${device}: ${JSON.stringify(data)}`);
      } catch (error) {
        console.error('Error disconnecting device:', error);
        alert(`Error disconnecting ${device}: ${error}`);
      }
    }
    
    // Build a simple table from an array of records.
    function buildTable(data) {
      if (!Array.isArray(data)) {
        data = [data];
      }
      if (data.length === 0) {
        return '<table><tr><td>No Data Available</td></tr></table>';
      }
      const keys = Object.keys(data[0]);
      let html = '<table>';
      html += '<thead><tr>';
      keys.forEach(key => {
        html += `<th>${key}</th>`;
      });
      html += '</tr></thead>';
      html += '<tbody>';
      data.forEach(record => {
        html += '<tr>';
        keys.forEach(key => {
          html += `<td>${record[key]}</td>`;
        });
        html += '</tr>';
      });
      html += '</tbody></table>';
      return html;
    }
    
    // Build a collapsible table from an object with sections.
    function buildCollapsibleTable(data) {
      let html = '';
      for (const section in data) {
        if (data.hasOwnProperty(section)) {
          const records = data[section];
          html += `<div class="collapsible-section">
                     <button class="collapsible">${section} (${Array.isArray(records) ? records.length : 0})</button>
                     <div class="content">`;
          if (Array.isArray(records) && records.length > 0) {
            html += buildTable(records);
          } else {
            html += '<p>No data available.</p>';
          }
          html += '</div></div>';
        }
      }
      return html;
    }
    
    async function getUserData(email, device) {
      try {
        let url = `/manager/ui/users/${email}/data/${device}`;
        const startInput = document.getElementById(`start_date_${device.toLowerCase()}`);
        const endInput = document.getElementById(`end_date_${device.toLowerCase()}`);
        const params = [];
        if (startInput && startInput.value) {
          params.push(`start_date=${encodeURIComponent(startInput.value)}`);
        }
        if (endInput && endInput.value) {
          params.push(`end_date=${encodeURIComponent(endInput.value)}`);
        }
        if (params.length > 0) {
          url += '?' + params.join('&');
        }
        const response = await fetch(url, { method: 'GET' });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        let tableHtml = '';
        // If the device is WITHINGS and the data has "weight_measurements",
        // or if the device is FITBIT and the data has "activity_measurements", use collapsible view.
        if ((device.toUpperCase() === 'WITHINGS' && data.hasOwnProperty('weight_measurements')) ||
            (device.toUpperCase() === 'FITBIT' && data.hasOwnProperty('activity_measurements'))) {
          tableHtml = buildCollapsibleTable(data);
        } else {
          tableHtml = buildTable(data);
        }
        document.getElementById(`data_table_${device.toLowerCase()}`).innerHTML = tableHtml;
        
        // Add collapsible toggle functionality.
        const coll = document.getElementsByClassName("collapsible");
        for (const element of coll) {
          element.addEventListener("click", function() {
            this.classList.toggle("active");
            const content = this.nextElementSibling;
            if (content.style.display === "block") {
              content.style.display = "none";
            } else {
              content.style.display = "block";
            }
          });
        }
      } catch (error) {
        console.error('Error getting user data:', error);
        alert(`Error getting data for ${device}: ${error}`);
      }
    }
  </script>
</body>
</html>
