from functools import wraps
from typing import Any, Callable, List, Optional, Tuple

import httpx
from jose import jws, jwt
from redis import StrictRedis
from redis_cache import CacheDecorator, RedisCache, get_cache_lua_fn
from ciba_participant.participant.models import (
    Participant,
    ParticipantStatus,
    SoleraParticipant,
)

from app.auth.base import BaseAuthenticator
from app.auth.constants import PART<PERSON><PERSON>AN<PERSON>, PROVIDER, SOLERA
from app.auth.exceptions import InvalidToken
from app.auth.models import AuthContext
from app.log.logging import logger
from app.settings import get_settings

settings = get_settings()

client = StrictRedis(
    host=settings.REDIS_HOST,
    decode_responses=True,
    port=settings.REDIS_PORT,
    password=settings.REDIS_PASSWORD,
)


class AsyncCacheDecorator(CacheDecorator):
    """Async redis cache decorator."""

    def __call__(self, fn: Callable):  # type: ignore
        # pylint: disable=attribute-defined-outside-init
        self.namespace: str = (
            self.namespace
            if self.namespace
            else f"{fn.__module__}.{fn.__name__}"
        )
        # pylint: disable=attribute-defined-outside-init
        self.keys_key: str = f"{self.prefix}:{self.namespace}:keys"
        self.original_fn = fn  # pylint: disable=attribute-defined-outside-init

        @wraps(fn)
        async def inner(*args: Any, **kwargs: Any) -> Any:
            nonlocal self
            key = self.get_key(args, kwargs)
            try:
                res = self.client.get(key)
            except Exception as ex:  # pylint: disable=broad-except
                logger.warning(
                    "Cannot connect to redis. Call original func. %s", ex
                )
                return await fn(*args, **kwargs)
            if not res:
                res = await fn(*args, **kwargs)
                result_serialized = self.serializer(res)
                get_cache_lua_fn(self.client)(
                    keys=[key, self.keys_key],
                    args=[result_serialized, self.ttl, self.limit],
                )
            else:
                res = self.deserializer(res)
            return res

        inner.invalidate = self.invalidate  # type: ignore
        inner.invalidate_all = self.invalidate_all  # type: ignore
        inner.instance = self  # type: ignore
        return inner  # type: ignore


class AsyncRedisCache(RedisCache):
    def cache(
        self, ttl: int = 0, limit: int = 0, namespace: str | None = None
    ) -> AsyncCacheDecorator:
        """Instantiate cache decorator."""
        logger.info("AsyncRedisCache.cache")
        return AsyncCacheDecorator(
            redis_client=self.client,
            prefix=self.prefix,
            serializer=self.serializer,
            deserializer=self.deserializer,
            key_serializer=self.key_serializer,
            ttl=ttl,
            limit=limit,
            namespace=namespace,
        )


cache = AsyncRedisCache(redis_client=client)

BEARER_PREFIX = "Bearer "
SOLERA_PREFIX = "Bearer solera"


def result(msg: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """Verification result."""
    return (True, None) if msg is None else (False, msg)


class CognitoAuthenticator(BaseAuthenticator):
    """Cognito authenticator class."""

    REGION = settings.COGNITO_AWS_REGION
    APP_CLIENT_ID: str = ""
    POOL_ID: str = ""
    ISSUER: str = PARTICIPANT

    async def authenticate(self, token: str) -> AuthContext:
        if token.startswith(BEARER_PREFIX):
            token = token[len(BEARER_PREFIX) :]
        user_context = self.get_email_from_token(token)
        if not user_context.email:
            raise InvalidToken("Email not found in token")
        if not user_context.email_verified:
            raise InvalidToken("Email is not verified")
        valid, message = await self.validate_jwt(token)
        if not valid:
            raise InvalidToken(f"Token validation failed: {message}")
        return user_context

    def get_issuer(self) -> str:
        """Return the iss of the Cognito User Pool."""
        logger.debug("CognitoAuthenticator.get_issuer")
        return (
            f"https://cognito-idp.{self.REGION}.amazonaws.com/{self.POOL_ID}"
        )

    @staticmethod
    @cache.cache()
    async def get_cognito_jwks(issuer: str) -> List[dict]:
        """Download the JWT Set of the user pool."""
        logger.debug("CognitoAuthenticator.get_cognito_jwks")
        jwt_set_url = f"{issuer}/.well-known/jwks.json"
        try:
            async with httpx.AsyncClient() as request_client:
                jwt_set = await request_client.get(jwt_set_url, timeout=60)
            jwt_set.raise_for_status()
            return jwt_set.json()["keys"]
        except httpx.HTTPStatusError as ex:
            logger.error(
                "Failed to retrieve jwks. Response - %s", ex.response.content
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Failed to download JWT set: %s", ex)
        return []

    async def get_use_keys(self, kid: str) -> List[dict]:
        """Get jwks from cache."""
        logger.debug("CognitoAuthenticator.get_use_keys")
        issuer = self.get_issuer()
        jwks = await self.get_cognito_jwks(issuer)
        use_keys = [key for key in jwks if key["kid"] == kid]
        if not use_keys:
            try:
                self.get_cognito_jwks.invalidate(issuer)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logger.warning("Cannot invalidate jwks. %s", ex)
            jwks = await self.get_cognito_jwks(self.get_issuer())
            use_keys = [key for key in jwks if key["kid"] == kid]
        return use_keys

    async def validate_jwt(self, token: str) -> Tuple[bool, Optional[str]]:
        """Perform the token validation steps."""
        logger.debug("CognitoAuthenticator.validate_jwt")
        try:
            jwt_headers = jwt.get_unverified_header(token)
            kid = jwt_headers.get("kid")
            use_keys = await self.get_use_keys(kid)
            if len(use_keys) != 1:
                return result("Obtained keys are wrong")
            use_key = use_keys[0]
            jwt.decode(
                token,
                use_key,
                issuer=self.get_issuer(),
                options={"verify_at_hash": False, "verify_aud": False},
            )
            jws.verify(token, use_key, jwt_headers["alg"])
        except (jwt.JWTError, jws.JWSError, KeyError) as ex:
            return result(f"Failed to decode or verify token: {ex}")

        claims = jwt.get_unverified_claims(token)
        if claims.get("token_use") not in ["id", "access"]:
            return result(
                f"Token not of valid use - `{claims.get('token_use')}`."
            )
        if "aud" in claims and claims.get(
            "aud"
        ) not in self.APP_CLIENT_ID.split(","):
            return result(f"Token audience not valid - `{claims.get('aud')}`.")

        return result(None)

    def get_email_from_token(self, token: str) -> AuthContext:
        """Get email from token claims."""
        logger.debug("CognitoAuthenticator.get_email_from_token")
        claims = jwt.get_unverified_claims(token)
        use = claims["token_use"]
        if use == "id":
            return AuthContext(
                sub=claims.get("custom:participantNickname")
                or claims.get("sub"),
                email=claims.get("email"),
                auth_time=claims.get("auth_time"),
                email_verified=claims.get("email_verified"),
                admin=claims.get("custom:isAdmin", False),
                issuer=self.ISSUER,
            )
        return AuthContext(
            sub=None,
            email=None,
            email_verified=None,
        )


class SoleraAuthenticator(BaseAuthenticator):
    ISSUER: str = SOLERA
    POOL_ID: str = ""

    # pylint: disable=arguments-renamed
    async def authenticate(  # type: ignore # noqa: F401
        self,  # type: ignore # noqa: F401
        solera_look_up: str,  # type: ignore # noqa: F401
    ) -> Participant:  # type: ignore # noqa: F401
        """Authenticate user.

        :param solera_look_up: Solera look up key
        :return: email
        """
        logger.debug("SoleraAuthenticator.authenticate")
        solera_look_up = [
            el[len(SOLERA_PREFIX) :].strip()
            for el in solera_look_up.split(",")
            if el.startswith(SOLERA_PREFIX)
        ][-1]

        user_context = await self.verify_user(solera_look_up)

        if user_context.participant.email is None:
            logger.warning("Email not found in token")
            raise InvalidToken("Email not found in token")

        if user_context.participant.status is ParticipantStatus.REJECTED:
            raise InvalidToken("Token rejected")

        return user_context.participant

    def get_issuer(self) -> list[str] | str:
        return self.ISSUER

    async def verify_user(self, solera_look_up: str) -> Participant:
        """Verify user."""
        participant = (
            await SoleraParticipant.filter(
                solera_key=solera_look_up,
                status=ParticipantStatus.ACTIVE,
            )
            .prefetch_related("participant")
            .get_or_none()
        )
        if not participant:
            raise InvalidToken("Token not found")
        return participant


class ParticipantAuthenticator(CognitoAuthenticator):
    """Participant authenticator."""

    APP_CLIENT_ID = settings.COGNITO_APP_CLIENT_ID
    POOL_ID = settings.COGNITO_USER_POOL_ID
    ISSUER = PARTICIPANT


class ProviderAuthenticator(CognitoAuthenticator):
    """Provider authenticator."""

    APP_CLIENT_ID = settings.PROVIDER_APP_CLIENT_ID
    POOL_ID = settings.PROVIDER_USER_POOL_ID
    ISSUER = PROVIDER
