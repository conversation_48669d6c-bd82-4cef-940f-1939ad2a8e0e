#################################
######### Platform Team #########
#################################
name: Continuous Delivery
on:
  push:
    paths-ignore:
      - '**.md'
      - '.github/**'
      - 'docs/**'
      - '*.graphql'

    # Deploy to Development
    branches:
      - main
    # Release to Stg
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'
#   release:
#     # Deploy to Production
#     types: [published]

permissions:
  contents: read
  id-token: write

jobs:
  Deploy:
    name: Platform Deployment
    uses: Cibahealth/platform-pipelines/.github/workflows/cd-argo-app-deploy-v2.yml@main
    secrets: inherit
    with:
      github-event: ${{ github.event_name }}
      github-ref: ${{ github.ref }}
      github-sha: ${{ github.sha }}
      app-name: participant-admin-api
