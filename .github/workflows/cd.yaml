#################################
######### Platform Team #########
#################################
name: Continuous Delivery
on:
  push:
    paths-ignore:
      - '**.md'
      - '.github/**'
      - 'docs/**'
      - '*.graphql'

    # Deploy to Development
    branches:
      - main
    # Release to Stg
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'
#   release:
#     # Deploy to Production
#     types: [published]

permissions:
  contents: read
  id-token: write

jobs:
  Runner:
    runs-on: ubuntu-latest
    outputs:
      debug: ${{ steps.set-debug.outputs.debug }}
    steps:
      - name: Enable Ansible Debug
        id: set-debug
        run: echo "debug=${{ runner.debug }}" >> $GITHUB_OUTPUT

  Deploy:
    name: Platform Deployment
    uses: Cibahealth/platform-pipelines/.github/workflows/cd-sam-ansible-v2.yml@main
    needs: Runner
    secrets: inherit
    with:
      github-event: ${{ github.event_name }}
      github-ref: ${{ github.ref }}
      repository-name: ${{ github.repository }}
      ecr-tag: ${{ github.ref_name }}
      debug: ${{ needs.Runner.outputs.debug == 1 && 'true' || 'false' }}
      path-to-lambda: ./push_notifications
