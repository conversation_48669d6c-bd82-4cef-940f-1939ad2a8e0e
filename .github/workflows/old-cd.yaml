name: Continuous Deployment
on:
  workflow_dispatch:
  push:
    paths-ignore:
      - 'docs/**'
      - '.github/**'
    # Release to Dev
    branches:
      - main
    # Release to Stg
    # tags:
    #   - 'v[0-9]+.[0-9]+.[0-9]+'
  release:
    types: [released]

permissions:
  contents: read
  id-token: write
  pull-requests: write

jobs:
  Deploy-Dev:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    uses: Cibahealth/platform-pipelines/.github/workflows/cd-sam-lambda-ch.yml@main
    secrets: inherit
    with:
      github-event: ${{ github.event_name }}
      github-ref: ${{ github.ref }}
      sam-stack-name: ${{ vars.STACK_NAME }}
      path-to-lambda: ${{ vars.PATH_TO_LAMBDA }}
      parameters-list: ${{ vars.DEV_PARAMETERS_LIST }}
      ecr-repo-name: ${{ vars.ECR_REPO_NAME }}
      ecr-tag: ${{ github.sha }}
      docker-cache: false

#   Deploy-Stg:
#     if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
#     uses: Cibahealth/platform-pipelines/.github/workflows/cd-sam-lambda-chgit a
#       parameters-list: ${{ fromJson(vars.CONFIG_ENV_STG).parameters-list }}

  Deploy-Prd:
    if: github.event_name == 'release' && startsWith(github.ref, 'refs/tags/')
    uses: Cibahealth/platform-pipelines/.github/workflows/cd-sam-lambda-ch.yml@main
    secrets: inherit
    with:
      github-event: ${{ github.event_name }}
      github-ref: ${{ github.ref }}
      sam-stack-name: ${{ vars.STACK_NAME }}
      path-to-lambda: ${{ vars.PATH_TO_LAMBDA }}
      parameters-list: ${{ vars.PRD_PARAMETERS_LIST }}
      ecr-repo-name: ${{ vars.ECR_REPO_NAME }}
      ecr-tag: ${{ github.sha }}
      docker-cache: false
