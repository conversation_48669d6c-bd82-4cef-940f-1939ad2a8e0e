name: Build and Release using Poetry

on:
  workflow_dispatch:

permissions: write-all

jobs:
  build-and-upload:
    strategy:
      matrix:
        os: [ubuntu-latest]
        python: ['3.12']
    runs-on: ${{ matrix.os }}
    steps:
    - name: Check out code
      uses: actions/checkout@v2

    - name: Set up Python ${{ matrix.python }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python }}

    - name: Install Poetry
      run: |
        pip install poetry
      shell: bash

    - name: Build wheel using Poetry
      id: get_version
      run: |
        poetry build -f wheel
      shell: bash

    - name: Extract branch name and construct wheel filename
      id: construct_filename
      run: |
        BRANCH_NAME=$(echo ${GITHUB_REF##*/})
        VERSION=$(poetry version --short)
        echo "::set-output name=filename::ciba-iot-etl$BRANCH_NAME-$VERSION-${{ matrix.os }}-${{ matrix.python }}.whl"
        echo "::set-output name=releasename::${filename%.*}"
        echo "::set-output name=version::$VERSION"
        echo "::set-output name=client::$BRANCH_NAME"
      shell: bash

    - name: Rename wheel file
      id: wheel_path
      run: |
        ls -l
        mv dist/*.whl dist/${{ steps.construct_filename.outputs.filename }}
        echo "::set-output name=wheel_path::dist/${{ steps.construct_filename.outputs.filename }}"
      shell: bash
    - name: Run checks to file
      run: |
        poetry install --with dev
#        make lint-all
      shell: bash
    - name: Upload assets to Release
      uses: softprops/action-gh-release@v1
      with:
        discussion_category_name: ${{ steps.construct_filename.outputs.client }}
        tag_name: ${{ steps.construct_filename.outputs.version }}
        name: ${{ steps.construct_filename.outputs.releasename }}
        draft: true
        prerelease: true
        files: |
          ${{ steps.wheel_path.outputs.wheel_path }}
#          ruff.json
#          bandit.json
#          mypy.json
#          pre-commit.txt
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
